# Coverity Violation Feature

## Overview
The Coverity Violation feature allows you to quickly insert Coverity exception comments into your code to suppress specific violations.

## How to Use

1. **Open a source file** in the editor
2. **Position your cursor** on the line where you want to insert the Coverity comment
3. **Right-click** to open the context menu
4. **Select "Coverity Violation"** from the context menu
5. A dialog will open showing available Coverity violation rules
6. **Select a rule** from the list by clicking on it
7. **Click "Insert"** to add the comment before the current line

## Comment Format
The inserted comment follows this format:
```
//coverity[X_Y], ## violation reason SYSSW_V_X.Y_ZZ
```

For example, selecting `SYSSW_V_8.6_01` will insert:
```
//coverity[8_6], ## violation reason SYSSW_V_8.6_01
```

## Rules File
The violation rules are read from `coverity_exception.rules` file in the extension directory. Each line contains:
- **Column 1**: Violation code (e.g., SYSSW_V_8.6_01)
- **Column 2**: Description of the violation
- **Column 3**: Category (e.g., Memory Safety, Security, Concurrency)
- Columns are separated by tabs

## Example Usage
```c
int main() {
    char buffer[100];
    //coverity[8_6], ## violation reason SYSSW_V_8.6_01
    strcpy(buffer, user_input);  // Potential buffer overflow
    return 0;
}
```

## Features
- ✅ Context menu integration
- ✅ Interactive dialog with 3-column rule selection
- ✅ Automatic comment formatting
- ✅ Version number extraction from rule codes
- ✅ Tab-separated 3-column rules file format
- ✅ Extension-based rules storage
- ✅ Categorized violation rules
