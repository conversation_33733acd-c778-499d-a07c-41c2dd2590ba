SYSSW_V_8.6_01	Buffer overflow vulnerability - array bounds check required	Memory Safety
SYSSW_V_8.6_02	Null pointer dereference - add null check before access	Memory Safety
SYSSW_V_8.6_03	Memory leak - ensure proper memory deallocation	Memory Safety
SYSSW_V_8.6_04	Uninitialized variable - initialize before use	Memory Safety
SYSSW_V_8.6_05	Resource leak - close file/socket handles properly	Resource Management
SYSSW_V_9.2_01	Integer overflow - validate input ranges	Data Validation
SYSSW_V_9.2_02	Format string vulnerability - use safe string functions	Input Validation
SYSSW_V_9.2_03	Race condition - add proper synchronization	Concurrency
SYSSW_V_9.2_04	Use after free - avoid accessing freed memory	Memory Safety
SYSSW_V_9.2_05	Double free - check if pointer is already freed	Memory Safety
SYSSW_V_10.1_01	SQL injection - use parameterized queries	Security
SYSSW_V_10.1_02	Cross-site scripting - sanitize user input	Security
SYSSW_V_10.1_03	Path traversal - validate file paths	Security
SYSSW_V_10.1_04	Command injection - validate command parameters	Security
SYSSW_V_10.1_05	Weak cryptography - use strong encryption algorithms	Security
SYSSW_V_11.3_01	Deadlock potential - review lock ordering	Concurrency
SYSSW_V_11.3_02	Stack overflow - limit recursion depth	Memory Safety
SYSSW_V_11.3_03	Heap corruption - validate memory operations	Memory Safety
SYSSW_V_11.3_04	Time-of-check time-of-use - atomic operations needed	Concurrency
SYSSW_V_11.3_05	Privilege escalation - validate user permissions	Security
