2025-07-15 17:00:00
SYSSW_V_2.1_01	不得包含不可达代码(unreachable code)	正常情况下代码中不应该有永不可达的代码(这个例外列举仅对特殊的底层系统代码 - 重启函数软件处理完必要的资源释放后等待硬件重启软件;之后的代码执行不到，也不需要执行，形式上符合函数style要求，有return)
SYSSW_V_2.2_01	Un10	MCU临界区代码采用统一的Schm_XXX的形式，函数的内容由客户通过RTE填充
SYSSW_V_4.12_01	不应使用动态内存分配。	初始化时分配内存
SYSSW_V_4.12_02	不应使用动态内存分配。	只能运行时确定的内存大小
SYSSW_V_5.1_01	vocf	调用第三方的宏生成的长标志符
SYSSW_V_5.4_01	Macro identifiers shall be distinct	宏名称的含义是清晰的(因表述完整含义需要较长的字符，有些模块某些细节特性相关的宏，前罗干字符相同-如31个相同，Coverity会告警)，本身是没问题，可以变更意义不大。
SYSSW_V_5.5_01	宏标识符与其他标识符不得重名	由于历史原因，存在结构体成员与第三方代码宏重名
SYSSW_V_5.7_01	标记名称应是唯一的标识符。	内核或第三方代码中原有定义
SYSSW_V_5.8_01	使用外部链接定义对象或函数的标识符应该唯一。	多个工具一起编译时有个main函数
SYSSW_V_5.8_02	使用外部链接定义对象或函数的标识符应该唯一。	继承的对外接口中的结构成员变量和标准库或第三方库接口重名
SYSSW_V_8.4_01	对象定义不包含一个可见的原型	引用开源代码宏引入的问题
SYSSW_V_8.4_02	对象定义不包含一个可见的原型	定义的全局变量在coverity未扫描的代码中引用。因此不能在定义位置添加static。
SYSSW_V_8.5_01	函数被声明了多次	函数在定义的.c中声明一次，在引用的.c中作extern外部声明。受限于放在.h中只放置autosar接口/存在互相包含的风险，所以没有通过.h声明。
SYSSW_V_8.6_01	函数 "" 已声明，但从未定义。	由汇编代码定义实现的函数
SYSSW_V_8.6_02	全局标识符应在且只在一处定义	在第三方库里由weak申明的函数，重新定义
SYSSW_V_8.6_03	变量只有声明，没有定义	"变量定义在EB工具生成文件中，变量名为工具开发时固定的，文件由用户配置并生成。
驱动中需要引用该变量/通过宏引用该变量，也就是引用EB中用户配置的参数集合。"
SYSSW_V_8.7_01	对象 "" 具有外部链接，但仅用在了一个编译单元中。	"引用开源代码宏引入的问题, 符号由链接脚本以及系统代码使用"
SYSSW_V_8.7_02	对象 "" 具有外部链接，但仅用在了一个编译单元中。	库中的对外接口，本编译单元没有调用。
SYSSW_V_8.7_03	对象 "" 具有外部链接，但仅用在了一个编译单元中。	驱动中EXPORT_SYMBOL的符号，给别的模块调用
SYSSW_V_8.7_04	对象 "" 具有外部链接，但仅用在了一个编译单元中。	文件复用在另外的编译模块单元中，在那个模块中会被别的文件调用，所以不能定义成static.
SYSSW_V_8.7_05	对象 "" 具有外部链接，但仅用在了一个编译单元中。	"驱动中某个.c 定义一个全局变量/函数，该变量/函数在其他文件中引用，但：
(1) 引用位置被宏包起，某些配置情况下宏才会被开启，因此这个全局变量/函数不能定义为static
(2) 函数在OS周期任务或中断处理函数中或用户代码中调用，由用户决定，因此这个函数不能定义为static
(3) 在eb配置回调函数时，eb生成文件中调用"
SYSSW_V_8.9_01	如果对象的标识符只出现在一个函数中，则应该在块范围内定义该对象。	汇编代码中使用的变量
SYSSW_V_8.9_02	如果对象的标识符只出现在一个函数中，则应该在块范围内定义该对象。	内核驱动框架习惯风格要求的结构和变量作为全局变量。
SYSSW_V_8.9_03	如果对象的标识符只出现在一个函数中，则应该在块范围内定义该对象。	较大的结构或数组可以不要求放在函数内部
SYSSW_V_8.10_01	内联函数应该通过静态存储类声明。	内联函数需要在多个文件引用。
SYSSW_V_9.5_01	使用指定的初始化器对数组对象执行初始化时，应显式指定数组的大小。	引用第三方库中的宏引起的，无法在自研代码中修改
SYSSW_V_10.1_01	"Operands shall not be of an inappropriate essential type。
典型情况: 比如 运算符 ""<<"" 或者 ""|"" 的操作数 """" 不具有基本无符号类型"	引用开源代码宏引入的问题（不适合修改外部的文件），且具体值范围合理，分析后并不会导致问题
SYSSW_V_10.1_02	！！操作符，只适用与布尔变量	由于历史原因，需要将32位寄存器值转换为布尔值
SYSSW_V_10.3_01	不应将表达式的值赋值给为较窄的基本类型或不同基本类型类别的对象。（宽转窄）	枚举最后作为整数写入硬件配置寄存器
SYSSW_V_10.3_02	不应将表达式的值赋值给为较窄的基本类型或不同基本类型类别的对象。（宽转窄）	枚举需要比较范围
SYSSW_V_10.3_03	不应将表达式的值赋值给为较窄的基本类型或不同基本类型类别的对象。（宽转窄）	由于历史原因，枚举需要转成整数传递给别的函数或结构
SYSSW_V_10.3_04	不应将表达式的值赋值给为较窄的基本类型或不同基本类型类别的对象。（宽转窄）	从json中解析的参数转为枚举
SYSSW_V_10.3_05	不应将表达式的值赋值给为较窄的基本类型或不同基本类型类别的对象。（宽转窄）	uin32变量位运算并移位后，将最低bit值取出作为枚举类型的返回值
SYSSW_V_10.3_06	不应将表达式的值赋值给为较窄的基本类型或不同基本类型类别的对象。（宽转窄）	由于历史原因，整数需要转换为枚举传递给别的函数或结构
SYSSW_V_10.4_01	左操作数 的基本类型（无符号型）与右操作数 的基本类型（带符号型）不同。	使用optee kernel开源代码中定义的宏(如TEE_SUCCESS等)的告警，不好直接修改开源代码中的宏定义.
SYSSW_V_10.5_01	表达式的值不应(强制)转换为不适当的基本类型	由于历史原因，整数需要转成枚举传递给别的函数或结构
SYSSW_V_10.5_02	表达式的值不应(强制)转换为不适当的基本类型	Boolean值最后作为整数写入硬件配置寄存器
SYSSW_V_10.7_01	"如果将复合表达式用作执行常规算术转换的运算符的一个操作数，则另一个操作数不得具有更宽的基本类型
(窄转宽)"	"时间转换函数里面，用到一些转换到tv_nsec nanoseconds which valid values are [0, 999999999], and uint32_t are enough to keep this value, some platform may define it(tv_nsec) as is long long, convert from uint32_t will not lose info."
SYSSW_V_10.8_01	不应将复合表达式的值转换为不同的基本类型类别或较宽的基本类型。(窄转宽)	由于历史原因，整数需要转成枚举传递给别的函数或结构
SYSSW_V_10.8_02	不应将复合表达式的值转换为不同的基本类型类别或较宽的基本类型。(窄转宽)	由于历史原因，计算需要结合浮点数
SYSSW_V_11.1_01	不得在指向函数的指针和任何其他类型的指针之间进行转换	底层软件【典型Boot阶段的代码】往往需要特殊的函数与地址(基本类型)的转换，包括与第三方提供的结构体和接口的关联，无法完全避免转换（质量通过涉及和测试保障）
SYSSW_V_11.2_01	指向不完整类型的指针不应转换为任何其他类型。	传递指针的接口不需要处理内容，隐藏结构内容，允许最终处理的函数进行转换
SYSSW_V_11.2_02	指向不完整类型的指针不应转换为任何其他类型。	内核或第三方接口定义返回不完整结构指针类型，没有对外暴露完整结构
SYSSW_V_11.4_01	不得在指向对象的指针和整数类型之间进行转换	由于历史原因，和原定的结构或函数之间要进行交互，虽然新的方式更合理
SYSSW_V_11.4_02	不得在指向对象的指针和整数类型之间进行转换	描述设备寄存器地址的变量转换成指针来访问设备硬件寄存器， MCU中用结构描述一组寄存器。
SYSSW_V_11.5_01	指向 void 的指针不应转换为指向对象的指针	内核结构中原有定义为void
SYSSW_V_11.5_02	指向 void 的指针不应转换为指向对象的指针	传入的指针有多种可能性，需要根据其它参数来转换成具体的结构指针
SYSSW_V_11.5_03	指向 void 的指针不应转换为指向对象的指针	内存分配返回void指针转换为对应的结构指针
SYSSW_V_11.5_04	指向 void 的指针不应转换为指向对象的指针	void指针作为基础结构指针，需要转换为具体的结构指针
SYSSW_V_11.6_01	指向 void 的指针不应转换为算术运算类型。	指针高位有特殊含义
SYSSW_V_11.6_02	类型为 "void *" 的表达式 "" 被转换为类型 ""。	引用开源代码宏引入的问题
SYSSW_V_11.6_03	指向 void 的指针不应转换为算术运算类型。	void指针用来保存地址，地址需要转换为整数来赋值给地址相关寄存器。
SYSSW_V_11.6_04	指向 void 的指针不应转换为算术运算类型。	需要判断void指针指向的地址是否对齐
SYSSW_V_11.8_01	指针所指向类型的转换不应移除任何常量或易失性属性	内核框架定义的回调函数，是const *的指针，但自已代码里要修改内容，并确认返回内核后不会再用这个指针。
SYSSW_V_11.8_02	指针所指向类型的转换不应移除任何常量或易失性属性	"第三方框架回调函数定义的参数是const *指针，在函数实现时要第三方函数，要传这个指针，但没有定义成const, 实际上内部并不会修改指针指向内容。"
SYSSW_V_13.6_01	sizeof 运算符的操作数不应包含具有潜在其他作用的任何表达式。	内核宏所包含的sizeof
SYSSW_V_15.1_01	使用 "goto" 语句。	异常处理，跳转到统一的异常返回处理入口，减少重复代码和return.
SYSSW_V_15.1_02	使用 "goto" 语句。	为了减少return和统一返后前处理， 正常路径下向下跳到正常处理退出的标签
SYSSW_V_16.4_01	Switch 语句没有非空 default 子句。	其它情况默认代码流程已经包含，不需要做任何额外处理。
SYSSW_V_17.1_01	不应使用 <stdarg.h> 的功能。	用于log打印记录可允许
SYSSW_V_17.2_01	函数不得直接或间接调用自身(不得使用递归函数)	"递归用法默认不允许。
仅当明确分析了时间复杂度(可在有限确定的时间内退出而非死循环)和空间影响度(深度对stack的影响) AND 最好是其输入数据是范围/长度是有确约束的 - 对时空的影响是预期可控的"
SYSSW_V_18.4_01	不应对指针类型的表达式应用 +、-、+= 和 -= 运算符	按单字节或不定长的buffer指针处理
SYSSW_V_18.4_02	不应对指针类型的表达式应用 +、-、+= 和 -= 运算符	计算结构首地址不得不用
SYSSW_V_18.4_03	不应对指针类型的表达式应用 +、-、+= 和 -= 运算符	调用linux内核宏函数
SYSSW_V_18.5_01	参数的声明类型包含超过两层的指针嵌套	第三方框架定义的回调函数中的申明
SYSSW_V_20.1_01	#include 指令之前只能包含其他预处理器指令或注释。	MCU上通过宏改变include的头文件
SYSSW_V_20.7_01	宏参数扩展成一个表达式，但该参数没有被括号括起来	引用开源代码宏引入的问题
SYSSW_V_20.7_02	宏参数展开产生的表达式应放在括号内	内核结构中原有定义，经分析可能误报
SYSSW_V_20.10_01	使用了预处理运算符 "#" 或者 "##" 。	deubg打印会使用##连接字符串
SYSSW_V_20.10_02	使用了预处理运算符 "#" 或者 "##" 。	pinctrl框架惯例
SYSSW_V_21.3_01	不应使用 <stdlib.h> 的内存分配和重新分配函数	初始化时分配内存
SYSSW_V_21.3_02	不应使用 <stdlib.h> 的内存分配和重新分配函数	只能运行时确定的内存大小
SYSSW_V_21.7_01	不应该调用atoi	C标准库函数
SYSSW_V_21.8_01	不应使用库函数 abort	aarch64架构下 abort通常通过生成一个同步中止异常，终止程序运行，abort 行为确定
SYSSW_V_22.8_01	"在调用 errno-setting-function 之前，应将 errno 的值设置为
零。"	"第三方系统中自带的C库实现, 没有errno的功能"
SYSSW_V_22.9_01	在调用 errno-setting-function 之后，应测试 errno 的值是否为零。	"第三方系统中自带的C库实现, 没有errno的功能"
SYSSW_V_4.7_01	函数 "strtoul" 未提供任何其他 "errno" 是否设置的指示。应测试变量 "errno" 是否为零。	"strtoul为ATF代码中自带的C库实现, 没有errno的功能"
SYSSW_V_VOCF_01	VOCF指标[0-5]	一个结构的很多成员变量同时初始化成相同值，或从另外一个结构变量的成员变量中复制过来
SYSSW_V_VOCF_02	VOCF指标[0-5]	第三方代码原生宏，会有很多相同操作
SYSSW_V_VOCF_03	VOCF指标[0-5]	"(1) MCU驱动由于需要去EB工具转化的代码里拿参数，或者使用AUTOSAR相关数据结构，导致有大量的指针/多级指针引用，包括： 在逻辑判断条件中引用、多级指针引用后赋值、调用函数时传入形参中的引用 (2) 驱动本身构造大型数据结构，集合IP所有 feature，导致多级指针的引用 (3) 为实现AUTOSAR驱动状态机，导致switch case的某个case中再次嵌套switch case，复杂度上升。 服务某个功能的函数，不宜拆分，if判断情况较多，复杂度上升。 (4) 寄存器数值的提取及处理，需要连续的位运算"
SYSSW_V_VOCF_04	VOCF指标[0-5]	结构体的值需要根据输入条件配置成不同的参数。
SYSSW_V_VOCF_05	VOCF指标[0-5]	已经精简的ioctl函数处理函数，无法再拆分
SYSSW_V_VOCF_06	VOCF指标[0-5]	不展开宏，不超标
SYSSW_V_CCM_01	CCM指标[0-15]	处理类型/情况较多，每种情况下需要进行一系列寄存器操作/子函数调用，需连续执行，导致函数行数过多。
SYSSW_V_CCM_02	CCM指标[0-15]	该接口为加内存接口，需要检查项目的数量客观存在，无需修改
SYSSW_V_CCM_03	CCM指标[0-15]	不展开宏，不超标
SYSSW_V_CYCLE_01	CYCLE指标<1	只在debug的时候，手动触发的路径或Debug版本才会触发的路径
SYSSW_V_LEVEL_01	LEVEL指标<=5	不展开宏，不超标
SYSSW_V_STMT_01	度量值高于 HIS 度量标准策略所允许的最大值 100.00	不展开宏，不超标
SYSSW_V_STMT_02	度量值高于 HIS 度量标准策略所允许的最大值 100.00	一些结构体的各个成员初始化占用代码行数比较多(典型是一些是init函数)，这种不增加代码复杂度，如果函数超标是因为这种情况，忽略此指标的违背。
