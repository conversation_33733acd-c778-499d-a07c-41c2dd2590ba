#!/usr/bin/env python3

import os
import sys
from os.path import abspath, exists, join, normpath

class CscopeFileCollector:
    def __init__(self, srcroot, objroot):
        self.srcroot = abspath(srcroot)
        self.objroot = abspath(objroot)
        self.file_map = set()
        self.file_list = []
        self.final_map = set()

    def save_list_to_file(self, filename, file_list):
        with open(filename, 'w') as f:
            for line in file_list:
                f.write(f"{line}\n")
            # Always include autoconf.h
            f.write(f"{join(self.objroot, 'include/generated/autoconf.h')}\n")

    def collect_src(self, fname):
        try:
            with open(fname, 'r') as f:
                for line in f:
                    line = line.replace(')', ' ').strip()
                    for item in line.split():
                        if item.endswith(('.h', '.c', '.S')) and not item.endswith('.mod.c'):
                            self.file_map.add(item)
        except I<PERSON>rror as e:
            print(f"Warning: Failed to read {fname}: {e}", file=sys.stderr)

    def make_cscope_files(self):
        for fname in self.file_map:
            fname = normpath(fname)
            if fname.startswith('/') and fname.startswith(self.srcroot):
                fname = fname[len(self.srcroot) + 1:]
            self.final_map.add(fname)

        for name in self.final_map:
            src_path = join(self.srcroot, name)
            obj_path = join(self.objroot, name)
            if exists(src_path):
                self.file_list.append(src_path)
            elif exists(obj_path) and 'include/config' not in name:
                self.file_list.append(obj_path)

        self.file_list.sort()
        output_file = join(self.srcroot, "cscope.files")
        self.save_list_to_file(output_file, self.file_list)
        print(f"{output_file} is created.")

def main():
    if len(sys.argv) != 3:
        print(f"Usage: {sys.argv[0]} <kernel_src_path> <kernel_obj_path>")
        sys.exit(1)

    srcroot, objroot = sys.argv[1], sys.argv[2]

    if not exists(srcroot):
        sys.exit(f"Error: source root '{srcroot}' doesn't exist")
    if not exists(objroot):
        sys.exit(f"Error: object root '{objroot}' doesn't exist")

    print(f"srcroot: {abspath(srcroot)}")
    print(f"objroot: {abspath(objroot)}")

    collector = CscopeFileCollector(srcroot, objroot)

    for rootdir, _, filelist in os.walk(objroot):
        for fname in filelist:
            if fname.endswith('.cmd'):
                collector.collect_src(join(rootdir, fname))

    missing_syscalls = join(objroot, '.missing-syscalls.d')
    if exists(missing_syscalls):
        collector.collect_src(missing_syscalls)

    collector.make_cscope_files()

if __name__ == '__main__':
    main()

