import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { outputChannel } from './UtilFuns';

interface CoverityRule {
    code: string;
    description: string;
    category: string;
}

/**
 * Show Coverity Violation dialog
 */
export async function showCoverityViolationDialog(context: vscode.ExtensionContext): Promise<void> {
    try {
        // Store the current active editor info before opening the dialog
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            vscode.window.showWarningMessage('No active editor found. Please open a file first.');
            return;
        }

        // Store document URI and cursor position
        const documentUri = activeEditor.document.uri;
        const cursorPosition = activeEditor.selection.active;

        // Create a WebviewPanel beside the current editor
        const panel = vscode.window.createWebviewPanel(
            'coverityViolation',
            'Coverity Violation',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        // Get extension path from context
        const extensionPath = context.extensionPath;

        // Read coverity exception rules from extension folder
        const rulesFilePath = path.join(extensionPath, 'coverity_exception.rules');
        let rules: CoverityRule[] = [];

        try {
            if (fs.existsSync(rulesFilePath)) {
                const content = fs.readFileSync(rulesFilePath, 'utf8');
                rules = parseRulesFile(content);
            } else {
                vscode.window.showWarningMessage('coverity_exception.rules file not found in extension folder');
                return;
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to read coverity_exception.rules: ${error instanceof Error ? error.message : String(error)}`);
            return;
        }

        // Set the HTML content for the webview
        panel.webview.html = getWebviewContent(rules);

        // Handle messages from the webview
        panel.webview.onDidReceiveMessage(async message => {
            switch (message.command) {
                case 'insert':
                    if (message.selectedRule) {
                        await insertCoverityComment(message.selectedRule, documentUri, cursorPosition);
                        panel.dispose();
                    }
                    break;
                case 'cancel':
                    panel.dispose();
                    break;
            }
        });

    } catch (error) {
        outputChannel.appendLine(`Error showing Coverity Violation dialog: ${error instanceof Error ? error.message : String(error)}`);
        vscode.window.showErrorMessage(`Failed to show Coverity Violation dialog: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Parse the rules file content
 */
function parseRulesFile(content: string): CoverityRule[] {
    const rules: CoverityRule[] = [];
    const lines = content.split('\n');

    for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine && !trimmedLine.startsWith('#')) {
            const parts = trimmedLine.split('\t');
            if (parts.length >= 3) {
                rules.push({
                    code: parts[0].trim(),
                    description: parts[1].trim(),
                    category: parts[2].trim()
                });
            }
        }
    }

    return rules;
}

/**
 * Extract version numbers from coverity code (e.g., "SYSSW_V_8.6_01" -> "8_6")
 */
function extractVersionNumbers(code: string): string {
    const match = code.match(/SYSSW_V_(\d+)\.(\d+)_\d+/);
    if (match) {
        return `${match[1]}_${match[2]}`;
    }
    return '';
}

/**
 * Insert coverity comment before current line
 */
async function insertCoverityComment(rule: CoverityRule, documentUri: vscode.Uri, position: vscode.Position): Promise<void> {
    const versionNumbers = extractVersionNumbers(rule.code);

    if (!versionNumbers) {
        vscode.window.showErrorMessage(`Could not extract version numbers from ${rule.code}`);
        return;
    }

    const commentLine = `//coverity[${versionNumbers}], ## violation reason ${rule.code}`;

    try {
        // Open the document and get a fresh editor reference
        const document = await vscode.workspace.openTextDocument(documentUri);
        const editor = await vscode.window.showTextDocument(document);

        // Set cursor to the original position
        editor.selection = new vscode.Selection(position, position);

        await editor.edit(editBuilder => {
            // Insert the comment line before the current line
            const lineStart = new vscode.Position(position.line, 0);
            editBuilder.insert(lineStart, commentLine + '\n');
        });

        vscode.window.showInformationMessage(`Inserted Coverity comment: ${commentLine}`);
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to insert comment: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Generate HTML content for the webview
 */
function getWebviewContent(rules: CoverityRule[]): string {
    const rulesTableRows = rules.map(rule =>
        `<tr>
            <td>${rule.code}</td>
            <td>${rule.description}</td>
            <td>${rule.category}</td>
        </tr>`
    ).join('');

    return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Coverity Violation</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    font-size: var(--vscode-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 20px;
                }

                .container {
                    max-width: 800px;
                    margin: 0 auto;
                }

                h2 {
                    color: var(--vscode-foreground);
                    margin-bottom: 20px;
                }

                .rules-container {
                    border: 1px solid var(--vscode-panel-border);
                    border-radius: 4px;
                    margin-bottom: 20px;
                    max-height: 400px;
                    overflow-y: auto;
                }

                table {
                    width: 100%;
                    border-collapse: collapse;
                }

                th, td {
                    padding: 8px 12px;
                    text-align: left;
                    border-bottom: 1px solid var(--vscode-panel-border);
                }

                th:nth-child(1), td:nth-child(1) {
                    width: 20%;
                }

                th:nth-child(2), td:nth-child(2) {
                    width: 60%;
                }

                th:nth-child(3), td:nth-child(3) {
                    width: 20%;
                }

                th {
                    background-color: var(--vscode-editor-selectionBackground);
                    font-weight: bold;
                    position: sticky;
                    top: 0;
                }

                tr:hover {
                    background-color: var(--vscode-list-hoverBackground);
                    cursor: pointer;
                }

                tr.selected {
                    background-color: var(--vscode-list-activeSelectionBackground);
                    color: var(--vscode-list-activeSelectionForeground);
                }

                .button-container {
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                }

                button {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: var(--vscode-font-size);
                }

                button:hover {
                    background-color: var(--vscode-button-hoverBackground);
                }

                button:disabled {
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                    cursor: not-allowed;
                }

                .cancel-button {
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                }

                .cancel-button:hover {
                    background-color: var(--vscode-button-secondaryHoverBackground);
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h2>Coverity Violation Rules</h2>

                <div class="rules-container">
                    <table id="rulesTable">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Description</th>
                                <th>Category</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${rulesTableRows}
                        </tbody>
                    </table>
                </div>

                <div class="button-container">
                    <button id="insertButton" disabled>Insert</button>
                    <button id="cancelButton" class="cancel-button">Cancel</button>
                </div>
            </div>

            <script>
                const vscode = acquireVsCodeApi();
                let selectedRule = null;

                // Handle row selection
                const tableRows = document.querySelectorAll('#rulesTable tbody tr');
                const insertButton = document.getElementById('insertButton');

                tableRows.forEach(row => {
                    row.addEventListener('click', () => {
                        // Remove previous selection
                        tableRows.forEach(r => r.classList.remove('selected'));

                        // Add selection to clicked row
                        row.classList.add('selected');

                        // Get rule data
                        const cells = row.querySelectorAll('td');
                        selectedRule = {
                            code: cells[0].textContent.trim(),
                            description: cells[1].textContent.trim(),
                            category: cells[2].textContent.trim()
                        };

                        // Enable insert button
                        insertButton.disabled = false;
                    });
                });

                // Handle insert button click
                insertButton.addEventListener('click', () => {
                    if (selectedRule) {
                        vscode.postMessage({
                            command: 'insert',
                            selectedRule: selectedRule
                        });
                    }
                });

                // Handle cancel button click
                document.getElementById('cancelButton').addEventListener('click', () => {
                    vscode.postMessage({
                        command: 'cancel'
                    });
                });
            </script>
        </body>
        </html>
    `;
}

/**
 * Register Coverity Violation command
 */
export function registerCoverityViolationCommand(context: vscode.ExtensionContext): void {
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.coverityViolation', async () => {
            await showCoverityViolationDialog(context);
        })
    );
}
