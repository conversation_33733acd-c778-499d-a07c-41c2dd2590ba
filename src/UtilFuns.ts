import * as vscode from 'vscode';
import { EXTENSION_ID, EXTENSION_NAME } from './globalSettings';
import { ExecOptions } from 'child_process';
import {os_constants, cmd_result} from "./globalSettings";
import * as path from 'path';
import { getDatabasePath, getCommandPath, getWorkspaceRootPath } from './BuildDatabase';
import * as childProcess from 'child_process';


export async function delayMs(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
} 

export function determineScope(document: vscode.TextDocument): vscode.WorkspaceFolder | undefined {
    return vscode.workspace.workspaceFolders?.find(scope => document.uri.fsPath.includes(scope.uri.fsPath));
}

export function getConfiguration(scope: vscode.WorkspaceFolder | null = null): vscode.WorkspaceConfiguration {
    return vscode.workspace.getConfiguration(EXTENSION_ID, scope);
}

export function commandGuard(command: string): boolean {
    if (typeof command !== 'string' || command.trim() === '') {
        vscode.window.showErrorMessage(
            `${EXTENSION_NAME}: The "Command" preference is not set. Please check your configuration.`
        );
        return true;
    }

    return false;
}

const SYMBOL_KINDS2: { [key: string]: vscode.SymbolKind } = {
    class: vscode.SymbolKind.Class,
    const: vscode.SymbolKind.Constant,
    constant: vscode.SymbolKind.Constant,
    constractor: vscode.SymbolKind.Constructor,
    define: vscode.SymbolKind.Constant,
    enum: vscode.SymbolKind.Enum,
    enumConstant: vscode.SymbolKind.EnumMember,
    enumerator: vscode.SymbolKind.EnumMember,
    event: vscode.SymbolKind.Event,
    externvar: vscode.SymbolKind.Variable,
    field: vscode.SymbolKind.Field,
    func: vscode.SymbolKind.Function,
    function: vscode.SymbolKind.Function,
    functionVar: vscode.SymbolKind.Variable,
    globalVar: vscode.SymbolKind.Variable,
    header: vscode.SymbolKind.File,
    ifclass: vscode.SymbolKind.Interface,
    instance: vscode.SymbolKind.Object,
    interface: vscode.SymbolKind.Module,
    key: vscode.SymbolKind.Key,
    library: vscode.SymbolKind.Package,
    local: vscode.SymbolKind.Variable,
    member: vscode.SymbolKind.Property,
    method: vscode.SymbolKind.Method,
    module: vscode.SymbolKind.Module,
    namespace: vscode.SymbolKind.Namespace,
    net: vscode.SymbolKind.Variable,
    nettype: vscode.SymbolKind.Variable,
    package: vscode.SymbolKind.Package,
    parameter: vscode.SymbolKind.Constant,
    port: vscode.SymbolKind.Variable,
    program: vscode.SymbolKind.Module,
    procedure: vscode.SymbolKind.Function,
    property: vscode.SymbolKind.Property,
    protected: vscode.SymbolKind.Variable,
    register: vscode.SymbolKind.Variable,
    RecordField: vscode.SymbolKind.Property,
    signal: vscode.SymbolKind.Variable,
    singletonMethod: vscode.SymbolKind.Method,
    struct: vscode.SymbolKind.Struct,
    submethod: vscode.SymbolKind.Method,
    subprogram: vscode.SymbolKind.Function,
    subroutine: vscode.SymbolKind.Function,
    subroutineDeclaration: vscode.SymbolKind.Function,
    subtype: vscode.SymbolKind.TypeParameter,
    task: vscode.SymbolKind.Function,
    trait: vscode.SymbolKind.Interface,
    type: vscode.SymbolKind.TypeParameter,
    typedef: vscode.SymbolKind.TypeParameter,
    union: vscode.SymbolKind.Struct,
    var: vscode.SymbolKind.Variable,
    variable: vscode.SymbolKind.Variable,
    macro: vscode.SymbolKind.Constant
};
const SYMBOL_KINDS: { [key: string]: vscode.SymbolKind } = {
    'd': vscode.SymbolKind.String,
    'e': vscode.SymbolKind.Enum,
    'f': vscode.SymbolKind.Function,
    'g': vscode.SymbolKind.EnumMember,
    'h': vscode.SymbolKind.File,
    'l': vscode.SymbolKind.Variable,
    'm': vscode.SymbolKind.Field,
    'p': vscode.SymbolKind.Function,
    's': vscode.SymbolKind.Struct,
    't': vscode.SymbolKind.Class,
    'u': vscode.SymbolKind.Struct,
    'v': vscode.SymbolKind.Variable,
    'x': vscode.SymbolKind.Variable,
    'z': vscode.SymbolKind.TypeParameter,
    'L': vscode.SymbolKind.Namespace,
    'D': vscode.SymbolKind.TypeParameter,
};

export let g_showCommand = false;
export function show_command(command: string) {
    const is_show_command = getConfiguration(vscode.workspace.workspaceFolders?.[0]).get("showCommand") as boolean;
    if (g_showCommand) {
        vscode.window.showInformationMessage(command);
    }

};

export async function doCLI(command: string, showMessage: boolean = false): Promise<string> {
    let dir = getWorkspaceRootPath();

    outputChannel.appendLine(`${command}`);
    if (showMessage) {
        show_command(command);
    }
    return new Promise((resolve, reject) => {
        childProcess.exec(command, {
            cwd: dir,
            maxBuffer: 1024 * 1024 * 1024 * 1024 * 10
        }, async (error, stdout, stderr) => {
            if (error) {
                outputChannel.appendLine(`Error: ${error}`);
                reject(stderr);
            } else {
                resolve(stdout);
                outputChannel.appendLine(stdout);
            }
        });
    });
}

function getLastPart(str: string): string {
    const parts = str.split(/[::]/);
    return parts.slice(-1)[0];
}

function getLastTwoPart(str: string): string {
    const parts = str.split(/[::]/);
    return parts.slice(-3)[0];
}

export function definitionToSymbolInformation(definition: string, scope: vscode.WorkspaceFolder): vscode.SymbolInformation {
    
    const [symbol, path, defs, ...fields] = definition.split("\t");
    let spath = path;

    if (process.platform === 'win32') {
        if (path[1] == ':') {
            spath = "/" + path;
        }
    }
    const file = spath.startsWith('/') ? vscode.Uri.parse(spath) : vscode.Uri.joinPath(scope.uri, spath);

    const lineStr = findField(fields, "line:");
    const line = lineStr ? parseInt(lineStr, 10) - 1 : 0;
    const kind = findField(fields, "kind:");
    let container = findField(fields, "class:");
    if (container == undefined) {
        container = findField(fields, "namespace:");
    }
    if (container == undefined) {
        container = findField(fields, "struct:");
    }
    if (container == undefined) {
        container = findField(fields, "enum:");
    }
    if (container == undefined) {
        container = findField(fields, "union:");
    }
    if (container == undefined) {
        container = '';
    }
    // if (kind == 'namespace' || kind == 'member' || kind == 'struct' || kind == 'class' )
    //     container = getLastPart(container);
    // else if(kind == 'function') {
    //     const temp = getLastTwoPart(container);
    //     if (temp != undefined)
    //         container = temp;
    // }

    let symk =  vscode.SymbolKind.Variable;

    if (kind != undefined) {
        symk = SYMBOL_KINDS[kind];
        if (symk == undefined) {
            symk = SYMBOL_KINDS2[kind];
        }
    }

    return new vscode.SymbolInformation(
        symbol,
        symk,
        container || '',
        new vscode.Location(file, new vscode.Position(line, 0))
    );
}

export function relativeToAbsolute(sym: vscode.SymbolInformation, scope: vscode.WorkspaceFolder): vscode.SymbolInformation {
    
    let uri = sym.location.uri;
    if (!sym.location.uri.fsPath.startsWith(scope.uri.fsPath)) 
        uri = vscode.Uri.joinPath(scope.uri, sym.location.uri.fsPath);
    return new vscode.SymbolInformation(
            sym.name,
            sym.kind,
            sym.containerName,
            new vscode.Location(uri, sym.location.range)
        );
}

export function absoluteToRelative(sym: vscode.SymbolInformation, scope: vscode.WorkspaceFolder): vscode.SymbolInformation {
    let uri = sym.location.uri;
    if (uri.fsPath.startsWith(scope.uri.fsPath)) { 
        uri = vscode.Uri.parse(path.relative(scope.uri.fsPath, uri.fsPath));
    }
    return new vscode.SymbolInformation(
        sym.name,
        sym.kind,
        sym.containerName,
        new vscode.Location(uri, sym.location.range)
    );
}

function findField(tags: string[], prefix: string): string | undefined {
    const tag = tags.find(value => value.startsWith(prefix));
    return tag && tag.substring(prefix.length);
}

export const outputChannel = vscode.window.createOutputChannel(EXTENSION_NAME);

interface ExecResult {
    stdout: string;
    stderr: string;
}

type ExecFunction = (command: string, options: ExecOptions) => Promise<ExecResult>;

export function wrapExec(exec: ExecFunction, platform: string = process.platform): (command: string, options: ExecOptions) => Promise<string[]> {
    return async (command: string, options: ExecOptions) => {
        try {
            if (platform === "win32") {
                // Use PowerShell on Windows because Command Prompt does not support single quotes
                options = { ...options, shell: "powershell.exe" };
            }

            outputChannel.appendLine(`${command} ${JSON.stringify(options)}`);

            const { stdout } = await exec(command, options);
            const output = stdout.trim();
            if (command.includes("readtags")) {
                outputChannel.appendLine(output);
            }
            return output ? output.split('\n') : [];
        } catch (error) {
            outputChannel.appendLine((error as Error).message);
            return [];
        }
    };
}


const _spawn = require('child_process').spawn;
const _os = require('os');

export function run_command(cmd:string, args?:string[], option?:Object): Promise<cmd_result> {
    return new Promise((resolve, reject) => {
        const result = {
            success:true,
            code : -1,
            stdout:"",
            stderr:""
        };

        try {
            const child = _spawn(cmd, args, option);

            child.stdout.on("data", (data: Buffer)=>{
                result.stdout += data.toString();
            });

            child.stderr.on("data", (data: Buffer)=>{
                result.stderr += data.toString();
            });

            child.on("close", (code: number)=>{
                result.code = code;
                resolve(result);
            });

            child.on("error", (err: Error)=>{
                result.stderr = err.message;
                result.success = false;
                resolve(result);
            });

        }
        catch (error) {
            result.stderr += error;
            result.success = false;
            resolve(result);
        }
    });
};

export function current_os() :number {
    const os_platform = _os.platform();

    if (os_platform === "linux") {
        return os_constants.OS_LINUX;
    }
    else if (os_platform === "win31") {
        return os_constants.OS_WINDOWS;
    }
    else if (os_platform === "darwin") {
        return os_constants.OS_MAC_OS;
    }

    return os_constants.OS_UNKOWN;
}

function resolve_path(origin_path : string) : string {
    if (origin_path) {
        return origin_path;
    }
    return "";
}

/**
 * Finds the C function name that contains a specific line number in the file.
 * @param cFileContent The entire content of the C file as a string
 * @param lineNumber The line number to check (1-based index)
 * @returns The name of the function containing the line, or null if not in any function
 */
export function findFunctionNameByLine(document: vscode.TextDocument, lineNumber: number): string | null {
    const result = getFunctionNameAndStartLine(document, lineNumber);
    return result ? result.name : null;
}

export function getFunctionCodeByLine(document: vscode.TextDocument, lineNumber: number): {code:string, startLine:number} | null {
    const result = getFunctionNameAndStartLine(document, lineNumber);
    return result ? {code:getFunctionCode(document, result.startLine), startLine:result.startLine}   : null;
}

export function getFunctionNameAndStartLine(document: vscode.TextDocument, lineNumber: number): { name: string; startLine: number } | null {
    const text = document.getText();

    if (lineNumber < 1) {
        return null;
    }

    const lines = text.split('\n');
    if (lineNumber > lines.length) {
        return null;
    }

    // Track current function and its line range
    let currentFunction: { name: string; startLine: number; endLine: number } | null = null;
    const functionStack: Array<{ name: string; startLine: number; endLine: number }> = [];
    
    // Simplified C function pattern (won't handle all edge cases)
    //const functionPattern = /^\s*(?:\w+\s+)*(\w+)\s*$[^)]*$\s*(?:\w+\s*)*\{/;
    //const functionPattern = /\s*(?:[a-zA-Z_]\w*\**\s*)\**\s*([a-zA-Z_][:\w]*)(\s*\(([^)]*)\))\s*\{?\s*$/;
    const functionPattern = /^\s*(?:[\w\*\s]*?)([a-zA-Z_][:\w]*)(\s*\(([^)]*)\))\s*\{?\s*$/;
    const MultilinefunctionPattern = /^\s*(?:[\w\*\s]*?)([a-zA-Z_][:\w]*)(\s*\([^)]*)$/;
    //const MultilinefunctionPattern = /\s*([a-zA-Z_]\w*\**)\s+\**\s*([a-zA-Z_][:\w]*)(\s*\([^)]*)$/;
    const closingBracePattern = /^\s*\}\s*$/;
    
    let braceDepth = 0;
    let functionStartLine = 0;
    let currentFunctionName: string | null = null;
    let function_state  = 0;
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const currentLineNumber = i + 1; // Convert to 1-based index
        
        // Check for function declaration
        if (braceDepth === 0) {
            const functionMatch = line.match(functionPattern);
            if (functionMatch) {
                currentFunctionName = functionMatch[1];
                functionStartLine = currentLineNumber;
                function_state = 1;
            } else {
                const functionMatch = line.match(MultilinefunctionPattern);
                if (functionMatch) {
                    currentFunctionName = functionMatch[1];
                    functionStartLine = currentLineNumber;
                    function_state = 1;
                }
            }
        }
        
        // Track brace depth
        const openBraces = (line.match(/\{/g) || []).length;
        const closeBraces = (line.match(/\}/g) || []).length;
        const is_namespace = (line.match(/namespace/)||[])?.length;
        if (is_namespace > 0) {
            continue;
        }
        braceDepth += openBraces - closeBraces;
        if (function_state == 1 && openBraces > 0) {
            function_state = 2;
        }
        
        // If we just exited a function
        if (braceDepth === 0 && currentFunctionName) {
            if (functionStartLine == currentLineNumber && openBraces == 0) {
                continue;
            }
            if (function_state == 1 && openBraces == 0) {
                continue;
            }
            // Record this function's range
            functionStack.push({
                name: currentFunctionName,
                startLine: functionStartLine,
                endLine: currentLineNumber
            });
            function_state = 0;
            currentFunctionName = null;
        }
    }
    
    // Find the most nested function that contains our line
    for (const func of functionStack) {
        if (lineNumber >= func.startLine && lineNumber <= func.endLine) {
            return { name: func.name, startLine: func.startLine };
        }
    }
    
    return null;
}

// Alternative implementation using cscope/ctags
export async function getFunctionNameFromFileAndLineUsingTags(
    filePath: string,
    lineNumber: number
): Promise<string | undefined> {
    try {
        const { ctagsDbPath } = getDatabasePath();
        const command = getCommandPath("readtags");
        
        // First, get all functions in the file
        const fileRelativePath = path.relative(getWorkspaceRootPath(), filePath);
        let format = process.platform === 'win32' ?
            '(list $name \\" \\" $input \\" \\" $line \\" \\" $kind #t)' :
            '(list $name " " $input " " $line " " $kind #t)';
            
        let data = process.platform === 'win32' ?
            await doCLI(`${command} -t ${ctagsDbPath} -F "${format}" -f "${fileRelativePath}"`) :
            await doCLI(`${command} -t ${ctagsDbPath} -F '${format}' -f "${fileRelativePath}"`);
            
        // Parse the output to get functions
        const lines = data.split('\n').filter(line => line.trim().length > 0);
        const functions = lines
            .map(line => {
                const parts = line.split(/\s+/);
                if (parts.length >= 4) {
                    return {
                        name: parts[0],
                        file: parts[1],
                        line: parseInt(parts[2], 10),
                        kind: parts[3]
                    };
                }
                return null;
            })
            .filter(item => item !== null && 
                (item.kind === 'f' || item.kind === 'p' || item.kind === 'm'))
            .sort((a, b) => (a?.line ?? 0) - (b?.line ?? 0));
            
        // Find the function that contains the line
        // We're looking for the last function that starts before our line
        let bestMatch = null;
        for (const func of functions) {
            if (func?.line && func.line <= lineNumber + 1) { // +1 because ctags is 1-based
                bestMatch = func;
            } else {
                break; // Functions are sorted by line, so we can stop here
            }
        }
        
        return bestMatch?.name;
    } catch (error) {
        outputChannel.appendLine(`Error getting function name using tags: ${error}`);
        return undefined;
    }
}

/**
 * Get the complete function code from a document starting at a specific line
 * @param document The text document containing the function
 * @param startLine The line number where the function starts (1-based)
 * @returns The complete function code as a string, or undefined if not found
 */
export function getFunctionCode(document: vscode.TextDocument, startLine: number): string {
    // Convert to 0-based line number for internal use
    const zeroBasedLine = startLine - 1;
    
    if (zeroBasedLine < 0 || zeroBasedLine >= document.lineCount) {
        return "";
    }
    
    // Find the opening brace
    let openBraceLineIndex = zeroBasedLine;
    let openBraceFound = false;
    let braceCount = 0;
    
    // Look for the opening brace, starting from the function declaration line
    for (let i = zeroBasedLine; i < document.lineCount; i++) {
        const line = document.lineAt(i).text;
        
        // Count opening braces in this line
        for (let j = 0; j < line.length; j++) {
            if (line[j] === '{') {
                if (!openBraceFound) {
                    openBraceLineIndex = i;
                    openBraceFound = true;
                }
                braceCount++;
            } else if (line[j] === '}') {
                braceCount--;
            }
        }
        
        // If we found the opening brace and the braces are balanced, we've reached the end of the function
        if (openBraceFound && braceCount === 0) {
            // Extract the function code from the start line to the current line
            const startPos = new vscode.Position(zeroBasedLine, 0);
            const endPos = new vscode.Position(i, document.lineAt(i).text.length);
            const range = new vscode.Range(startPos, endPos);
            
            return document.getText(range);
        }
    }
    
    // If we couldn't find a balanced closing brace, try to extract what we have
    if (openBraceFound) {
        const startPos = new vscode.Position(zeroBasedLine, 0);
        const endPos = new vscode.Position(document.lineCount - 1, document.lineAt(document.lineCount - 1).text.length);
        const range = new vscode.Range(startPos, endPos);
        
        return document.getText(range);
    }
    
    // If we couldn't find an opening brace, just return the function declaration line
    return document.lineAt(zeroBasedLine).text;
}

/**
 * Finds the struct variable name at a specific line number in the file.
 * @param filePath The path to the C file
 * @param lineNumber The line number to check (1-based index)
 * @returns The name of the struct variable, or null if not found
 */
export async function findStructVariableAtLine(filePath: string, lineNumber: number): Promise<string | null> {
    try {
        const abspath = path.join(getWorkspaceRootPath(), filePath);
        const document = await vscode.workspace.openTextDocument(abspath);
        
        // Look back up to 10 lines to find the struct declaration start
        const startLine = Math.max(0, lineNumber - 30);
        let structName: string | null = null;
        
        // Search backwards from the current line
        for (let i = lineNumber - 1; i >= startLine; i--) {
            const line = document.lineAt(i).text;
            
            // Match struct variable declarations with various patterns
            const structPatterns = [
                // Match struct initialization with member list
                /(?:static\s+)?struct\s+([a-zA-Z_]\w*)\s+([a-zA-Z_]\w*)\s*=\s*{/,
                
                // Match simple struct declarations
                /(?:static\s+)?struct\s+([a-zA-Z_]\w*)\s+([a-zA-Z_]\w*)\s*(?:=\s*{[^}]*})?;/,
                
                // Match struct pointer declarations
                /(?:static\s+)?struct\s+([a-zA-Z_]\w*)\s*\*\s*([a-zA-Z_]\w*)\s*(?:=\s*(?:NULL|nullptr))?;/
            ];

            for (const pattern of structPatterns) {
                const match = line.match(pattern);
                if (match) {
                    // Found a struct declaration, now check if our line is within its scope
                    let braceCount = (line.match(/{/g) || []).length - (line.match(/}/g) || []).length;
                    
                    // If we found an opening brace, check subsequent lines
                    if (braceCount > 0) {
                        for (let j = i + 1; j < Math.min(document.lineCount, i + 20); j++) {
                            const nextLine = document.lineAt(j).text;
                            braceCount += (nextLine.match(/{/g) || []).length;
                            braceCount -= (nextLine.match(/}/g) || []).length;
                            
                            // If we found the closing brace
                            if (braceCount === 0) {
                                // Check if our target line is within this range
                                if (lineNumber - 1 >= i && lineNumber - 1 <= j) {
                                    return match[2]; // Return variable name
                                }
                                break;
                            }
                        }
                    } else if (i === lineNumber - 1) {
                        // For single-line declarations
                        return match[2];
                    }
                }
            }
        }

        return null;
    } catch (error) {
        outputChannel.appendLine(`Error finding struct variable: ${error}`);
        return null;
    }
}

export function filterSymbolInfos(origSymbolInfos: vscode.SymbolInformation[]): vscode.SymbolInformation[] {
    let symbolInfos: vscode.SymbolInformation[] = origSymbolInfos;
    if (origSymbolInfos.length > 1) {
        let filteredSymbolInfos = origSymbolInfos.filter(symbol => symbol.kind === vscode.SymbolKind.Function);
        if (filteredSymbolInfos.length === 1) {
            symbolInfos = filteredSymbolInfos;
        } else if (filteredSymbolInfos.length === 0) {
            filteredSymbolInfos = origSymbolInfos.filter(symbol => symbol.kind === vscode.SymbolKind.Struct
                 || symbol.kind === vscode.SymbolKind.String);
            if (filteredSymbolInfos.length >= 1) {
                symbolInfos = filteredSymbolInfos;
            }
        }
    }
    return symbolInfos;
}

export function getFullPath(filePath: string): string {

    const workspace_path = vscode.workspace.workspaceFolders![0].uri.fsPath;
    let fpath = path.join(workspace_path, filePath);
    let spath = filePath;

    if (spath[1] == ':') {
        const dist  = spath[0].toLowerCase();
        spath = dist + spath.slice(1);
    }
    if (spath.startsWith(workspace_path) || spath.startsWith('/')) {
        // If the path is already absolute or starts with the workspace path, use it directly
        fpath = filePath;
    } else {
        fpath = path.join(workspace_path, spath);
    }
    if (process.platform === 'win32') {
        if (fpath[1] == ':') {
            fpath = "/" + fpath;
        }
    }

    return fpath;
}

export function getRelativePath(filePath: string, scope: vscode.WorkspaceFolder | null): string {
    if (scope) {
        return path.relative(scope.uri.fsPath, filePath);
    } else {
        return filePath;
    }
}
