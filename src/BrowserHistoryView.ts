import * as vscode from 'vscode';
import Browser<PERSON><PERSON><PERSON>, { MyCallHierarchyItem } from './BrowserHistoryStore';
import { g_browserHistory } from './extension';
import * as fs from 'fs';
import * as path from 'path';
import { outputChannel } from './UtilFuns';
import { HistoryEntry } from './BrowserHistoryStore';
import { showBuildDialog} from './BuildDatabase';
const callHierarchyProvider = require("./CallHierarchyProvider");


// 导出视图提供者变量，以便其他模块可以访问
export let g_browserHistoryViewProvider: BrowserHistoryViewProvider | undefined;

export class BrowserHistoryItem extends vscode.TreeItem {
    constructor(
        public readonly item: vscode.CallHierarchyItem,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly entryIndex?: number,
        public readonly parentItem?: BrowserHistoryItem,
        public readonly isCallStack?: boolean,
        public readonly customDescription?: string,
        public readonly itemCount?: number,
        public readonly historyEntry?: HistoryEntry
    ) {
        super(item.name, collapsibleState);
        
        // Extract the file name from the URI path
        const fileName = item.uri.path.split('/').pop() || '';
        this.description = fileName;
        
        // Get the full file path for the tooltip
        let filePath = item.uri.fsPath || item.uri.path;
        
        // Set the tooltip to show more information
        this.tooltip = `${item.name}`;
        
        // Add file path to tooltip
        this.tooltip = `${filePath}`;
        
        // Add line number information
        this.tooltip += `+${item.selectionRange.start.line + 1}`;

        if (item.kind != vscode.SymbolKind.Null && 
            item.kind != vscode.SymbolKind.Function) {
            this.tooltip += ` : ${vscode.SymbolKind[item.kind]}`;
        }

        // Add custom description if available
        if (customDescription) 
            this.tooltip += `\n${customDescription}`;

        // Add call stack indicator to the label if this is a root item
        if (!parentItem && isCallStack) {
            const updown = historyEntry?.isCalling ? 'Calling' : 'Called';
            this.label = `${item.name} [${updown}: ${itemCount}]`;
        } else if (!parentItem && itemCount && itemCount > 1) {
            this.label = `${item.name} [${itemCount}]`;
        } else {
            // Add asterisk (*) before the name if this is a head item
            const myItem = item as MyCallHierarchyItem;
            if (myItem.head) {
                this.label = `* ${item.name}`;
            }
        }
        
        // Set the command to execute when clicking on the item
        this.command = {
            command: 'sourceseek.open',
            arguments: [item.uri, { selection: item.selectionRange }],
            title: 'Go to Definition'
        };
        
        // Set icon based on symbol kind
        this.iconPath = this.getIconForSymbolKind(item.kind);
    }
    
    private getIconForSymbolKind(kind: vscode.SymbolKind): vscode.ThemeIcon {
        switch (kind) {
            case vscode.SymbolKind.Function:
            case vscode.SymbolKind.Method:
                return new vscode.ThemeIcon('symbol-method');
            case vscode.SymbolKind.Class:
                return new vscode.ThemeIcon('symbol-class');
            case vscode.SymbolKind.Interface:
                return new vscode.ThemeIcon('symbol-interface');
            case vscode.SymbolKind.Variable:
                return new vscode.ThemeIcon('symbol-variable');
            case vscode.SymbolKind.Constant:
                return new vscode.ThemeIcon('symbol-constant');
            case vscode.SymbolKind.Property:
                return new vscode.ThemeIcon('symbol-property');
            case vscode.SymbolKind.Enum:
                return new vscode.ThemeIcon('symbol-enum');
            case vscode.SymbolKind.Struct:
                return new vscode.ThemeIcon('symbol-struct');
            default:
                return new vscode.ThemeIcon('symbol-misc');
        }
    }
}

export class BrowserHistoryViewProvider implements vscode.TreeDataProvider<BrowserHistoryItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<BrowserHistoryItem | undefined | null | void> = new vscode.EventEmitter<BrowserHistoryItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<BrowserHistoryItem | undefined | null | void> = this._onDidChangeTreeData.event;
    
    // 添加一个状态变量来跟踪当前显示模式
    private _functionOnlyMode: boolean = false;
    
    // Add drag and drop support
    private _dragAndDropController: BrowserHistoryDragAndDropController;
    
    constructor(private browserHistory: BrowserHistory) {
        this._dragAndDropController = new BrowserHistoryDragAndDropController(this, browserHistory);
    }
    
    // Add getter for the drag and drop controller
    public get dragAndDropController(): BrowserHistoryDragAndDropController {
        return this._dragAndDropController;
    }
    
    // 添加一个方法来切换显示模式
    public toggleFunctionOnlyMode(): void {
        this._functionOnlyMode = !this._functionOnlyMode;

        // Update the command title to reflect the current mode
        vscode.commands.executeCommand(
            'setContext',
            'browserHistoryView.functionOnlyMode',
            this._functionOnlyMode
        );

        this.refresh();
    }
    
    // 获取当前模式
    public get functionOnlyMode(): boolean {
        return this._functionOnlyMode;
    }
    
    // Keep track of all items for parent lookup
    private itemMap = new Map<string, BrowserHistoryItem>();
    
    // Generate a unique ID for each item
    private getItemId(item: BrowserHistoryItem): string {
        return `${item.item.uri.toString()}:${item.item.range.start.line}:${item.item.range.start.character}:${item.entryIndex || 0}`;
    }
    
    refresh(): void {
        this.itemMap.clear(); // Clear the map when refreshing
        this._onDidChangeTreeData.fire();
    }
    
    getTreeItem(element: BrowserHistoryItem): vscode.TreeItem {
        // If this is the first item and we're in function-only mode, add a note to the tooltip
        if (!element.parentItem && this._functionOnlyMode) {
            element.tooltip = `${element.tooltip || element.item.name}\n\nFunction Only Mode is ON`;
        }
        return element;
    }
    
    getChildren(element?: BrowserHistoryItem): Thenable<BrowserHistoryItem[]> {
        if (element) {
            // This is a child item request
            if (element.entryIndex !== undefined) {
                // 获取此索引处的条目
                const entry = this.browserHistory.getEntryByAbsIndex(element.entryIndex);
                
                if (entry && entry.size > 1) {
                    // 返回除第一个之外的所有项（第一个已作为父项显示）
                    const childItems = entry.items.slice(1);
                    
                    // 如果启用了仅函数模式，则过滤非函数符号
                    const filteredItems = this._functionOnlyMode 
                        ? childItems.filter(item => this.isFunctionSymbol(item.kind))
                        : childItems;
                    
                    const children = filteredItems.map((item, index) => {
                        const childItem = new BrowserHistoryItem(
                            item,
                            vscode.TreeItemCollapsibleState.None,
                            index,
                            element,
                            entry.isCallStack,
                            entry.description,
                            entry.size,
                            entry
                        );
                        
                        this.itemMap.set(this.getItemId(childItem), childItem);
                        return childItem;
                    });
                    
                    return Promise.resolve(children);
                }
            }
            return Promise.resolve([]);
        } else {
            // Root level - show the first item of each history entry
            const historyEntries = this.browserHistory.getList();
            
            if (historyEntries.length === 0) {
                // No history yet
                return Promise.resolve([]);
            }
            
            const filteredEntries = this._functionOnlyMode
                ? historyEntries.filter(entry => this.isFunctionSymbol(entry.items[0].kind))
                : historyEntries;
            
            const rootItems = historyEntries.map((entry, index) => {
                // 如果条目有多个项，则使其可折叠
                if (this._functionOnlyMode) {
                    if (!this.isFunctionSymbol(entry.items[0].kind)) {
                        return undefined;
                    }
                }
                const collapsibleState = (entry && entry.size > 1) 
                    ? vscode.TreeItemCollapsibleState.Collapsed 
                    : vscode.TreeItemCollapsibleState.None;
                
                const rootItem = new BrowserHistoryItem(
                    entry.items[0],
                    collapsibleState,
                    this.browserHistory.getAbsIndex(index),
                    undefined,
                    entry.isCallStack,
                    entry.description,
                    entry.items.length,
                    entry
                );
                
                // Store in the map
                this.itemMap.set(this.getItemId(rootItem), rootItem);
                
                return rootItem;
            });
            
            return Promise.resolve(rootItems.filter(item => item !== undefined));
        }
    }
    
    // 检查符号是否为函数类型
    private isFunctionSymbol(kind: vscode.SymbolKind): boolean {
        return kind === vscode.SymbolKind.Function || 
               kind === vscode.SymbolKind.Method || 
               kind === vscode.SymbolKind.Constructor;
    }
    
    // Get the parent of a given item
    getParent(item: BrowserHistoryItem): BrowserHistoryItem | undefined {
        return item.parentItem;
    }
    
    // Find an item by its CallHierarchyItem
    findItemByCallHierarchyItem(callItem: vscode.CallHierarchyItem): BrowserHistoryItem | undefined {
        for (const [_, item] of this.itemMap) {
            if (item.item.uri.toString() === callItem.uri.toString() && 
                item.item.range.isEqual(callItem.range)) {
                return item;
            }
        }
        return undefined;
    }
    
    // Helper method to get an entry by index
    getEntryByIndex(index: number): HistoryEntry | undefined {
        const entrie = this.browserHistory.getEntryByIndex(index);
        return entrie;
    }
    
    // Helper method to get an item by entry index and item index
    getItemByIndices(entryIndex: number, itemIndex: number): vscode.CallHierarchyItem | undefined {
        const entry = this.getEntryByIndex(entryIndex);
        if (entry && itemIndex < entry.size) {
            return entry.items[itemIndex];
        }
        return undefined;
    }

    // 导出历史记录条目到剪贴板
    async exportToClipboard(item: BrowserHistoryItem): Promise<void> {
        try {
            // 查找包含此项的历史记录条目
            const entry = this.browserHistory.findRootParentEntryByItem(item.item);
            
            if (!entry) {
                vscode.window.showErrorMessage('无法找到选定的历史记录条目');
                return;
            }
            
            // 格式化导出内容
            let exportContent = ``;
            let linksymbol = "";
            if (entry.isCalling) {
                linksymbol = "=>";
            } else {
                linksymbol = "<=";
            }
            entry.items.forEach((symbol) => {
                exportContent += `${symbol.name}() ${linksymbol} `;
            });
            exportContent += '\n';
            // 添加每个符号的信息
            /*
            entry.items.forEach((symbol, index) => {
                exportContent += `## ${index + 1}. ${symbol.name}\n`;
                exportContent += `- Type: ${this.getSymbolKindName(symbol.kind)}\n`;
                exportContent += `- File: ${symbol.uri.fsPath}\n`;
                exportContent += `- Line: ${symbol.range.start.line + 1}\n`;
                if (symbol.detail) {
                    exportContent += `- Detail: ${symbol.detail}\n`;
                }
                exportContent += '\n';
            });
            */
            
            // 复制到剪贴板
            await vscode.env.clipboard.writeText(exportContent);
            vscode.window.showInformationMessage(`已将 "${item.item.name}" 的历史记录导出到剪贴板`);
        } catch (error) {
            vscode.window.showErrorMessage(`导出到剪贴板失败: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    
    // 获取符号类型的名称
    private getSymbolKindName(kind: vscode.SymbolKind): string {
        switch (kind) {
            case vscode.SymbolKind.Function: return 'Function';
            case vscode.SymbolKind.Method: return 'Method';
            case vscode.SymbolKind.Class: return 'Class';
            case vscode.SymbolKind.Variable: return 'Variable';
            case vscode.SymbolKind.Interface: return 'Interface';
            case vscode.SymbolKind.Enum: return 'Enum';
            case vscode.SymbolKind.Struct: return 'Struct';
            // ... 其他符号类型 ...
            default: return 'Symbol';
        }
    }

    public reverseOrder(): void {
        this.browserHistory.reverseOrder();
        this.refresh();
    }

    // Add a method to edit the description of a history item
    public async editItemDescription(item: BrowserHistoryItem): Promise<void> {
        try {
            let index = item.entryIndex;
            if (item.parentItem !== undefined) {
                index = item.parentItem.entryIndex;
            }
            if (index === undefined) {
                vscode.window.showErrorMessage('History entry not found');
                return;
            }
            const entry = this.browserHistory.getEntryByAbsIndex(index);
            if (!entry) {
                vscode.window.showErrorMessage('History entry not found');
                return;
            }
            
            // Show input box with current description (if any)
            const currentDescription = entry.description || '';
            const newDescription = await vscode.window.showInputBox({
                prompt: 'Enter a description for this history item',
                value: currentDescription,
                placeHolder: 'Description (optional)'
            });
            
            // If user cancels, newDescription will be undefined
            if (newDescription !== undefined) {
                // Update the description in the history entry
                entry.description = newDescription;
                this.refresh();
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to edit description: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    // Add a method to open a symbol in the right window
    public async openInRightWindow(item: BrowserHistoryItem): Promise<void> {
        try {
            const uri = item.item.uri;
            const range = item.item.selectionRange;
            
            let docuri = uri;
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri;
            if(workspaceRoot && !uri.fsPath.startsWith(workspaceRoot.fsPath)) {
                docuri = vscode.Uri.joinPath(workspaceRoot, uri.fsPath);
            }
            /*
            const definitionDocument = await vscode.workspace.openTextDocument(docuri);
            const editor = await vscode.window.showTextDocument(definitionDocument, {
                    viewColumn: vscode.ViewColumn.Beside,
                    preserveFocus: true // 保持焦点在原窗口
                });
            */ 
            // Open the document in the right editor group (group 2)
            await vscode.commands.executeCommand('vscode.openWith', docuri, 'default', { viewColumn: vscode.ViewColumn.Two });
            // Get the active editor in the right editor group
            const editor = vscode.window.visibleTextEditors.find(
                e => e.viewColumn === vscode.ViewColumn.Two && e.document.uri.toString() === uri.toString()
            );
            
            
            if (editor) {
                // Reveal the range and position the cursor at the beginning of the symbol
                editor.revealRange(range, vscode.TextEditorRevealType.InCenter);
                editor.selection = new vscode.Selection(range.start, range.start);
            } else {
                vscode.window.showErrorMessage('Failed to open editor in right window');
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to open in right window: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}

// Add a new class for handling drag and drop operations
export class BrowserHistoryDragAndDropController implements vscode.TreeDragAndDropController<BrowserHistoryItem> {
    // Define the supported drag mime types
    readonly dropMimeTypes: string[] = ['application/vnd.code.tree.browserHistoryView'];
    readonly dragMimeTypes: string[] = ['application/vnd.code.tree.browserHistoryView'];
    
    constructor(
        private treeDataProvider: BrowserHistoryViewProvider,
        private browserHistory: BrowserHistory
    ) {}
    
    // Handle the drag operation
    handleDrag(source: readonly BrowserHistoryItem[], dataTransfer: vscode.DataTransfer): void {
        // Only allow dragging root items (not children)
        const rootItems = source.filter(item => item.parentItem === undefined);
        if (rootItems.length === 0) {
            return;
        }
        
        // Store the dragged items' entry indices in the data transfer
        const indices = rootItems
            .map(item => item.entryIndex)
            .filter((index): index is number => index !== undefined);
            
        dataTransfer.set('application/vnd.code.tree.browserHistoryView', new vscode.DataTransferItem(indices));
    }
    
    // Handle the drop operation
    async handleDrop(target: BrowserHistoryItem | undefined, dataTransfer: vscode.DataTransfer): Promise<void> {
        const transferItem = dataTransfer.get('application/vnd.code.tree.browserHistoryView');
        if (!transferItem) {
            return;
        }
        
        const sourceIndices: number[] = transferItem.value;
        if (!sourceIndices || !sourceIndices.length) {
            return;
        }
        
        // Get the target index
        let targetIndex: number | undefined;
        
        if (target) {
            // If dropping onto an item, use its index
            targetIndex = target.parentItem ? target.parentItem.entryIndex : target.entryIndex;
        } else {
            // If dropping at the end of the list, use the last index + 1
            targetIndex = this.browserHistory.getSize();
        }
        
        if (targetIndex === undefined) {
            return;
        }
        
        // Move the items to the target position
        this.browserHistory.moveEntries(sourceIndices, targetIndex);
        
        // Refresh the view
        this.treeDataProvider.refresh();
    }
}

// Register the BrowserHistoryView with VS Code
export function registerBrowserHistoryView(context: vscode.ExtensionContext, browserHistory: BrowserHistory): vscode.TreeView<BrowserHistoryItem> {
    // Create the tree view provider
    g_browserHistoryViewProvider = new BrowserHistoryViewProvider(browserHistory);
    
    // Register the tree view with drag and drop support
    const treeView = vscode.window.createTreeView('browserHistoryView', {
        treeDataProvider: g_browserHistoryViewProvider,
        showCollapseAll: true, // Enable collapse all button since we now have a hierarchical view
        dragAndDropController: g_browserHistoryViewProvider.dragAndDropController
    });
    outputChannel.appendLine('registerBrowserHistoryView');
    // Create a status bar item for the function-only mode
    /*
    const functionOnlyStatusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    functionOnlyStatusBarItem.command = 'browserHistoryView.toggleFunctionOnly';
    context.subscriptions.push(functionOnlyStatusBarItem);
    */

    // Function to update the status bar item
    function updateFunctionOnlyStatus() {
        if (g_browserHistoryViewProvider) {
            const mode = g_browserHistoryViewProvider.functionOnlyMode ? 'Function Only' : 'All Symbols';
            /*
            functionOnlyStatusBarItem.text = `$(filter) ${mode}`;
            functionOnlyStatusBarItem.tooltip = `Symbol History: ${mode} mode`;
            functionOnlyStatusBarItem.show();
            */
        }
    }

    // Register a command to toggle function-only mode
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.toggleFunctionOnly', () => {
            if (g_browserHistoryViewProvider) {
                g_browserHistoryViewProvider.toggleFunctionOnlyMode();

                // Update the command title based on the current mode
                const mode = g_browserHistoryViewProvider.functionOnlyMode ? 'ON' : 'OFF';
                vscode.commands.getCommands().then(commands => {
                    if (commands.includes('browserHistoryView.toggleFunctionOnly')) {
                        // This doesn't actually work because command titles are defined in package.json
                        // But we can show a notification instead
                        vscode.window.showInformationMessage(`Function Only Mode: ${mode}`);
                    }
                });
            }
        })
    );

    // Initialize the status bar
    updateFunctionOnlyStatus();

    // Register a command to refresh the view
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.refresh', () => {
            g_browserHistoryViewProvider?.refresh();
        })
    );
    
    // Register a command to clear the history with confirmation
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.clear', async () => {
            // Show confirmation dialog
            const answer = await vscode.window.showWarningMessage(
                'Are you sure you want to clear all symbol history?',
                { modal: true },
                'Yes', 'No'
            );

            if (answer === 'Yes') {
                // Clear the history
                browserHistory.clear();
                g_browserHistoryViewProvider?.refresh();
                vscode.window.showInformationMessage('Symbol history cleared');
            }
        })
    );

    // Register the browserHistoryView.reverseOrder command
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.reverseOrder', () => {
            g_browserHistoryViewProvider?.reverseOrder();
        })
    );

    // Register the browserHistoryView.openSettings command
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.openSettings', () => {
            vscode.commands.executeCommand('workbench.action.openSettings', 'sourceseek');
        })
    );

    // Register the browserHistoryView.buildDatabase command
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.buildDatabase', async () => {
            // Show a progress notification
           await showBuildDialog(); 
        })
    );

    // Register a command to remove an item
    context.subscriptions.push(
        vscode.commands.registerCommand(
            'browserHistoryView.removeItem', (item: BrowserHistoryItem) => {
            // Check if the item is not collapsed and set nccol to true
            let wholetree = false;
            if (item.entryIndex === undefined) {
                return;
            }
            if (item && item.collapsibleState === vscode.TreeItemCollapsibleState.Collapsed) {
                // Ask for confirmation before deleting a collapsed item (which means deleting a tree)
                vscode.window.showWarningMessage(
                    'Are you sure you want to delete this entire group?',
                    { modal: true },
                    'Yes', 'No'
                ).then(answer => {
                    if (answer !== 'Yes' && answer !== 'No') {
                        return;
                    }
                    if (answer === 'Yes') {
                        wholetree = true;
                    }
                    if (item.entryIndex !== undefined) {
                        g_browserHistory.deleteByItemAbs(item.item, item.entryIndex, wholetree);
                        g_browserHistoryViewProvider?.refresh();
                    } 
                    return;
                });
            } else {
                if (item.parentItem == undefined)
                    g_browserHistory.deleteByItemAbs(item.item, item.entryIndex, wholetree);
                else
                    g_browserHistory.deleteByItemAbs(item.item, item.parentItem.entryIndex??0, wholetree);
                g_browserHistoryViewProvider?.refresh();
            }
        })
    );

    // Register a command to export to clipboard
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.exportToClipboard', (item: BrowserHistoryItem) => {
            g_browserHistoryViewProvider?.exportToClipboard(item);
        })
    );

    // Register a command to save history to file
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.saveToFile', async () => {
            try {
                // Get the history data
                const historyEntries = browserHistory.getList();

                if (historyEntries.length === 0) {
                    vscode.window.showInformationMessage('No history to save.');
                    return;
                }

                // Open file save dialog
                const defaultPath = vscode.workspace.workspaceFolders
                    ? path.join(vscode.workspace.workspaceFolders[0].uri.fsPath, 'symbol_history.json')
                    : 'symbol_history.json';

                const fileUri = await vscode.window.showSaveDialog({
                    defaultUri: vscode.Uri.file(defaultPath),
                    filters: {
                        'JSON Files': ['json'],
                        'All Files': ['*']
                    },
                    title: 'Save Symbol History'
                });

                if (!fileUri) {
                    // User cancelled the dialog
                    return;
                }

                // Prepare data for serialization
                const serializableHistory = historyEntries.map(entry => ({
                    size: entry.size,
                    isCallStack: entry.isCallStack,
                    description: entry.description,
                    items: entry.items.map(item => ({
                        name: item.name,
                        kind: item.kind,
                        detail: item.detail || '',
                        uri: item.uri.toString(),
                        range: {
                            start: { line: item.range.start.line, character: item.range.start.character },
                            end: { line: item.range.end.line, character: item.range.end.character }
                        },
                        selectionRange: {
                            start: { line: item.selectionRange.start.line, character: item.selectionRange.start.character },
                            end: { line: item.selectionRange.end.line, character: item.selectionRange.end.character }
                        }
                    }))
                }));

                // Convert to JSON and write to file
                const jsonData = JSON.stringify(serializableHistory, null, 2);
                fs.writeFileSync(fileUri.fsPath, jsonData);

                vscode.window.showInformationMessage(`Symbol history saved to ${fileUri.fsPath}`);
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to save history: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );

    // Register a command to load history from file
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.loadFromFile', async () => {
            outputChannel.appendLine('browserHistoryView.loadFromFile');
            try {
                // Open file open dialog
                const fileUris = await vscode.window.showOpenDialog({
                    canSelectMany: false,
                    filters: {
                        'JSON Files': ['json'],
                        'All Files': ['*']
                    },
                    title: 'Load Symbol History'
                });

                if (!fileUris || fileUris.length === 0) {
                    // User cancelled the dialog
                    return;
                }

                const fileUri = fileUris[0];

                // Read and parse the file
                const fileContent = fs.readFileSync(fileUri.fsPath, 'utf8');
                const loadedData = JSON.parse(fileContent);

                // Validate the data structure
                if (!Array.isArray(loadedData)) {
                    throw new Error('Invalid history file format');
                }

                // Clear current history
                browserHistory.clear();

                // Convert the loaded data back to CallHierarchyItems and add to history
                for (const entryData of loadedData) {
                    const entry: HistoryEntry = {
                        size: entryData.size,
                        items: entryData.items.map((itemData: any) => {
                            const uri = vscode.Uri.parse(itemData.uri);

                            // Create range objects
                            const range = new vscode.Range(
                                new vscode.Position(itemData.range.start.line, itemData.range.start.character),
                                new vscode.Position(itemData.range.end.line, itemData.range.end.character)
                            );

                            const selectionRange = new vscode.Range(
                                new vscode.Position(itemData.selectionRange.start.line, itemData.selectionRange.start.character),
                                new vscode.Position(itemData.selectionRange.end.line, itemData.selectionRange.end.character)
                            );

                            // Create CallHierarchyItem
                            const item = new vscode.CallHierarchyItem(
                                itemData.kind,
                                itemData.name,
                                itemData.detail || '',
                                uri,
                                range,
                                selectionRange
                            );

                            return item;
                        }),
                        isCallStack: entryData.isCallStack,
                        description: entryData.description
                    };
                    browserHistory.addCallHierarchyItem(entry.items[0]);
                }

                // Refresh the view
                g_browserHistoryViewProvider?.refresh();

                vscode.window.showInformationMessage(`Symbol history loaded from ${fileUri.fsPath}`);
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to load history: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );

    // Register a command to edit item description
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.editDescription', (item: BrowserHistoryItem) => {
            g_browserHistoryViewProvider?.editItemDescription(item);
        })
    );

    // Register a command to open in right window
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.openInRightWindow', (item: BrowserHistoryItem) => {
            g_browserHistoryViewProvider?.openInRightWindow(item);
        })
    );

    /**
 * Registers the sourceseek.open command that resolves workspace paths before opening
 * @param context The extension context
 */
    const disposable = vscode.commands.registerCommand('sourceseek.open', async (uri: vscode.Uri, options?: vscode.TextDocumentShowOptions) => {
        try {
            // If the URI is a string, convert it to a URI object
            if (typeof uri === 'string') {
                uri = vscode.Uri.parse(uri);
            }
            
            // If the URI is relative, resolve it against the workspace root
            const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri;
            if (!uri.scheme || uri.scheme === 'file' && !path.isAbsolute(uri.fsPath) ) {
                if (workspaceRoot) {
                    uri = vscode.Uri.joinPath(workspaceRoot, uri.fsPath);
                }
            }
            // Check if the URI path is not already within the workspace root
            if (workspaceRoot && uri.scheme === 'file' && !uri.fsPath.startsWith(workspaceRoot.fsPath)) {
                // Only join with workspace root if it's not already an absolute path that exists
                if (!fs.existsSync(uri.fsPath)) {
                    const joinedPath = vscode.Uri.joinPath(workspaceRoot, uri.fsPath);
                    // Use the joined path if it exists
                    if (fs.existsSync(joinedPath.fsPath)) {
                        uri = joinedPath;
                    }
                }
            }
            
            // Open the document using the standard vscode.open command
            await vscode.commands.executeCommand('vscode.open', uri, options);
            
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to open file: ${error instanceof Error ? error.message : String(error)}`);
        }
    });
    
    context.subscriptions.push(disposable);

    g_browserHistoryViewProvider?.refresh();
    
    // Set the view in your BrowserHistory instance
    browserHistory.view = treeView;
    
    return treeView;
} 