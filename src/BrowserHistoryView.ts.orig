import * as vscode from 'vscode';
import Browser<PERSON>ist<PERSON> from './BrowserHistoryStore';
import { g_browserHistory } from './extension';

// Define the HistoryEntry interface to match BrowserHistoryStore
interface HistoryEntry {
    size: number;
    items: vscode.CallHierarchyItem[];
}

// 导出视图提供者变量，以便其他模块可以访问
export let g_browserHistoryViewProvider: BrowserHistoryViewProvider | undefined;

export class BrowserHistoryItem extends vscode.TreeItem {
    constructor(
        public readonly item: vscode.CallHierarchyItem,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly entryIndex?: number, // Optional index of the parent entry
        public readonly parentItem?: BrowserHistoryItem // Add parent reference
    ) {
        super(item.name, collapsibleState);
        
        // Extract the file name from the URI path
        const fileName = item.uri.path.split('/').pop() || '';
        this.description = fileName;
        
        // Set the tooltip to show more information
        this.tooltip = `${item.name} (${vscode.SymbolKind[item.kind]})`;
        
        // Set the command to execute when clicking on the item
        this.command = {
            command: 'vscode.open',
            arguments: [item.uri, { selection: item.selectionRange }],
            title: 'Go to Definition'
        };
        
        // Set icon based on symbol kind
        this.iconPath = this.getIconForSymbolKind(item.kind);
    }
    
    private getIconForSymbolKind(kind: vscode.SymbolKind): vscode.ThemeIcon {
        switch (kind) {
            case vscode.SymbolKind.Function:
            case vscode.SymbolKind.Method:
                return new vscode.ThemeIcon('symbol-method');
            case vscode.SymbolKind.Class:
                return new vscode.ThemeIcon('symbol-class');
            case vscode.SymbolKind.Interface:
                return new vscode.ThemeIcon('symbol-interface');
            case vscode.SymbolKind.Variable:
                return new vscode.ThemeIcon('symbol-variable');
            case vscode.SymbolKind.Constant:
                return new vscode.ThemeIcon('symbol-constant');
            case vscode.SymbolKind.Property:
                return new vscode.ThemeIcon('symbol-property');
            case vscode.SymbolKind.Enum:
                return new vscode.ThemeIcon('symbol-enum');
            case vscode.SymbolKind.Struct:
                return new vscode.ThemeIcon('symbol-struct');
            default:
                return new vscode.ThemeIcon('symbol-misc');
        }
    }
}

export class BrowserHistoryViewProvider implements vscode.TreeDataProvider<BrowserHistoryItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<BrowserHistoryItem | undefined | null | void> = new vscode.EventEmitter<BrowserHistoryItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<BrowserHistoryItem | undefined | null | void> = this._onDidChangeTreeData.event;
    
    // 添加一个状态变量来跟踪当前显示模式
    private _functionOnlyMode: boolean = false;
    
    constructor(private browserHistory: BrowserHistory) {}
    
    // 添加一个方法来切换显示模式
    public toggleFunctionOnlyMode(): void {
        this._functionOnlyMode = !this._functionOnlyMode;
        this.refresh();
    }
    
    // 获取当前模式
    public get functionOnlyMode(): boolean {
        return this._functionOnlyMode;
    }
    
    // Keep track of all items for parent lookup
    private itemMap = new Map<string, BrowserHistoryItem>();
    
    // Generate a unique ID for each item
    private getItemId(item: BrowserHistoryItem): string {
        return `${item.item.uri.toString()}:${item.item.range.start.line}:${item.item.range.start.character}:${item.entryIndex || 0}`;
    }
    
    refresh(): void {
        this.itemMap.clear(); // Clear the map when refreshing
        this._onDidChangeTreeData.fire();
    }
    
    getTreeItem(element: BrowserHistoryItem): vscode.TreeItem {
        return element;
    }
    
    getChildren(element?: BrowserHistoryItem): Thenable<BrowserHistoryItem[]> {
        if (element) {
            // 这是子项请求 - 检查是否有条目索引
            if (element.entryIndex !== undefined) {
                // 获取此索引处的条目
                const historyEntries = this.browserHistory.getList();
                const entry = historyEntries[element.entryIndex];
                
                if (entry && entry.size > 1) {
                    // 返回除第一个之外的所有项（第一个已作为父项显示）
                    const childItems = entry.items.slice(1);
                    
                    // 如果启用了仅函数模式，则过滤非函数符号
                    const filteredItems = this._functionOnlyMode 
                        ? childItems.filter(item => this.isFunctionSymbol(item.kind))
                        : childItems;
                    
                    const children = filteredItems.map(item => {
                        const childItem = new BrowserHistoryItem(
                            item,
                            vscode.TreeItemCollapsibleState.None,
                            undefined, // 子项没有条目索引
                            element // 设置父引用
                        );
                        
                        // 存储在映射中
                        this.itemMap.set(this.getItemId(childItem), childItem);
                        
                        return childItem;
                    });
                    
                    return Promise.resolve(children);
                }
            }
            // 没有子项或无效条目
            return Promise.resolve([]);
        } else {
            // Root level - show the first item of each history entry
            const historyEntries = this.browserHistory.getList();
            
            if (historyEntries.length === 0) {
                // No history yet
                return Promise.resolve([]);
            }
            
            // 如果启用了仅函数模式，则过滤非函数符号的条目
            const filteredEntries = this._functionOnlyMode
                ? historyEntries.filter(entry => this.isFunctionSymbol(entry.items[0].kind))
                : historyEntries;
            
            // 将每个条目的第一项转换为 BrowserHistoryItems
            const rootItems = filteredEntries.map((entry, index) => {
                // 如果条目有多个项，则使其可折叠
                const collapsibleState = entry.size > 1 
                    ? vscode.TreeItemCollapsibleState.Collapsed 
                    : vscode.TreeItemCollapsibleState.None;
                
                // 使用第一项作为父项
                const rootItem = new BrowserHistoryItem(
                    entry.items[0],
                    collapsibleState,
                    index // Store the entry index for later retrieval
                );
                
                // Store in the map
                this.itemMap.set(this.getItemId(rootItem), rootItem);
                
                return rootItem;
            });
            
            return Promise.resolve(rootItems);
        }
    }
    
    // 检查符号是否为函数类型
    private isFunctionSymbol(kind: vscode.SymbolKind): boolean {
        return kind === vscode.SymbolKind.Function || 
               kind === vscode.SymbolKind.Method || 
               kind === vscode.SymbolKind.Constructor;
    }
    
    // Get the parent of a given item
    getParent(item: BrowserHistoryItem): BrowserHistoryItem | undefined {
        return item.parentItem;
    }
    
    // Find an item by its CallHierarchyItem
    findItemByCallHierarchyItem(callItem: vscode.CallHierarchyItem): BrowserHistoryItem | undefined {
        for (const [_, item] of this.itemMap) {
            if (item.item.uri.toString() === callItem.uri.toString() && 
                item.item.range.isEqual(callItem.range)) {
                return item;
            }
        }
        return undefined;
    }
    
    // Helper method to get an entry by index
    getEntryByIndex(index: number): HistoryEntry | undefined {
        const entries = this.browserHistory.getList();
        return entries[index];
    }
    
    // Helper method to get an item by entry index and item index
    getItemByIndices(entryIndex: number, itemIndex: number): vscode.CallHierarchyItem | undefined {
        const entry = this.getEntryByIndex(entryIndex);
        if (entry && itemIndex < entry.size) {
            return entry.items[itemIndex];
        }
        return undefined;
    }

    // 导出历史记录条目到剪贴板
    async exportToClipboard(item: BrowserHistoryItem): Promise<void> {
        try {
            // 查找包含此项的历史记录条目
            const entry = this.browserHistory.findRootParentEntryByItem(item.item);
            
            if (!entry) {
                vscode.window.showErrorMessage('无法找到选定的历史记录条目');
                return;
            }
            
            // 格式化导出内容
            let exportContent = ``;
            
            entry.items.forEach((symbol) => {
                exportContent += `${symbol.name}<-`;
            });
            exportContent += '\n';
            // 添加每个符号的信息
            /*
            entry.items.forEach((symbol, index) => {
                exportContent += `## ${index + 1}. ${symbol.name}\n`;
                exportContent += `- Type: ${this.getSymbolKindName(symbol.kind)}\n`;
                exportContent += `- File: ${symbol.uri.fsPath}\n`;
                exportContent += `- Line: ${symbol.range.start.line + 1}\n`;
                if (symbol.detail) {
                    exportContent += `- Detail: ${symbol.detail}\n`;
                }
                exportContent += '\n';
            });
            */
            
            // 复制到剪贴板
            await vscode.env.clipboard.writeText(exportContent);
            vscode.window.showInformationMessage(`已将 "${item.item.name}" 的历史记录导出到剪贴板`);
        } catch (error) {
            vscode.window.showErrorMessage(`导出到剪贴板失败: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    
    // 获取符号类型的名称
    private getSymbolKindName(kind: vscode.SymbolKind): string {
        switch (kind) {
            case vscode.SymbolKind.Function: return 'Function';
            case vscode.SymbolKind.Method: return 'Method';
            case vscode.SymbolKind.Class: return 'Class';
            case vscode.SymbolKind.Variable: return 'Variable';
            case vscode.SymbolKind.Interface: return 'Interface';
            case vscode.SymbolKind.Enum: return 'Enum';
            case vscode.SymbolKind.Struct: return 'Struct';
            // ... 其他符号类型 ...
            default: return 'Symbol';
        }
    }
}

// Register the BrowserHistoryView with VS Code
export function registerBrowserHistoryView(context: vscode.ExtensionContext, browserHistory: BrowserHistory): vscode.TreeView<BrowserHistoryItem> {
    // Create the tree view provider
    g_browserHistoryViewProvider = new BrowserHistoryViewProvider(browserHistory);
    
    // Register the tree view
    const treeView = vscode.window.createTreeView('browserHistoryView', {
        treeDataProvider: g_browserHistoryViewProvider,
        showCollapseAll: true // Enable collapse all button since we now have a hierarchical view
    });
    
    // Register a command to refresh the view
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.refresh', () => {
            g_browserHistoryViewProvider?.refresh();
        })
    );
    
    // Register a command to clear the history
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.clear', () => {
            // Clear the history (you'll need to add a clear method to BrowserHistory)
            // browserHistory.clear();
            g_browserHistoryViewProvider?.refresh();
        })
    );
    
    // Register a command to remove an item
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.removeItem', (item: BrowserHistoryItem) => {
            g_browserHistory.deleteByItem(item.item);
            g_browserHistoryViewProvider?.refresh();
        })
    );

    // Register a command to export to clipboard
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.exportToClipboard', (item: BrowserHistoryItem) => {
            g_browserHistoryViewProvider?.exportToClipboard(item);
        })
    );
/*
    // 创建状态栏项目
    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right);
    statusBarItem.command = 'browserHistoryView.toggleFunctionOnly';
    updateStatusBar();

    // 更新状态栏的函数
    function updateStatusBar() {
        if (g_browserHistoryViewProvider) {
            const mode = g_browserHistoryViewProvider.functionOnlyMode ? 'Function Only' : 'All Symbols';
            statusBarItem.text = `$(symbol-method) ${mode}`;
            statusBarItem.tooltip = `Symbol History: ${mode} mode`;
            statusBarItem.show();
        }
    }

    // 在切换模式时更新状态栏
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.toggleFunctionOnly', () => {
            if (g_browserHistoryViewProvider) {
                g_browserHistoryViewProvider.toggleFunctionOnlyMode();
                updateStatusBar();
            }
        })
    );

    // 注册状态栏项目以便清理
    context.subscriptions.push(statusBarItem);
*/

    g_browserHistoryViewProvider?.refresh();
    
    return treeView;
} 