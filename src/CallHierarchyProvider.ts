import * as fs from 'fs';
import * as vscode from 'vscode';
import * as childProcess from 'child_process';
import * as path from 'path';
import { g_browserHistoryViewProvider } from './BrowserHistoryView';
import { g_browserHistory } from './extension';
import { CallTree } from './CallTree';
import { EXTENSION_ID, set_context, cscopeFindDefineCommand, cscopeFindCalleeCommand, cscopeFindCallerCommand, cscopeFindTextCommand, readtagsGoToDefinitionCommand } from './globalSettings';
import { g_cscopeupdate } from './globalSettings';
import { findStructVariableAtLine, outputChannel, doCLI, getFullPath, findFunctionNameByLine } from './UtilFuns';
import { g_functionPointerMap } from './extension';
import { g_checkSymbolKind, g_callhierarchyAddHistory, g_hoverCallTrigger, setHoverCallTrigger } from './SymbolPreviewView';
import { buildDatabase, getCommandPath, getDatabasePath, getWorkspaceRootPath, DatabaseType } from './BuildDatabase';
import { forEach, get } from 'lodash';

let ctag_message_show = false;

let CSCOPE_PATH = 'cscope';
let CTAGS_PATH = 'ctags';
let READTAGS_PATH = 'readtags';

let g_findAssignment = false;
let g_findGrep = false;
let g_findInclude = false;

export function setFindAssignment(setflag: boolean = true) : void {
    g_findAssignment = setflag;
}

export function setFindGrep(setflag: boolean = true) : void {
    g_findGrep = setflag;
}

export function setFindInclude(setflag: boolean = true) : void {
    g_findInclude = setflag;
}

const symbols: { [key: string]: vscode.SymbolKind } = {
    'd': vscode.SymbolKind.String,
    'e': vscode.SymbolKind.Enum,
    'f': vscode.SymbolKind.Function,
    'g': vscode.SymbolKind.EnumMember,
    'h': vscode.SymbolKind.File,
    'l': vscode.SymbolKind.Variable,
    'm': vscode.SymbolKind.Field,
    'p': vscode.SymbolKind.Function,
    's': vscode.SymbolKind.Struct,
    't': vscode.SymbolKind.Class,
    'u': vscode.SymbolKind.Struct,
    'v': vscode.SymbolKind.Variable,
    'x': vscode.SymbolKind.Variable,
    'z': vscode.SymbolKind.TypeParameter,
    'L': vscode.SymbolKind.Namespace,
    'D': vscode.SymbolKind.TypeParameter,
};

export function getCSCOPE_PATH(): string {
    return CSCOPE_PATH;
}

export function getCTAGS_PATH(): string {
    return CTAGS_PATH;
}

export function getREADTAGS_PATH(): string {
    return READTAGS_PATH;
}

export function setCSCOPE_PATH(path: string): void {
    CSCOPE_PATH = path;
}

export function setCTAGS_PATH(path: string): void {
    CTAGS_PATH = path;
}

export function setREADTAGS_PATH(path: string): void {
    READTAGS_PATH = path;
}

export enum ClickJumpLocation {
    SymbolDefinition = "Symbol Definition",
    SymbolCall = "Symbol Call"
}

export enum LogLevel {
    INFO = 0,
    WARN = 1,
    ERROR = 2
}


export class SymbolInfo {
    name: string;
    filePath: string;
    linePosition: number;
    description: string;

    constructor(name?: string, filePath?: string, position?: number, description?: string) {
        this.name = name ?? '';
        this.filePath = filePath ?? '';
        this.linePosition = position ?? -1;
        this.description = description ?? '';
    }

    toString = (): string => `SymbolInfo(${this.name}, ${this.description}, ${this.filePath}, ${this.linePosition})`;

    static convertToFuncInfo(line: string): SymbolInfo {
        let words = line.split(/\s+/);
        return new SymbolInfo(words[1], words[0], Number(words[2]));
    }

    static async convertToFuncInfoCpp(line: string): Promise<SymbolInfo> {
        let words = line.split(/\s+/);
        const path = getFullPath(words[0]);
        const doc = await vscode.workspace.openTextDocument(path);
        let parent = findFunctionNameByLine(doc, Number(words[2]) - 1);
        parent = parent ? parent : words[1];
        return new SymbolInfo(parent, words[0], Number(words[2]));
    }

    static convertToFuncRefInfo(line: string): SymbolInfo {
        let words = line.split(/\s+/);
        let statment = words.slice(3).join(' ');
        return new SymbolInfo(words[1], words[0], Number(words[2]), statment);
    }

    static convertToSymbolInfo(line: string): SymbolInfo {
        let words = line.split(/\s+/);
        let name = words[0].split(/[\\/]/).slice(-1)[0];
        return new SymbolInfo(name, words[0], Number(words[2]));
    }

    getFileName(): string {
        let folders = this.filePath.split(/[\\/]/);
        return folders.slice(-1)[0];
    }
}



function getCscopeCommand(command: string, cscopeDbPath: string, relative: string): string {
    const update_option = g_cscopeupdate ? "": "-d";
    return `${getCommandPath(command)} ${update_option} -f ${cscopeDbPath} -L0 ${relative}`;
}

export class CCallHierarchyItem extends vscode.CallHierarchyItem {
    constructor(
        kind: vscode.SymbolKind,
        name: string,
        detail: string,
        uri: vscode.Uri,
        range: vscode.Range,
        selectionRange: vscode.Range,
        public isIncludeItem: boolean
    ) {
        super(kind, name, detail, uri, range, selectionRange);
    }
}

interface SymbolInfoResult {
    description: string;
    filePath: vscode.Uri;
    symbolRange: vscode.Range;
}

export class CCallHierarchyProvider {
    private cwd: string;
    private calltree: CallTree;
    private context:vscode.ExtensionContext;

    constructor(context: vscode.ExtensionContext) {
        this.cwd = getWorkspaceRootPath();
        this.calltree = new CallTree();
        this.context = context;
        set_context(context);
    }


    async prepareCallHierarchy(
        document: vscode.TextDocument,
        position: vscode.Position,
        token: vscode.CancellationToken
    ): Promise<CCallHierarchyItem | undefined> {
 
        if (! await checkDatabase(DatabaseType.CSCOPE))
            return undefined;

        let text = document.lineAt(position.line).text;
        let regex = /#include\s*[<"]?(?<fileName>[\w\/]+.h)[">]?\s*/;
        let item: CCallHierarchyItem | undefined;

        if (regex.test(text)) {
            let match = regex.exec(text);
            let fileName = match!.groups!.fileName;
            item = new CCallHierarchyItem(
                vscode.SymbolKind.File,
                fileName,
                `@ ${(position.line + 1).toString()}`,
                document.uri,
                new vscode.Range(
                    new vscode.Position(position.line, match!.index),
                    new vscode.Position(position.line, text.length)
                ),
                new vscode.Range(
                    new vscode.Position(position.line, match!.index),
                    new vscode.Position(position.line, text.length)
                ),
                true
            );
        } else {
            let wordRange = document.getWordRangeAtPosition(position);
            if (wordRange !== undefined) {
                let symbol = new SymbolInfo(
                    document.getText(wordRange),
                    document.fileName.replace(this.cwd, '').replace(/[\\/]+/, ''),
                    position.line + 1
                );
                let { description, filePath, symbolRange } = await this.getSymbolInfo(symbol, symbol.name, wordRange);
                let kind = await getSymbolKind(symbol.name, true);
                item = new CCallHierarchyItem(
                    kind,
                    symbol.name,
                    description,
                    filePath,
                    symbolRange,
                    symbolRange,
                    false
                );
            }
        }

        return item;
    }

    async provideCallHierarchyIncomingCalls(
        item: CCallHierarchyItem,
        token: vscode.CancellationToken
    ): Promise<vscode.CallHierarchyIncomingCall[]> {
        let incomingCalls: vscode.CallHierarchyIncomingCall[] = [];
        let children: vscode.CallHierarchyItem[] = [];

        outputChannel.appendLine(`provideCallHierarchyIncomingCalls: ${item.name}`);
        if (item.isIncludeItem) {
            let includers = await findIncluders(item.name);
            let count = 0
            for (let includer of includers) {

                //let path = `${this.cwd}/${includer.filePath}`;
                const path = getFullPath(includer.filePath);
                let symbolRange = await this.getWordRange(
                    path,
                    includer.linePosition - 1,
                    item.name
                );
                let filePath = vscode.Uri.file(path);
                let description = `@ ${includer.linePosition.toString()}`;
                let fromCaller = new CCallHierarchyItem(
                    vscode.SymbolKind.File,
                    includer.name,
                    description,
                    filePath,
                    symbolRange,
                    symbolRange,
                    true
                );
                incomingCalls.push(new vscode.CallHierarchyIncomingCall(fromCaller, [symbolRange]));
                count++;
                if (count > 20) {
                    outputChannel.appendLine(`provideCallHierarchyIncomingCalls: too many includers, limit to 20`);
                    break;
                }
            }
        } else {
            let do_ref = false;
            let kind = item.kind;
            let callers: SymbolInfo[] = [];
            let doCheckSymbolKind = false;
            const activeEditor = vscode.window.activeTextEditor;
            let isCpp = false;

            if (activeEditor) {
                const fileName = activeEditor.document.fileName.toLowerCase();
                isCpp = fileName.endsWith('.cpp') || fileName.endsWith('.cc') ||
                    fileName.endsWith('.cxx') || fileName.endsWith('.hpp') ||
                    fileName.endsWith('.h++');
            }

            if (item.kind === vscode.SymbolKind.Function || item.kind === vscode.SymbolKind.Method || item.kind == vscode.SymbolKind.Field) {
                callers = await findCallers(item.name, isCpp);
                kind = vscode.SymbolKind.Function;
                if (callers.length === 0) 
                    do_ref = true;
            } else {
                do_ref = true;
                doCheckSymbolKind = true;
            }
            if (do_ref) {
                callers = await findFunctionReferences(item.name, isCpp);
                callers = callers.filter((caller) => caller.name != item.name)
                kind = vscode.SymbolKind.Variable;
            }
            let max_caller = 0; 
            for (let callerItem of callers) {
                let { description, filePath, symbolRange } = await this.getSymbolInfo(callerItem, item.name);
                
                if (g_checkSymbolKind || doCheckSymbolKind) {   
                    kind = await getSymbolKind(callerItem.name, false);
                } 
                let fromCaller = new CCallHierarchyItem(
                    kind,
                    callerItem.name,
                    description,
                    filePath,
                    symbolRange,
                    symbolRange,
                    false
                );
                children.push(fromCaller);
                incomingCalls.push(new vscode.CallHierarchyIncomingCall(fromCaller, [symbolRange]));
                max_caller++;
                if (max_caller > 20)
                    break;
            }
            if (g_callhierarchyAddHistory && !g_hoverCallTrigger) {
                this.calltree.add(item, children);
                // Add the current item to browser history
                if (g_browserHistory) {
                    // Find the root parent and immediate parent
                    const root_parent = this.calltree.findRootSymbol(item.name);
                    const parent_symbol = this.calltree.findParentSymbol(item.name);

                    if (root_parent) {
                        // Add to history with parent information
                        g_browserHistory.addCallStackItem(root_parent, item, parent_symbol);
                    }
                    g_browserHistoryViewProvider?.refresh();
                }
            }
            if (g_hoverCallTrigger)
                setHoverCallTrigger(false);
        }

        return incomingCalls;
    }

    async provideCallHierarchyOutgoingCalls(
        item: CCallHierarchyItem,
        token: vscode.CancellationToken
    ): Promise<vscode.CallHierarchyOutgoingCall[]> {
        const { cscopesDbPath } = getDatabasePath();
        let outgoingCalls: vscode.CallHierarchyOutgoingCall[] = [];

        console.log(`provideCallHierarchyOutgoingCalls: ${item.name}`);
        vscode.window.showInformationMessage(`provideCallHierarchyOutgoingCalls: ${item.name}`);

        if (item.isIncludeItem) {
            const filePath = (await doCLI(`${CSCOPE_PATH} -d -f ${cscopesDbPath} -L7 ${item.name}`)).split(/\s+/)[0];
            const document = await vscode.workspace.openTextDocument(`${this.cwd}/${filePath}`);

            for (let lineNumber = 0; lineNumber < document.lineCount; lineNumber++) {
                const line = document.lineAt(lineNumber).text;
                let regex = /#include\s*[<"]?(?<fileName>\w+.h)[">]?\s*/;
                if (regex.test(line)) {
                    let match = regex.exec(line);
                    let fileName = match!.groups!.fileName;
                    let symbolRange = new vscode.Range(
                        new vscode.Position(lineNumber, match!.index),
                        new vscode.Position(lineNumber, line.length)
                    );
                    let toCallee = new CCallHierarchyItem(
                        vscode.SymbolKind.File,
                        fileName,
                        `@ ${lineNumber.toString()}`,
                        document.uri,
                        symbolRange,
                        symbolRange,
                        true
                    );
                    outgoingCalls.push(new vscode.CallHierarchyOutgoingCall(toCallee, [symbolRange]));
                }
            }
        } else {
            let callees = await findCallees(item.name);
            for (let calleeItem of callees) {
                let { description, filePath, symbolRange } = await this.getSymbolInfo(calleeItem, calleeItem.name);
                let toCallee = new CCallHierarchyItem(
                    await getSymbolKind(calleeItem.name),
                    calleeItem.name,
                    description,
                    filePath,
                    symbolRange,
                    symbolRange,
                    false
                );
                outgoingCalls.push(new vscode.CallHierarchyOutgoingCall(toCallee, [symbolRange]));
            }
        }

        return outgoingCalls;
    }

    private async getSymbolInfo(
        symbol: SymbolInfo,
        relative: string,
        range?: vscode.Range
    ): Promise<SymbolInfoResult> {
        const config = vscode.workspace.getConfiguration(EXTENSION_ID);
        const canShowFileNames = config.get<boolean>('showFileNamesInSearchResults');
        const clickJumpLocation = config.get<ClickJumpLocation>('clickJumpLocation');
        const { cscopesDbPath } = getDatabasePath();
        //let path = `${this.cwd}/${symbol.filePath}`;
        let path = getFullPath(symbol.filePath);
        let symbolRange = range ?? await this.getWordRange(
            path,
            symbol.linePosition - 1,
            relative
        );
        let filePath = vscode.Uri.file(path);
        let description = `${canShowFileNames ? symbol.getFileName() : ''} @ ${symbol.linePosition.toString()}`;

        if (clickJumpLocation === ClickJumpLocation.SymbolDefinition) {
            const command = getCscopeCommand(cscopeFindDefineCommand, cscopesDbPath, relative);
            let definition = await doCLI(`${command}`);
            if (definition.length > 0) {
                let funcInfo = SymbolInfo.convertToFuncInfo(definition);
                const fpath = getFullPath(funcInfo.filePath);
                symbolRange = await this.getWordRange(
                    fpath,
                    funcInfo.linePosition - 1,
                    funcInfo.name
                );
                filePath = vscode.Uri.file(fpath);
                description = `${canShowFileNames ? funcInfo.getFileName() : ''} @ ${funcInfo.linePosition.toString()}`;
            }
        }

        return { description, filePath, symbolRange };
    }

    private async getWordRange(filePath: string, linePosition: number, word: string): Promise<vscode.Range> {

        let document; 
        try {
            const file:vscode.Uri = vscode.Uri.parse(filePath) 
            document = await vscode.workspace.openTextDocument(file);
        } catch (error) {
            outputChannel.appendLine(`Error opening file: ${filePath}`);
            outputChannel.appendLine(`Error message: ${error}`);
            return new vscode.Range(new vscode.Position(0, 0), new vscode.Position(0, 0));
        }

        let text = document.lineAt(linePosition);
        let wordIndex = 0;
        let length = text.text.length;
        try {
            wordIndex = new RegExp(`\\b${word}\\b`, "i").exec(text.text)!.index;
            length = wordIndex + word.length;
        } catch {
            outputChannel.append("can not find word:" + word)
        }
        let callerItemPositionStart = new vscode.Position(linePosition, wordIndex);
        let callerItemPositionEnd = new vscode.Position(linePosition, length);
        return new vscode.Range(callerItemPositionStart, callerItemPositionEnd);
    }
}

export async function checkDatabase(db: DatabaseType, isBuild: boolean = true): Promise<boolean> {
        let buildOption = 0;
        let infoMessage = '';
        const { cscopesDbPath, ctagsDbPath } = getDatabasePath();

        if (db === DatabaseType.CSCOPE) {
            if (fs.existsSync(cscopesDbPath)) {
                return true;
            }
            const config = vscode.workspace.getConfiguration(EXTENSION_ID);
            const autoBuildCscopeDatabase = config.get<boolean>('autoBuildCscopeDatabase');
            if (autoBuildCscopeDatabase && isBuild) {
                outputChannel.appendLine(`build cscope database: ${cscopesDbPath}`);
                infoMessage += `cscope database doesn't exist, rebuilding...\n`;
                buildOption |= 1 << 0;
                showMessageWindow(infoMessage);
                await buildDatabase(buildOption);
                return true;
            } else {
                outputChannel.appendLine(`cscope database doesn't exist, but auto-build is disabled`);
                infoMessage += `cscope database doesn't exist. Use Menu "Build Database" in Browser History windows to create it.\n`;
                showMessageWindow(infoMessage, LogLevel.WARN);
                return false;
            }
        } else if (db === DatabaseType.CTAGS) {
            if (fs.existsSync(ctagsDbPath)) {
                return true;
            }
            const config = vscode.workspace.getConfiguration(EXTENSION_ID);
            const autoBuildCtagsDatabase = config.get<boolean>('autoBuildCtagsDatabase');
            if (autoBuildCtagsDatabase && isBuild) {
                outputChannel.appendLine(`build ctags database: ${ctagsDbPath}`);
                infoMessage += `ctags database doesn't exist, rebuilding...\n`;
                buildOption |= 1 << 1;
                showMessageWindow(infoMessage);
                await buildDatabase(buildOption);
                return true;
            } else {
                outputChannel.appendLine(`ctags database doesn't exist, but auto-build is disabled`);
                infoMessage += `ctags database doesn't exist. Use Menu "Build Database" in Browser History windows to create it.\n`;
                if (!ctag_message_show) {
                    ctag_message_show = true;
                    showMessageWindow(infoMessage, LogLevel.WARN);
                    return false;
                }
                showMessageWindow(infoMessage, LogLevel.WARN);
                return false;
            }
        }
        return false;

}

export async function findFiles(fileName: string): Promise<vscode.SymbolInformation[]> {
    const { cscopesDbPath } = getDatabasePath();
    const command = getCommandPath("cscope");
    let includers: SymbolInfo[] = [];
    let data = await doCLI(`${command} -d -f ${cscopesDbPath} -L7 ${fileName}`);
    let lines = data.split('\n');

    return lines.filter((value: string) => (value.length > 0))
        .map((value: string) => {
            const symbolInfo = SymbolInfo.convertToSymbolInfo(value);
            const fpath = getFullPath(symbolInfo.filePath);
            return new vscode.SymbolInformation(
                symbolInfo.name,
                vscode.SymbolKind.Null,
                '',
                new vscode.Location(
                    vscode.Uri.file(fpath),
                    new vscode.Position(symbolInfo.linePosition, 0)
                    )
            );
        });
}


export async function findIncluders(fileName: string): Promise<SymbolInfo[]> {
    const { cscopesDbPath } = getDatabasePath();
    const command = getCommandPath("cscope");
    let includers: SymbolInfo[] = [];
    let data = await doCLI(`${command} -d -f ${cscopesDbPath} -L8 ${fileName}`);
    let lines = data.split('\n');

    for (let line of lines) {
        if (line.length > 0) {
            includers.push(SymbolInfo.convertToSymbolInfo(line));
        }
    }

    return includers;
}

export async function findCallers(funcName: string, isCpp: boolean): Promise<SymbolInfo[]> {
    const fp_callers = g_functionPointerMap.getCallersBySymbol(funcName);
    let ex_callers: SymbolInfo[] = [];
    if (fp_callers.length > 0) {
        ex_callers = fp_callers.map((caller) => {
                const rpath = caller.location.uri.fsPath.replace(/^[\\\/]/, '');
                return new SymbolInfo(caller.name, rpath, caller.location.range.start.line + 1, "");
            }
        );
    }
    const { cscopesDbPath } = getDatabasePath();
    let cscope_path = '';
    if (isCpp)
        cscope_path = getCommandPath("gtags-cscope");
    else
        cscope_path = getCommandPath("cscope");

    outputChannel.appendLine(`findCallers: ${cscope_path}, ${funcName}`);
    let sl = 3;
    if (g_findAssignment) {
        sl = 9;
        setFindAssignment(false);
    }
    let data = await doCLI(`${cscope_path} -d -f ${cscopesDbPath} -L${sl} ${funcName}`, true);
    let lines = data.split('\n');

    let cs_caller: SymbolInfo[] = [];
    if (!isCpp) {
        cs_caller = lines.filter((value: string) => (value.length > 0))
        .map((value: string) => SymbolInfo.convertToFuncInfo(value));
    } else {
        for (let line of lines) {
            if (line.length > 0) {
                const symbolInfo = await SymbolInfo.convertToFuncInfoCpp(line);
                cs_caller.push(symbolInfo);
            }
        }
    }

    return [...ex_callers, ...cs_caller];

/*    return lodash.chain(lines)
        .filter((value: string) => (value.length > 0))
        .flatMap((value: string) => SymbolInfo.convertToFuncInfo(value))
        .groupBy((x: SymbolInfo) => x.linePosition)
        .map((group: SymbolInfo[]) => group.slice(-1)[0])
        .value();
        */
}

export async function findCallees(funcName: string): Promise<SymbolInfo[]> {
    const { cscopesDbPath } = getDatabasePath();
    const command = getCommandPath("cscope");
    let data = await doCLI(`${command} -d -f ${cscopesDbPath} -L2 ${funcName}`);
    let lines = data.split('\n');
    return lines.filter((value: string) => (value.length > 0))
        .map((value: string) => SymbolInfo.convertToFuncInfo(value));


/*    return lodash.chain(lines)
        .filter((value: string) => (value.length > 0))
        .flatMap((value: string) => SymbolInfo.convertToFuncInfo(value))
        .groupBy((x: SymbolInfo) => x.linePosition)
        .map((group: SymbolInfo[]) => group.slice(-1)[0])
        .value();
        */
}

export async function findDefinition(funcName: string): Promise<vscode.SymbolInformation[]> {
   
    const { cscopesDbPath } = getDatabasePath();
    const command = getCommandPath("cscope");
    let data = await doCLI(`${command} -d -f ${cscopesDbPath} -L1 ${funcName}`, true);
    let lines = data.split('\n');

    return lines.filter((value: string) => (value.length > 0))
        .map((value: string) => {
            const symbolInfo = SymbolInfo.convertToFuncInfo(value);
            const path = getFullPath(symbolInfo.filePath);
            return new vscode.SymbolInformation(
                symbolInfo.name,
                vscode.SymbolKind.Null,
                '',
                new vscode.Location(
                    vscode.Uri.file(path),
                    new vscode.Position(symbolInfo.linePosition, 0)
                    )
            );
        });
}

export async function findFunctionReferences(funcName: string, isCpp:Boolean): Promise<SymbolInfo[]> {
    if (! await checkDatabase(DatabaseType.CSCOPE))
        return [];

    const { cscopesDbPath } = getDatabasePath();
    
    // 检查当前文档是否为 C++ 文件

    
    let data;
    let command;
    if (isCpp) {
        // 对于 C++ 文档使用 global 命令
        command = getCommandPath("gtags-cscope");
        //data = await doCLI(`${command} -r -x ${funcName}`, true);
    } else {
        // 对于其他文档使用 cscope 命令
        command = getCommandPath("cscope");
    }
    data = await doCLI(`${command} -d -f ${cscopesDbPath} -L0 ${funcName}`, true);
    let lines = data.split('\n');

    let cs_caller: SymbolInfo[] = [];
    for (let line of lines) {
            if (line.length > 0) {
                let symbolInfo;
                if (isCpp)
                    symbolInfo = await SymbolInfo.convertToFuncInfoCpp(line);
                else
                    symbolInfo = SymbolInfo.convertToFuncRefInfo(line);
                if (symbolInfo.name === funcName)
                    continue;
                if (symbolInfo.name === "<global>") {
                    //const regex = new RegExp(`\\s*${funcName}\\s*\([^)]*\)\s*;`, 'gi');
                    const functionDeclarationRegex = /^(?:\s*)((?:(?:inline|static|extern|const)\s+)*)((?:unsigned\s+|signed\s+)?(?:void|char|short|int|long|float|double|size_t|[a-zA-Z_]??\w*?)\s*\**)\s*([a-zA-Z_]\w*)\s*\(([\s\S]*?)\)\s*;/;
                    //this is a function declaration, not a function call
                    const match = functionDeclarationRegex.exec(symbolInfo.description);
                    if (match) {
                        if (match[0] != '' && match[1] != '')
                            continue;
                    }
                }
                const structVariable = await findStructVariableAtLine(symbolInfo.filePath, symbolInfo.linePosition);
                if (structVariable) {
                    symbolInfo.name = structVariable;
                }
                cs_caller.push(symbolInfo);
            }
    }

    return cs_caller;
}

export async function findReferences(funcName: string): Promise<SymbolInfo[]> {
    if (! await checkDatabase(DatabaseType.CSCOPE))
        return [];

    const { cscopesDbPath } = getDatabasePath();
    const activeEditor = vscode.window.activeTextEditor;
    let sl = 0;
    if (g_findGrep) {
        sl = 6;
        setFindGrep(false);
    } else if (g_findAssignment) {
        sl = 9;
        setFindAssignment(false);
    } else if (g_findInclude) {
        sl = 8;
        if (activeEditor) {
            const currentFilePath = activeEditor.document.uri.fsPath;
            console.log(currentFilePath); // Full filesystem path
            funcName = path.basename(currentFilePath);
        }
        setFindInclude(false);
    }
    let isCpp = false;
    
    if (activeEditor) {
        const fileName = activeEditor.document.fileName.toLowerCase();
        isCpp = fileName.endsWith('.cpp') || fileName.endsWith('.cc') || 
                fileName.endsWith('.cxx') || fileName.endsWith('.hpp') || 
                fileName.endsWith('.h++');
    }
    let data, command; 
    if (isCpp) {
        // 对于 C++ 文档使用 global 命令
        command = getCommandPath("gtags-cscope");
        //data = await doCLI(`${command} -r -x ${funcName}`, true);
    } else {
        // 对于其他文档使用 cscope 命令
        command = getCommandPath("cscope");
    }
    data = await doCLI(`${command} -d -f ${cscopesDbPath} -L${sl} ${funcName}`, true);

    let lines = data.split('\n');

    return lines.map((value: string) => {
        if (value.length === 0)
            return new SymbolInfo(funcName, "", 0, "");
        let symbolInfo = SymbolInfo.convertToFuncRefInfo(value);
        if (isCpp)
            symbolInfo.name = '<cpp>';
        symbolInfo.name = '<cpp>';
        if (symbolInfo.description.includes("EXPORT_SYMBOL"))
            symbolInfo.name = funcName;
        else if (symbolInfo.name === "<global>" || isCpp) {
                const functionDeclarationRegex = /^(?:\s*)((?:(?:inline|static|extern|const)\s+)*)((?:unsigned\s+|signed\s+)?(?:void|char|short|int|long|float|double|size_t|[a-zA-Z_]??\w*?)\s*\**)\s*([a-zA-Z_]??\w*?)\s*\(([\s\S]*?)\)\s*;/;
                //this is a function declaration, not a function call
                const match = functionDeclarationRegex.exec(symbolInfo.description);
                if (match) {
                    if (match[0] != '' && match[1] != '')
                        symbolInfo.name = funcName;
                    else if(isCpp)
                        symbolInfo.name = '<cpp>';
                }
        }
        return symbolInfo;
    }).filter((symbolInfo: SymbolInfo) => symbolInfo.name !== funcName);

/*    return lodash.chain(lines)
        .filter((value: string) => (value.length > 0))
        .flatMap((value: string) => SymbolInfo.convertToFuncInfo(value))
        .groupBy((x: SymbolInfo) => x.linePosition)
        .map((group: SymbolInfo[]) => group.slice(-1)[0])
        .value();
        */
}

export async function getSymbolKind(symbolName: string, showMessage: boolean = false): Promise<vscode.SymbolKind> {

    if (symbolName == '<global>')
        return vscode.SymbolKind.Null;
    const { ctagsDbPath } = getDatabasePath();
    const command = getCommandPath("readtags");

    let format = process.platform === 'win32' ?
        '(list $name \\" \\" $input \\" \\" $line \\" \\" $kind #t)' :
        '(list $name " " $input " " $line " " $kind #t)';

    let data = process.platform === 'win32' ?
        await doCLI(`${command} -t ${ctagsDbPath} -F "(list $name \\" \\" $input \\" \\" $line \\" \\" $kind #t)" ${symbolName}`) :
        await doCLI(`${command} -t ${ctagsDbPath} -F '(list $name " " $input " " $line " " $kind #t)' ${symbolName}`);


    //let data = await doCLI(`${command} -t ${ctagsDbPath} -F ${format} ${symbolName}`, showMessage);

    let lines = data.split(/\n/);
    let kind = vscode.SymbolKind.Null;

    for (let line of lines) {
        let fields = line.split(/\s+/);
        if (fields.length >= 4) {
            if (fields[3] === 'd') {
                const path = getFullPath(fields[1]);
                const docLines = (await vscode.workspace.openTextDocument(path)).getText().split(/\n/gi);
                const definition = docLines[Number(fields[2]) - 1];
                const regex = new RegExp(`#define\\s*${fields[0]}\\s*\\(`, 'gi');
                if (regex.test(definition)) {
                    kind = vscode.SymbolKind.Field;
                } else {
                    kind = (fields[3] in symbols) ? symbols[fields[3]] : vscode.SymbolKind.Null;
                }
            } else {
                kind = (fields[3] in symbols) ? symbols[fields[3]] : vscode.SymbolKind.Null;
            }
        }
        if (kind === vscode.SymbolKind.Function) {
            return kind;
        }
    }
    return kind;
}




export function showMessageWindow(msg: string, logLevl: LogLevel = LogLevel.INFO): void {
    const config = vscode.workspace.getConfiguration(EXTENSION_ID);
    const canShowMessages = true; //config.get<boolean>('showMessages');

    if (canShowMessages) {
        switch (logLevl) {
            case LogLevel.INFO:
                vscode.window.showInformationMessage(msg);
                break;
            case LogLevel.WARN:
                vscode.window.showWarningMessage(msg);
                break;
            case LogLevel.ERROR:
                vscode.window.showErrorMessage(msg);
                break;
            default:
                break;
        }
    }
}
