import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { g_browserHistory } from './extension';
import { g_functionPointerMap } from './extension';
import { findFunctionNameByLine } from './UtilFuns';
import { g_browserHistoryViewProvider } from './BrowserHistoryView';

/**
 * Class to maintain mappings between function pointer calls and their definitions
 */
export class FunctionPointerMap {
    // Map to store caller -> definitions relationships (one caller can have multiple definitions)
    private callerToDefMap: Map<string, { caller: vscode.SymbolInformation, definitions: vscode.SymbolInformation[] }> = new Map();
    
    // Arrays to store callers and definitions separately
    private callers: vscode.SymbolInformation[] = [];
    private definitions: vscode.SymbolInformation[] = [];
    
    // Auto-save related properties
    private isDirty: boolean = false;
    private autoSaveInterval: NodeJS.Timeout | null = null;
    private storageUri: string = '';
    private context: vscode.ExtensionContext | null = null;
    
    constructor() {
        // Start auto-save timer
        this.startAutoSave();
    }
    
    /**
     * Start auto-save timer
     */
    private startAutoSave(): void {
        // Clear any existing interval
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }
        
        // Set up new interval - save every 10 seconds if dirty
        this.autoSaveInterval = setInterval(() => {
            if (this.isDirty && this.context) {
                this.save(this.context).then(() => {
                    this.isDirty = false;
                }).catch(err => {
                    console.error('Auto-save failed for function pointer map:', err);
                });
            }
        }, 10000); // 10 seconds
    }
    
    /**
     * Stop auto-save timer (call when disposing)
     */
    public stopAutoSave(): void {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }
        
        // Final save if dirty
        if (this.isDirty && this.context) {
            this.save(this.context).catch(err => {
                console.error('Final save failed for function pointer map:', err);
            });
        }
    }
    
    /**
     * Generate a unique key for a SymbolInformation
     * @param symbol The symbol to generate a key for
     * @returns A string key
     */
    private getSymbolKey(symbol: vscode.SymbolInformation): string {
        return `${symbol.location.uri.toString()}:${symbol.location.range.start.line}:${symbol.location.range.start.character}`;
    }
    
    /**
     * Mark a symbol as a function pointer caller
     * @param caller The symbol that represents a function pointer call
     */
    public markCaller(caller: vscode.SymbolInformation): void {
        // Check if this caller is already in the array
        if (!this.callers.some(c => this.getSymbolKey(c) === this.getSymbolKey(caller))) {
            this.callers.push(caller);
            this.isDirty = true;
        }
    }
    
    /**
     * Get the list of all function pointer callers
     * @returns Array of caller symbols
     */
    public listCallers(): vscode.SymbolInformation[] {
        return [...this.callers];
    }
    
    /**
     * Mark a symbol as a function definition
     * @param definition The symbol that represents a function definition
     */
    public markDef(definition: vscode.SymbolInformation): void {
        // Check if this definition is already in the array
        if (!this.definitions.some(d => this.getSymbolKey(d) === this.getSymbolKey(definition))) {
            this.definitions.push(definition);
            this.isDirty = true;
        }
    }
    
    /**
     * Get the list of all function definitions
     * @returns Array of definition symbols
     */
    public listDefs(): vscode.SymbolInformation[] {
        return [...this.definitions];
    }
    
    /**
     * Link a caller to a definition
     * @param caller The function pointer call
     * @param definition The actual function definition
     */
    public link(caller: vscode.SymbolInformation, definition: vscode.SymbolInformation): void {
        const callerKey = this.getSymbolKey(caller);
        
        // Check if this caller already exists in the map
        if (this.callerToDefMap.has(callerKey)) {
            // Get the existing entry
            const entry = this.callerToDefMap.get(callerKey)!;
            
            // Check if this definition is already linked to this caller
            const defKey = this.getSymbolKey(definition);
            if (!entry.definitions.some(def => this.getSymbolKey(def) === defKey)) {
                // Add the new definition to the array
                entry.definitions.push(definition);
            }
        } else {
            // Create a new entry with the caller and an array containing the definition
            this.callerToDefMap.set(callerKey, {
                caller: caller,
                definitions: [definition]
            });
        }
        
        // Mark as dirty for auto-save
        this.isDirty = true;
    }
    
    /**
     * Get the definitions for a caller
     * @param caller The function pointer call
     * @returns Array of linked function definitions, or undefined if not found
     */
    public getDefinitions(caller: vscode.SymbolInformation): vscode.SymbolInformation[] | undefined {
        const callerKey = this.getSymbolKey(caller);
        const entry = this.callerToDefMap.get(callerKey);
        if (entry) {
            return [...entry.definitions];
        }
        return undefined;
    }

    public getDefinitionsBySymbol(symbol: string): vscode.SymbolInformation[] {
        const definitions: vscode.SymbolInformation[] = [];
        
        for (const [key, value] of this.callerToDefMap.entries()) {
            if (value.caller.name === symbol) {
                definitions.push(...value.definitions);
            }
        }
        
        return definitions;
    }

    public getCallersBySymbol(symbol: string): vscode.SymbolInformation[] {
        const callers: vscode.SymbolInformation[] = [];
        
        for (const [key, value] of this.callerToDefMap.entries()) {
            if (value.definitions.some(def => def.name === symbol)) {
                callers.push(value.caller);
            }
        }
        
        return callers;
    }

        /**
     * Remove a caller from the map
     * @param caller The caller symbol to remove
     */
    public delMapByCaller(caller: vscode.SymbolInformation): void {
        const key = this.getSymbolKey(caller);
        
        // Remove from callerToDefMap if exists
        if (this.callerToDefMap.has(key)) {
            this.callerToDefMap.delete(key);
        }
        
        this.isDirty = true;
    }

    /**
     * Remove a caller from the map
     * @param caller The caller symbol to remove
     */
    public delCaller(caller: vscode.SymbolInformation): void {
        const key = this.getSymbolKey(caller);
        
        // Remove from callers array
        this.callers = this.callers.filter(c => this.getSymbolKey(c) !== key);
        
        this.isDirty = true;
    }

    /**
     * Remove a definition from the map and all its references
     * @param definition The definition symbol to remove
     */
    public delDefinition(definition: vscode.SymbolInformation): void {
        const defKey = this.getSymbolKey(definition);
        
        // Remove from definitions array
        this.definitions = this.definitions.filter(d => this.getSymbolKey(d) !== defKey);
        
        this.isDirty = true;
    }    
    /**
     * Clear all mappings and arrays
     */
    public clear(): void {
        this.callerToDefMap.clear();
        this.callers = [];
        this.definitions = [];
        this.isDirty = true;
    }
    
    /**
     * Save the function pointer mappings to a file
     * @param filePath The path to save the mappings to
     */
    public async saveToFile(filePath: string): Promise<void> {
        try {
            // Create a serializable data structure
            const serializedData = {
                mappings: Array.from(this.callerToDefMap.entries()).map(([key, value]) => ({
                    caller: {
                        name: value.caller.name,
                        kind: value.caller.kind,
                        containerName: value.caller.containerName || '',
                        location: {
                            uri: value.caller.location.uri.toString(),
                            range: {
                                start: {
                                    line: value.caller.location.range.start.line,
                                    character: value.caller.location.range.start.character
                                },
                                end: {
                                    line: value.caller.location.range.end.line,
                                    character: value.caller.location.range.end.character
                                }
                            }
                        }
                    },
                    definitions: value.definitions.map(def => ({
                        name: def.name,
                        kind: def.kind,
                        containerName: def.containerName || '',
                        location: {
                            uri: def.location.uri.toString(),
                            range: {
                                start: {
                                    line: def.location.range.start.line,
                                    character: def.location.range.start.character
                                },
                                end: {
                                    line: def.location.range.end.line,
                                    character: def.location.range.end.character
                                }
                            }
                        }
                    }))
                })),
                callers: this.callers.map(caller => ({
                    name: caller.name,
                    kind: caller.kind,
                    containerName: caller.containerName || '',
                    location: {
                        uri: caller.location.uri.toString(),
                        range: {
                            start: {
                                line: caller.location.range.start.line,
                                character: caller.location.range.start.character
                            },
                            end: {
                                line: caller.location.range.end.line,
                                character: caller.location.range.end.character
                            }
                        }
                    }
                })),
                definitions: this.definitions.map(def => ({
                    name: def.name,
                    kind: def.kind,
                    containerName: def.containerName || '',
                    location: {
                        uri: def.location.uri.toString(),
                        range: {
                            start: {
                                line: def.location.range.start.line,
                                character: def.location.range.start.character
                            },
                            end: {
                                line: def.location.range.end.line,
                                character: def.location.range.end.character
                            }
                        }
                    }
                }))
            };
            
            // Convert to JSON and write to file
            const jsonData = JSON.stringify(serializedData, null, 2);
            await vscode.workspace.fs.writeFile(
                vscode.Uri.file(filePath),
                Buffer.from(jsonData)
            );
        } catch (error) {
            throw new Error(`Failed to save function pointer mappings: ${error}`);
        }
    }
    
    /**
     * Load function pointer mappings from a file
     * @param filePath The path to load the mappings from
     */
    public async loadFromFile(filePath: string): Promise<void> {
        try {
            // Read the file
            const fileContent = await vscode.workspace.fs.readFile(vscode.Uri.file(filePath));
            const serializedData = JSON.parse(fileContent.toString());
            
            // Clear existing data
            //this.clear();
            
            // Helper function to create SymbolInformation from serialized data
            const createSymbolInfo = (data: any) => new vscode.SymbolInformation(
                data.name,
                data.kind,
                data.containerName,
                new vscode.Location(
                    vscode.Uri.parse(data.location.uri),
                    new vscode.Range(
                        new vscode.Position(
                            data.location.range.start.line,
                            data.location.range.start.character
                        ),
                        new vscode.Position(
                            data.location.range.end.line,
                            data.location.range.end.character
                        )
                    )
                )
            );
            
            // Load callers array
            if (serializedData.callers) {
                this.callers = serializedData.callers.map((c: any) => createSymbolInfo(c));
            }
            
            // Load definitions array
            if (serializedData.definitions) {
                this.definitions = serializedData.definitions.map((d: any) => createSymbolInfo(d));
            }
            
            // Load mappings
            if (serializedData.mappings) {
                for (const entry of serializedData.mappings) {
                    const caller = createSymbolInfo(entry.caller);
                    
                    // Handle both old format (single definition) and new format (array of definitions)
                    if (Array.isArray(entry.definitions)) {
                        // New format
                        const definitions = entry.definitions.map((def: any) => createSymbolInfo(def));
                        
                        // Create the map entry
                        this.callerToDefMap.set(this.getSymbolKey(caller), {
                            caller: caller,
                            definitions: definitions
                        });
                    } else if (entry.definition) {
                        // Old format - single definition
                        const definition = createSymbolInfo(entry.definition);
                        this.link(caller, definition);
                    }
                }
            }
            
            // Reset dirty flag since we just loaded
            this.isDirty = false;
        } catch (error) {
            // If file doesn't exist or has invalid format, just start with empty map
            console.log(`No existing function pointer mappings file found or error loading: ${error}`);
        }
    }
    
    /**
     * Save mappings to the default location in the workspace
     */
    public async save(context: vscode.ExtensionContext): Promise<void> {
        this.context = context; // Store context for auto-save
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            this.storageUri = path.join(workspaceFolder.uri.fsPath, 'sourceseek-fpm.json');
            return this.saveToFile(this.storageUri);
        }
    }
    
    /**
     * Load mappings from the default location in the workspace
     */
    public async load(context: vscode.ExtensionContext): Promise<void> {
        this.context = context; // Store context for auto-save
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (workspaceFolder) {
            this.storageUri = path.join(workspaceFolder.uri.fsPath, 'sourceseek-fpm.json');
            return this.loadFromFile(this.storageUri);
        }
    }
    
    /**
     * Dispose resources
     */
    public dispose(): void {
        this.stopAutoSave();
    }
} 

export function registerFunctionPointerMap(context: vscode.ExtensionContext) {
        // Register the link to definition command
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.linkToDefinition', async () => {
            // Get the current selection
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                return;
            }
            
            const selection = editor.selection;
            if (selection.isEmpty) {
                vscode.window.showInformationMessage('Please select a symbol to link');
                return;
            }
            
            // Get the selected text
            const selectedText = editor.document.getText(selection);
            const relativePath = path.relative(vscode.workspace.workspaceFolders![0].uri.fsPath, editor.document.uri.fsPath);
            const rpath = vscode.Uri.file(relativePath);
            // Create a SymbolInformation for the caller

            let callname = findFunctionNameByLine(editor.document, selection.start.line + 1);
            let kind = vscode.SymbolKind.Function;
            if (!callname) {
                vscode.window.showInformationMessage('No function name found at the selected line');
                callname = path.basename(editor.document.uri.fsPath) + '+' + selection.start.line;
                kind = vscode.SymbolKind.Null;
            }
            const caller = new vscode.SymbolInformation(
                callname,
                kind,
                '',
                new vscode.Location(
                    rpath,
                    selection
                )
            );
            let def_entries = g_functionPointerMap.listDefs();
            // Get function definitions from browser history
            const historyEntries = g_browserHistory.getList();

            const functionDefinitions: vscode.SymbolInformation[] = [];

            // Collect all function symbols from history
            for (const entry of historyEntries) {
                for (const item of entry.items) {
                    if (item.kind === vscode.SymbolKind.Function ||
                        item.kind === vscode.SymbolKind.Method) {
                        // Convert CallHierarchyItem to SymbolInformation
                        functionDefinitions.push(
                            new vscode.SymbolInformation(
                                item.name,
                                item.kind,
                                item.detail || '',
                                new vscode.Location(
                                    item.uri,
                                    item.range
                                )
                            )
                        );
                    }
                }
            }
            const entries = [...def_entries, ...functionDefinitions];
            
            // If no function definitions found
            if (entries.length === 0) {
                vscode.window.showInformationMessage('No function definitions found in history');
                return;
            }
            
            // Create quick pick items
            const quickPickItems = entries.map(def => ({
                label: def.name,
                description: `${path.basename(def.location.uri.fsPath)} ${def.location.range.start.line + 1}`,
                symbolInfo: def
            }));
            
            // Show quick pick
            const selected = await vscode.window.showQuickPick(quickPickItems, {
                placeHolder: 'Select a function definition to link to',
                matchOnDescription: true,
                matchOnDetail: true
            });
            
            if (selected) {
                // Link the caller to the selected definition
                g_functionPointerMap.link(caller, selected.symbolInfo);
                g_functionPointerMap.delCaller(caller);
                g_functionPointerMap.delDefinition(selected.symbolInfo);
                vscode.window.showInformationMessage(
                    `Linked "${selectedText}" to "${selected.label}"`
                );
                
            } else {
                g_functionPointerMap.markCaller(caller);
                g_browserHistory.addSymbolInformation([caller], null);
                g_browserHistoryViewProvider?.refresh();
                vscode.window.showInformationMessage("No definition selected. Caller added to history.");
            }
        })
    );

    // Register the link to caller command
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.linkToCaller', async () => {
            // Get the current selection
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                return;
            }
            
            const selection = editor.selection;
            if (selection.isEmpty) {
                vscode.window.showInformationMessage('Please select a definition to link');
                return;
            }
            
            // Get the selected text
            const selectedText = editor.document.getText(selection);
            
            const relativePath = path.relative(vscode.workspace.workspaceFolders![0].uri.fsPath, editor.document.uri.fsPath);
            const rpath = vscode.Uri.file(relativePath);
            const callname = findFunctionNameByLine(editor.document, selection.start.line + 1);
            if (!callname) {
                vscode.window.showInformationMessage('No function name found at the selected line');
                return;
            }
            // Create a SymbolInformation for the definition
            const definition = new vscode.SymbolInformation(
                callname,
                vscode.SymbolKind.Function,
                '',
                new vscode.Location(
                    rpath,
                    selection
                )
            );
            
            // Get all callers from the function pointer map
            const callers = g_functionPointerMap.listCallers();
            
            // If no callers found
            if (callers.length === 0) {
                vscode.window.showInformationMessage('No callers found. Create a caller first using "Link to Definition"');
                return;
            }
            
            // Create quick pick items
            const quickPickItems = callers.map(caller => ({
                label: caller.name,
                description: `${path.basename(caller.location.uri.fsPath)} ${caller.location.range.start.line + 1}`,
                symbolInfo: caller
            }));
            
            // Show quick pick
            const selected = await vscode.window.showQuickPick(quickPickItems, {
                placeHolder: 'Select a caller to link to this definition',
                matchOnDescription: true,
                matchOnDetail: true
            });
            
            if (selected) {
                // Link the selected caller to this definition
                g_functionPointerMap.link(selected.symbolInfo, definition);
                g_functionPointerMap.delDefinition(definition);
                g_functionPointerMap.delCaller(selected.symbolInfo);
                vscode.window.showInformationMessage(
                    `Linked caller "${selected.label}" to definition "${selectedText}"`
                );
                
            } else {
                // Optionally, add the definition to the definitions list
                g_functionPointerMap.markDef(definition);
                g_browserHistory.addSymbolInformation([definition], null);
                vscode.window.showInformationMessage("No caller selected. Definition added to definitions list.");
            }
        })
    );

    // Register the print function pointer map command
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.printFunctionPointerMap', () => {
            // Create or get the output channel
            const outputChannel = vscode.window.createOutputChannel('SourceSeek-FPMAP');
            outputChannel.clear();
            outputChannel.show(true); // Show and preserve focus
            
            // Print header
            outputChannel.appendLine('=== Function Pointer Mappings ===');
            outputChannel.appendLine('');
            
            // Print all mappings
            const mappings = Array.from(g_functionPointerMap['callerToDefMap'].entries());
            
            if (mappings.length === 0) {
                outputChannel.appendLine('No function pointer mappings found.');
            } else {

                // Print each mapping
                for (const [key, value] of mappings) {
                    const caller = value.caller;
                    const definitions = value.definitions;

                    // Print caller info
                    outputChannel.appendLine(`Caller: ${caller.name} ${caller.location.uri.fsPath}:${caller.location.range.start.line + 1}:${caller.location.range.start.character + 1}`);

                    // Print definitions
                    outputChannel.appendLine(`  Linked to ${definitions.length} definition(s):`);
                    for (const def of definitions) {
                        outputChannel.appendLine(`    - ${def.name} (${vscode.SymbolKind[def.kind]})`);
                        outputChannel.appendLine(`      Location: ${def.location.uri.fsPath}:${def.location.range.start.line + 1}:${def.location.range.start.character + 1}`);
                    }
                    outputChannel.appendLine('');
                }

                outputChannel.appendLine(`Total mappings: ${mappings.length}`);
            }
            // Print summary
            outputChannel.appendLine(`Total callers: ${g_functionPointerMap.listCallers().length}`);
            for (const caller of g_functionPointerMap.listCallers()) {
                outputChannel.appendLine(`    - ${caller.name} ${caller.location.uri.fsPath}:${caller.location.range.start.line + 1}:${caller.location.range.start.character + 1}`);
            }
            outputChannel.appendLine(`Total definitions: ${g_functionPointerMap.listDefs().length}`);
            for (const def of g_functionPointerMap.listDefs()) {
                outputChannel.appendLine(`    - ${def.name} (${vscode.SymbolKind[def.kind]}) ${def.location.uri.fsPath}:${def.location.range.start.line + 1}:${def.location.range.start.character + 1}`);
            }
        })
    );
    
    // Register the save function pointer map command
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.saveFunctionPointerMap', async () => {
            try {
                // Check if there are any mappings to save
                const mappings = Array.from(g_functionPointerMap['callerToDefMap'].entries());
                if (mappings.length === 0 && 
                    g_functionPointerMap.listCallers().length === 0 && 
                    g_functionPointerMap.listDefs().length === 0) {
                    vscode.window.showInformationMessage('No function pointer mappings to save.');
                    return;
                }

                // Open file save dialog
                const defaultPath = vscode.workspace.workspaceFolders
                    ? path.join(vscode.workspace.workspaceFolders[0].uri.fsPath, 'function_pointer_map.json')
                    : 'function_pointer_map.json';

                const fileUri = await vscode.window.showSaveDialog({
                    defaultUri: vscode.Uri.file(defaultPath),
                    filters: {
                        'JSON Files': ['json'],
                        'All Files': ['*']
                    },
                    title: 'Save Function Pointer Map'
                });

                if (!fileUri) {
                    // User cancelled the dialog
                    return;
                }

                // Save the function pointer map to the specified file
                await g_functionPointerMap.saveToFile(fileUri.fsPath);
                vscode.window.showInformationMessage(`Function pointer map saved to ${fileUri.fsPath}`);
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to save function pointer map: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );
    
    // Register the load function pointer map command
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.loadFunctionPointerMap', async () => {
            try {
                // Open file open dialog
                const fileUris = await vscode.window.showOpenDialog({
                    canSelectMany: false,
                    filters: {
                        'JSON Files': ['json'],
                        'All Files': ['*']
                    },
                    title: 'Load Function Pointer Map'
                });

                if (!fileUris || fileUris.length === 0) {
                    // User cancelled the dialog
                    return;
                }

                const fileUri = fileUris[0];

                // Show confirmation dialog if there are existing mappings
                const mappings = Array.from(g_functionPointerMap['callerToDefMap'].entries());
                if (mappings.length > 0 || 
                    g_functionPointerMap.listCallers().length > 0 || 
                    g_functionPointerMap.listDefs().length > 0) {
                    const answer = await vscode.window.showWarningMessage(
                        'Loading will replace all existing function pointer mappings. Continue?',
                        { modal: true },
                        'Yes', 'No'
                    );

                    if (answer !== 'Yes') {
                        return;
                    }
                }

                // Load the function pointer map from the specified file
                await g_functionPointerMap.loadFromFile(fileUri.fsPath);
                vscode.window.showInformationMessage(`Function pointer map loaded from ${fileUri.fsPath}`);
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to load function pointer map: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );
    
    // Register the clear function pointer map command
    context.subscriptions.push(
        vscode.commands.registerCommand('browserHistoryView.clearFunctionPointerMap', async () => {
            try {
                // Check if there are any mappings to clear
                const mappings = Array.from(g_functionPointerMap['callerToDefMap'].entries());
                if (mappings.length === 0 && 
                    g_functionPointerMap.listCallers().length === 0 && 
                    g_functionPointerMap.listDefs().length === 0) {
                    vscode.window.showInformationMessage('No function pointer mappings to clear.');
                    return;
                }

                // Show confirmation dialog
                const answer = await vscode.window.showWarningMessage(
                    'Are you sure you want to clear all function pointer mappings?',
                    { modal: true },
                    'Yes', 'No'
                );

                if (answer !== 'Yes') {
                    return;
                }

                // Clear all mappings
                g_functionPointerMap['callerToDefMap'].clear();
                g_functionPointerMap['callers'] = [];
                g_functionPointerMap['definitions'] = [];
                g_functionPointerMap['isDirty'] = true;

                vscode.window.showInformationMessage('Function pointer map cleared.');
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to clear function pointer map: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );
}
