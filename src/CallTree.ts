import * as vscode from 'vscode';
import { outputChannel } from './UtilFuns';

// Node in the call tree
export class CallNode {
    public symbol: vscode.CallHierarchyItem;
    public children: CallNode[] = [];
    public parent: CallNode | null = null;

    constructor(symbol: vscode.CallHierarchyItem) {
        this.symbol = symbol;
    }

    // Add a child node
    addChild(node: CallNode): void {
        if (node == this) {
            vscode.window.showErrorMessage("Add self as child!!!:"+node.symbol.name);
            //outputChannel.appendLine("Add self as child!!!");
            return;
        }
        node.parent = this;
        this.children.push(node);
    }

    // Check if this node has the given symbol
    hasSymbol(symbolName: string): boolean {
        return this.symbol.name === symbolName;
    }
}

// Tree structure to store calling relationships
export class CallTree {
    private root: CallNode;
    private nodeMap: Map<string, CallNode[]> = new Map();

    constructor() {
        // Create a root node with a placeholder symbol
        this.root = new CallNode(
            new vscode.CallHierarchyItem(
                vscode.SymbolKind.Null,
                "ROOT",
                "",
                vscode.Uri.parse("file://root"),
                new vscode.Range(0, 0, 0, 0),
                new vscode.Range(0, 0, 0, 0)
            )
        );
    }

    // Generate a unique key for a symbol
    private getSymbolKey(symbol: vscode.CallHierarchyItem): string {
        return `${symbol.name}:${symbol.uri.toString()}:${symbol.range.start.line}:${symbol.range.start.character}`;
    }

    // Find a node by symbol name
    private findNodeBySymbol(symbolName: string): CallNode | null {
        // First try the map for quick lookup
        const nodes = this.nodeMap.get(symbolName);
        if (nodes && nodes.length > 0) {
            for (const node of nodes) {
                if (node.symbol.name === symbolName) {
                    return node;
                }
            }
            return nodes[0]; // Return the first matching node
        }
        
        // Fallback to tree traversal if not found in map
        return this.findNodeInSubtree(this.root, symbolName);
    }

    // Recursively search for a node with the given symbol name
    private findNodeInSubtree(node: CallNode, symbolName: string): CallNode | null {
        if (node.hasSymbol(symbolName)) {
            return node;
        }

        for (const child of node.children) {
            const found = this.findNodeInSubtree(child, symbolName);
            if (found) {
                return found;
            }
        }

        return null;
    }

    // Add a symbol and its callers to the tree
    public add(symbol: vscode.CallHierarchyItem, callers: vscode.CallHierarchyItem[]): void {
        // Find the node for this symbol
        let symbolNode = this.findNodeBySymbol(symbol.name);
        
        // If not found, create a new node and add it to the root
        if (!symbolNode) {
            symbolNode = new CallNode(symbol);
            this.root.addChild(symbolNode);
            
            // Add to the map for quick lookup
            if (!this.nodeMap.has(symbol.name)) {
                this.nodeMap.set(symbol.name, []);
            }
            this.nodeMap.get(symbol.name)!.push(symbolNode);
        }
        
        // Add all callers as children of the symbol node
        for (const caller of callers) {
            // Check if this caller already exists as a child
            const existingChild = symbolNode.children.find(
                child => child.symbol.name === caller.name &&
                         child.symbol.uri.toString() === caller.uri.toString() &&
                         child.symbol.range.isEqual(caller.range)
            );
            
            if (!existingChild) {
                const callerNode = new CallNode(caller);
                symbolNode.addChild(callerNode);
                
                // Add to the map for quick lookup
                if (!this.nodeMap.has(caller.name)) {
                    this.nodeMap.set(caller.name, []);
                }
                this.nodeMap.get(caller.name)!.push(callerNode);
            }
        }
    }

    // Get all nodes for a symbol name
    public getNodesForSymbol(symbolName: string): CallNode[] {
        return this.nodeMap.get(symbolName) || [];
    }

    // Get the entire call tree
    public getTree(): CallNode {
        return this.root;
    }

    // Get all symbols in the tree
    public getAllSymbols(): vscode.CallHierarchyItem[] {
        const symbols: vscode.CallHierarchyItem[] = [];
        this.traverseTree(this.root, (node) => {
            if (node !== this.root) { // Skip the root node
                symbols.push(node.symbol);
            }
        });
        return symbols;
    }

    // Traverse the tree and call the callback for each node
    private traverseTree(node: CallNode, callback: (node: CallNode) => void): void {
        callback(node);
        for (const child of node.children) {
            this.traverseTree(child, callback);
        }
    }

    // Get the path from root to a specific symbol
    public getPathToSymbol(symbolName: string): CallNode[] {
        const node = this.findNodeBySymbol(symbolName);
        if (!node) {
            return [];
        }

        const path: CallNode[] = [];
        let current: CallNode | null = node;
        
        while (current && current !== this.root) {
            path.unshift(current);
            current = current.parent;
        }
        
        return path;
    }

    // Clear the tree
    public clear(): void {
        this.root.children = [];
        this.nodeMap.clear();
    }

    // Find the parent symbol of a given symbol
    public findParentSymbol(symbolName: string): vscode.CallHierarchyItem | null {
        const node = this.findNodeBySymbol(symbolName);
        if (!node || !node.parent || node.parent === this.root) {
            return null;
        }
        
        return node.parent.symbol;
    }

    // Find the root symbol of a given symbol
    public findRootSymbol(symbolName: string): vscode.CallHierarchyItem | null {
        const node = this.findNodeBySymbol(symbolName);
        if (!node) {
            return null;
        }

        let current: CallNode | null = node;
        while (current && current.parent !== this.root && current.parent != current) {
            current = current.parent;
        }
        return current?.symbol ?? null;
    }
} 