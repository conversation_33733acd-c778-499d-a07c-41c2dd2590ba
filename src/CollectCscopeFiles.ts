import * as fs from 'fs';
import * as path from 'path';
import * as vscode from 'vscode';
import { outputChannel } from './UtilFuns';
import { buildDatabase, DatabaseType } from './BuildDatabase';
import { g_macroDefinitionManager } from './MacroDefinition';

/**
 * A class to collect and generate cscope.files for code navigation
 */
export class CscopeFilesCollector {
    private srcRoot: string;
    private objRoot: string;
    private fileMap: Map<string, boolean>;
    private fileList: string[];
    private finalMap: Map<string, boolean>;

    constructor() {
        this.srcRoot = '';
        this.objRoot = '';
        this.fileMap = new Map<string, boolean>();
        this.fileList = [];
        this.finalMap = new Map<string, boolean>();
    }

    /**
     * Save a list of files to a file
     * @param filename The output file name
     * @param listName The list of files to save
     */
    private saveListToFile(filename: string, listName: string[]): void {
        try {
            const fileContent = listName.join('\n') + '\n' +
                path.join(this.objRoot, 'include/generated/autoconf.h');
            fs.writeFileSync(filename, fileContent);
        } catch (error) {
            outputChannel.appendLine(`Error saving file list: ${error instanceof Error ? error.message : String(error)}`);
            throw error;
        }
    }

    /**
     * Collect source files from a .cmd file
     * @param fname The .cmd file name
     */
    private collectSrc(fname: string): void {
        try {
            const content = fs.readFileSync(fname, 'utf8');
            const lines = content.split('\n');

            for (const line of lines) {
                // Replace closing parentheses with spaces
                const processedLine = line.replace(/\)/g, ' ').replace(/\n/g, ' ');
                const items = processedLine.split(' ');

                for (const item of items) {
                    // Check if file has .h, .c, or .S extension but not .mod.c
                    if (
                        (item.endsWith('.h') || item.endsWith('.c') || item.endsWith('.S')) &&
                        !item.endsWith('.mod.c')
                    ) {
                        this.fileMap.set(item, true);
                    }
                }
            }
        } catch (error) {
            outputChannel.appendLine(`Error collecting source from ${fname}: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Make cscope.files from collected files
     */
    private makeCscopeFiles(): void {
        // Process all files in the file map
        for (const fname of this.fileMap.keys()) {
            const normalizedPath = path.normalize(fname);

            if (normalizedPath.startsWith('/')) {
                // Handle absolute paths
                if (normalizedPath.startsWith(this.srcRoot)) {
                    const relativePath = normalizedPath.substring(this.srcRoot.length + 1);
                    this.finalMap.set(relativePath, true);
                }
            } else {
                // Handle relative paths
                this.finalMap.set(normalizedPath, true);
            }
        }

        // Build the final file list
        for (const name of this.finalMap.keys()) {
            const srcPath = path.join(this.srcRoot, name);
            const objPath = path.join(this.objRoot, name);

            if (!fs.existsSync(srcPath)) {
                if (!fs.existsSync(objPath)) {
                    continue;
                } else {
                    // Skip files in include/config
                    if (name.includes('include/config')) {
                        continue;
                    }
                    this.fileList.push(objPath);
                }
            } else {
                this.fileList.push(name);
            }
        }

        // Sort the file list
        this.fileList.sort();

        // Save the file list to cscope.files
        const outputFile = path.join(this.srcRoot, 'cscope.files');
        this.saveListToFile(outputFile, this.fileList);
        outputChannel.appendLine(`${outputFile} is created in srcroot.`);
    }

    /**
     * Main function to collect cscope files
     * @param srcRoot The source root directory
     * @param objRoot The object root directory
     */
    public async collectCscopeFiles(srcRoot: string, objRoot: string): Promise<void> {
        this.srcRoot = path.resolve(srcRoot);
        this.objRoot = path.resolve(objRoot);

        // Check if paths exist
        for (const dirPath of [this.srcRoot, this.objRoot]) {
            if (!fs.existsSync(dirPath)) {
                throw new Error(`${dirPath} doesn't exist`);
            }
        }

        outputChannel.appendLine(`srcroot: ${this.srcRoot}`);
        outputChannel.appendLine(`objroot: ${this.objRoot}`);

        // Walk through the object directory to find .cmd files
        await this.walkDirectory(this.objRoot);

        // Check for .missing-syscalls.d file
        const missingFile = path.join(this.objRoot, '.missing-syscalls.d');
        if (fs.existsSync(missingFile)) {
            this.collectSrc(missingFile);
        }

        // Generate cscope.files
        this.makeCscopeFiles();
    }

    /**
     * Walk through a directory recursively to find .cmd files
     * @param directory The directory to walk
     */
    private async walkDirectory(directory: string): Promise<void> {
        try {
            const entries = fs.readdirSync(directory, { withFileTypes: true });

            for (const entry of entries) {
                const fullPath = path.join(directory, entry.name);

                if (entry.isDirectory()) {
                    // Recursively walk subdirectories
                    await this.walkDirectory(fullPath);
                } else if (entry.isFile() && entry.name.endsWith('.cmd')) {
                    // Process .cmd files
                    this.collectSrc(fullPath);
                }
            }
        } catch (error) {
            outputChannel.appendLine(`Error walking directory ${directory}: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}

// The collectCscopeFiles function has been moved and enhanced below

/**
 * Show a dialog box with input fields for kernel output folder, cscope.files location, and macro definition file
 */
export async function showConfigDialog(): Promise<void> {
    try {
        // Create a WebviewPanel
        const panel = vscode.window.createWebviewPanel(
            'sourceseekConfig',
            'SourceSeek Configuration',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        // Get default paths
        const workspacePath = vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0
            ? vscode.workspace.workspaceFolders[0].uri.fsPath
            : '';

        // Default kernel output folder
        const defaultKernelOutput = path.join(workspacePath, 'out/debug-gcc_12.2-64/build/kernel/');

        // Default cscope.files location
        const defaultCscopeFiles = path.join(workspacePath, './');

        // Default macro definition file
        const defaultMacroFile = path.join(workspacePath, 'out/debug-gcc_12.2-64/build/kernel/include/generated/autoconf.h');

        // Get saved values from workspace settings
        const config = vscode.workspace.getConfiguration('sourceseek');
        const savedKernelOutput = config.get<string>('kernelOutputPath') || defaultKernelOutput;
        const savedCscopeFiles = config.get<string>('cscopeFilesPath') || defaultCscopeFiles;
        const savedMacroFile = config.get<string>('macroDefinitionFilePath') || defaultMacroFile;

        // Create HTML content for the webview
        panel.webview.html = `
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>SourceSeek Configuration</title>
                <style>
                    body {
                        font-family: var(--vscode-font-family);
                        padding: 20px;
                        color: var(--vscode-foreground);
                    }
                    .container {
                        display: flex;
                        flex-direction: column;
                        gap: 15px;
                    }
                    .input-group {
                        display: flex;
                        flex-direction: column;
                        gap: 5px;
                    }
                    .input-row {
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }
                    input[type="text"] {
                        flex-grow: 1;
                        padding: 5px;
                        background-color: var(--vscode-input-background);
                        color: var(--vscode-input-foreground);
                        border: 1px solid var(--vscode-input-border);
                    }
                    button {
                        padding: 5px 10px;
                        background-color: var(--vscode-button-background);
                        color: var(--vscode-button-foreground);
                        border: none;
                        cursor: pointer;
                    }
                    button:hover {
                        background-color: var(--vscode-button-hoverBackground);
                    }
                    .action-buttons {
                        display: flex;
                        justify-content: flex-end;
                        gap: 10px;
                        margin-top: 20px;
                    }
                    label {
                        font-weight: bold;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <h2>SourceSeek Configuration</h2>

                    <div class="input-group">
                        <label for="kernelOutput">Kernel Output Folder:</label>
                        <div class="input-row">
                            <input type="text" id="kernelOutput" value="${savedKernelOutput}" />
                            <button id="browseKernelOutput">Browse...</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="cscopeFiles">Cscope Files Location:</label>
                        <div class="input-row">
                            <input type="text" id="cscopeFiles" value="${savedCscopeFiles}" />
                            <button id="browseCscopeFiles">Browse...</button>
                        </div>
                    </div>

                    <div class="input-group">
                        <label for="macroFile">Macro Definition File:</label>
                        <div class="input-row">
                            <input type="text" id="macroFile" value="${savedMacroFile}" />
                            <button id="browseMacroFile">Browse...</button>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button id="buildDatabase">Build Database</button>
                        <button id="saveButton">Save & Close</button>
                        <button id="cancelButton">Cancel</button>
                    </div>
                </div>

                <script>
                    const vscode = acquireVsCodeApi();

                    // Button click handlers
                    document.getElementById('browseKernelOutput').addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'browseKernelOutput'
                        });
                    });

                    document.getElementById('browseCscopeFiles').addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'browseCscopeFiles'
                        });
                    });

                    document.getElementById('browseMacroFile').addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'browseMacroFile'
                        });
                    });

                    document.getElementById('buildDatabase').addEventListener('click', () => {
                        const kernelOutput = document.getElementById('kernelOutput').value;
                        const cscopeFiles = document.getElementById('cscopeFiles').value;
                        const macroFile = document.getElementById('macroFile').value;

                        vscode.postMessage({
                            command: 'buildDatabase',
                            kernelOutput,
                            cscopeFiles,
                            macroFile
                        });
                    });

                    document.getElementById('saveButton').addEventListener('click', () => {
                        const kernelOutput = document.getElementById('kernelOutput').value;
                        const cscopeFiles = document.getElementById('cscopeFiles').value;
                        const macroFile = document.getElementById('macroFile').value;

                        vscode.postMessage({
                            command: 'save',
                            kernelOutput,
                            cscopeFiles,
                            macroFile
                        });
                    });

                    document.getElementById('cancelButton').addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'cancel'
                        });
                    });

                    // Handle messages from the extension
                    window.addEventListener('message', event => {
                        const message = event.data;

                        switch (message.command) {
                            case 'updateKernelOutput':
                                document.getElementById('kernelOutput').value = message.path;
                                break;
                            case 'updateCscopeFiles':
                                document.getElementById('cscopeFiles').value = message.path;
                                break;
                            case 'updateMacroFile':
                                document.getElementById('macroFile').value = message.path;
                                break;
                        }
                    });
                </script>
            </body>
            </html>
        `;

        // Handle messages from the webview
        panel.webview.onDidReceiveMessage(async message => {
            switch (message.command) {
                case 'browseKernelOutput':
                    const kernelOutputUri = await vscode.window.showOpenDialog({
                        canSelectFiles: false,
                        canSelectFolders: true,
                        canSelectMany: false,
                        openLabel: 'Select Kernel Output Folder',
                        defaultUri: vscode.Uri.file(savedKernelOutput)
                    });

                    if (kernelOutputUri && kernelOutputUri.length > 0) {
                        panel.webview.postMessage({
                            command: 'updateKernelOutput',
                            path: kernelOutputUri[0].fsPath
                        });
                    }
                    break;

                case 'browseCscopeFiles':
                    const cscopeFilesUri = await vscode.window.showOpenDialog({
                        canSelectFiles: false,
                        canSelectFolders: true,
                        canSelectMany: false,
                        openLabel: 'Select Cscope Files Location',
                        defaultUri: vscode.Uri.file(savedCscopeFiles)
                    });

                    if (cscopeFilesUri && cscopeFilesUri.length > 0) {
                        panel.webview.postMessage({
                            command: 'updateCscopeFiles',
                            path: cscopeFilesUri[0].fsPath
                        });
                    }
                    break;

                case 'browseMacroFile':
                    const macroFileUri = await vscode.window.showOpenDialog({
                        canSelectFiles: true,
                        canSelectFolders: false,
                        canSelectMany: false,
                        filters: {
                            'Header Files': ['h'],
                            'All Files': ['*']
                        },
                        openLabel: 'Select Macro Definition File',
                        defaultUri: vscode.Uri.file(savedMacroFile)
                    });

                    if (macroFileUri && macroFileUri.length > 0) {
                        panel.webview.postMessage({
                            command: 'updateMacroFile',
                            path: macroFileUri[0].fsPath
                        });
                    }
                    break;

                case 'buildDatabase':
                    try {
                        // Save the paths to workspace settings
                        const config = vscode.workspace.getConfiguration('sourceseek');
                        await config.update('kernelOutputPath', message.kernelOutput, vscode.ConfigurationTarget.Workspace);
                        await config.update('cscopeFilesPath', message.cscopeFiles, vscode.ConfigurationTarget.Workspace);
                        await config.update('macroDefinitionFilePath', message.macroFile, vscode.ConfigurationTarget.Workspace);

                        // Generate cscope.files if needed
                        if (!fs.existsSync(path.join(message.cscopeFiles, 'cscope.files'))) {
                            await collectCscopeFiles(message.cscopeFiles, message.kernelOutput);
                        }
                        
                        // Load macro definition file
                        if (message.macroFile && fs.existsSync(message.macroFile)) {
                            await g_macroDefinitionManager.loadFromFile(message.macroFile);
                        }

                        // Build the database
                        await buildDatabase(DatabaseType.BOTH, message.macroFile);

                        vscode.window.showInformationMessage('Database built successfully');
                    } catch (error) {
                        vscode.window.showErrorMessage(`Failed to build database: ${error instanceof Error ? error.message : String(error)}`);
                    }
                    break;

                case 'save':
                    try {
                        // Save the paths to workspace settings
                        const config = vscode.workspace.getConfiguration('sourceseek');
                        await config.update('kernelOutputPath', message.kernelOutput, vscode.ConfigurationTarget.Workspace);
                        await config.update('cscopeFilesPath', message.cscopeFiles, vscode.ConfigurationTarget.Workspace);
                        await config.update('macroDefinitionFilePath', message.macroFile, vscode.ConfigurationTarget.Workspace);

                        vscode.window.showInformationMessage('Settings saved successfully');
                        panel.dispose();
                    } catch (error) {
                        vscode.window.showErrorMessage(`Failed to save settings: ${error instanceof Error ? error.message : String(error)}`);
                    }
                    break;

                case 'cancel':
                    panel.dispose();
                    break;
            }
        });
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to show configuration dialog: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Collect cscope files with specified source and object directories
 * @param srcRoot Source root directory
 * @param objRoot Object root directory
 */
export async function collectCscopeFiles(srcRoot?: string, objRoot?: string): Promise<void> {
    try {
        // If srcRoot and objRoot are not provided, ask for them
        if (!srcRoot) {
            srcRoot = await vscode.window.showOpenDialog({
                canSelectFiles: false,
                canSelectFolders: true,
                canSelectMany: false,
                openLabel: 'Select Kernel Src',
                title: 'Select Kernel Src',
                defaultUri: vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0
                    ? vscode.Uri.file(path.join(vscode.workspace.workspaceFolders[0].uri.fsPath, 'kernel'))
                    : undefined
            }).then(uris => {
                if (uris && uris.length > 0) {
                    return uris[0].fsPath;
                }
                return undefined;
            });

            if (!srcRoot) {
                return; // User cancelled
            }
        }

        // If srcRoot is not an absolute path, add workspace path as prefix
        if (!path.isAbsolute(srcRoot) && vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
            const workspacePath = vscode.workspace.workspaceFolders[0].uri.fsPath;
            const absoluteSrcRoot = path.join(workspacePath, srcRoot);
            outputChannel.appendLine(`Converting relative path ${srcRoot} to absolute path ${absoluteSrcRoot}`);
            srcRoot = absoluteSrcRoot;
        }

        if (!objRoot) {
            objRoot = await vscode.window.showOpenDialog({
                canSelectFiles: false,
                canSelectFolders: true,
                canSelectMany: false,
                openLabel: 'Select Kernel Object Path',
                title: 'Select Kernel Object Path',
                defaultUri: vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0
                    ? vscode.Uri.file(path.join(vscode.workspace.workspaceFolders[0].uri.fsPath, 'out/debug-gcc_12.2-64/build/kernel/'))
                    : undefined
            }).then(uris => {
                if (uris && uris.length > 0) {
                    return uris[0].fsPath;
                }
                return undefined;
            });

            if (!objRoot) {
                return; // User cancelled
            }
        }

        // If objRoot is not an absolute path, add workspace path as prefix
        if (!path.isAbsolute(objRoot) && vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {
            const workspacePath = vscode.workspace.workspaceFolders[0].uri.fsPath;
            const absoluteObjRoot = path.join(workspacePath, objRoot);
            outputChannel.appendLine(`Converting relative path ${objRoot} to absolute path ${absoluteObjRoot}`);
            objRoot = absoluteObjRoot;
        }

        // Show progress
        await vscode.window.withProgress({
            location: vscode.ProgressLocation.Notification,
            title: 'Collecting cscope files...',
            cancellable: true
        }, async () => {
            // Call the Python script with the selected paths
            const { exec } = require('child_process');
            // Get the extension context from the function parameter
            // We need to pass the context from the registerCollectCscopeFilesCommand function
            const scriptPath = path.join(vscode.extensions.getExtension('lingxf.SourceSeek')?.extensionPath || '', 'collect_cscope_files.py');

            outputChannel.appendLine(`Executing: python ${scriptPath} "${srcRoot}" "${objRoot}"`);

            await new Promise<void>((resolve, reject) => {
                exec(`python3 ${scriptPath} "${srcRoot}" "${objRoot}"`, (error: any, stdout: string, stderr: string) => {
                    if (error) {
                        outputChannel.appendLine(`Error: ${error.message}`);
                        reject(error);
                        return;
                    }

                    if (stderr) {
                        outputChannel.appendLine(`stderr: ${stderr}`);
                    }

                    outputChannel.appendLine(`stdout: ${stdout}`);
                    resolve();
                });
            });
        });

        vscode.window.showInformationMessage('cscope.files generated successfully');
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to collect cscope files: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Register the collect cscope files command
 * @param context The extension context
 */
export function registerCollectCscopeFilesCommand(context: vscode.ExtensionContext): void {
    // Register the command to collect cscope files
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.collectCscopeFiles', () => collectCscopeFiles())
    );

    // Register the command to show the configuration dialog
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.showConfigDialog', showConfigDialog)
    );
}