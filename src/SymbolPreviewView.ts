import * as vscode from 'vscode';
import * as path from 'path';
import { ReadtagsProvider } from './ReadTags';
import { filterSymbolInfos, getFunctionCode, outputChannel, wrapExec } from './UtilFuns';
import { g_browserHistory} from './extension';
import { g_browserHistoryViewProvider } from './BrowserHistoryView';
import { EXTENSION_ID, g_findDefinitionTrigger, setFindDefinitionTrigger } from './globalSettings';
import { findDefinition } from './CallHierarchyProvider';
const { promisify } = require('util');
const { exec } = require('child_process');

// 跟踪上次打开的定义，避免重复打开
let lastOpenedDefinition = {
    uri: '',
    line: -1,
    character: -1,
    timestamp: 0
};

// 防抖延迟时间（毫秒）
const DEBOUNCE_DELAY = 500;

// 控制是否在新窗口中打开符号定义
let g_openInNewWindow = false;
let g_hoverPreviewEnabled = true;
let g_hoverCallerEnabled = false;
let g_hoverCallerClickTrigger = false;
let g_hoverAddHistory = false;
let g_openRightWindow = true;
export let g_hoverCallTrigger = false;
export let g_callhierarchyAddHistory = true; 
 
export let g_fnstructOnly = false;  // New flag for function structure only mode
export let g_checkSymbolKind = false; // New flag for checking symbol kind
// 跟踪上次打开的编辑器和标签页
let lastSymbolEditor: vscode.TextEditor | undefined;
let lastSymbolViewColumn: vscode.ViewColumn | undefined;

// 状态栏项目
let symbolModeStatusBarItem: vscode.StatusBarItem;

export function setHoverCallTrigger(trigger: boolean) {
    g_hoverCallTrigger = trigger;
}

/**
 * 从配置中加载设置
 */
function loadSettings() {
    const config = vscode.workspace.getConfiguration(EXTENSION_ID);
    g_hoverPreviewEnabled = config.get('hoverPreviewEnabled', true);
    g_openInNewWindow = config.get('openInNewWindow', false);
    g_hoverAddHistory = config.get('hoverAddHistory', false);
    g_openRightWindow = config.get('openRightWindow', true);
    g_callhierarchyAddHistory = config.get('callhierarchyAddHistory', true);
    g_fnstructOnly = config.get('fnstructOnly', false);  // Load function structure only setting
    g_checkSymbolKind = config.get('checkSymbolKind', false); // Load check symbol kind setting
    g_hoverCallerEnabled = config.get('hoverCallerEnabled', false); // Load new setting
    g_hoverCallerClickTrigger = config.get('hoverCallerClickTrigger', false); // Load hover caller click trigger setting
    // 更新状态栏
    updateStatusBar();
}

/**
 * 保存设置到配置
 */
async function saveSettings() {
    const config = vscode.workspace.getConfiguration(EXTENSION_ID);
    await config.update('hoverPreviewEnabled', g_hoverPreviewEnabled, true);
    await config.update('openInNewWindow', g_openInNewWindow, true);
    await config.update('hoverAddHistory', g_hoverAddHistory, true);
    await config.update('openRightWindow', g_openRightWindow, true);
    await config.update('callhierarchyAddHistory', g_callhierarchyAddHistory, true);
    await config.update('fnstructOnly', g_fnstructOnly, true);  // Save function structure only setting
    await config.update('checkSymbolKind', g_checkSymbolKind, true); // Save check symbol kind setting
    await config.update('hoverCallerEnabled', g_hoverCallerEnabled, true); // Save new setting
    await config.update('hoverCallerClickTrigger', g_hoverCallerClickTrigger, true); // Save hover caller click trigger setting
}

/**
 * 更新状态栏显示
 */
function updateStatusBar() {
    if (symbolModeStatusBarItem) {
        symbolModeStatusBarItem.text = `$(settings-gear) SourceSeek`;

        // 构建工具提示文本，显示当前状态
        g_openInNewWindow = !vscode.workspace.getConfiguration('workbench.editor').get('enablePreview');
        const tooltipItems = [
            `${g_hoverPreviewEnabled ? 'X' : '-'} Enable Preview`,
            `${!g_openInNewWindow ? 'X' : '-'} Same Tab to Preview`,
            `${g_hoverAddHistory ? 'X' : '-'} Hover add to History`,
            `${g_openRightWindow ? 'X' : '-'} Open in Right Window`,
            `${g_callhierarchyAddHistory ? 'X' : '-'} Call Hierarchy Add History`,
            `${g_fnstructOnly ? 'X' : '-'} Function Structure Only`,  // Add to tooltip
            `${g_checkSymbolKind ? 'X' : '-'} Check Symbol Kind`,  // Add to tooltip
            `${g_hoverCallerEnabled ? 'X' : '-'} Enable Hover CallHierarchy`, // New menu item
            `${g_hoverCallerClickTrigger ? 'X' : '-'} Hover Caller Click Trigger` // Add hover caller click trigger to tooltip
        ];
        symbolModeStatusBarItem.tooltip = tooltipItems.join('\n');
    }
}

/**
 * 切换符号打开模式
 */
async function toggleSymbolOpenMode() {


    // 更新设置
    g_openInNewWindow = !g_openInNewWindow;

    // 同时切换预览模式
    await togglePreviewMode(!g_openInNewWindow);
    await saveSettings();

    updateStatusBar();

    // 显示通知
    const mode = g_openInNewWindow ? 'open in new tab' : 'reuse the same tab';
    vscode.window.showInformationMessage(`Symbol preview will now ${mode}`);
}

/**
 * 切换预览编辑器模式
 * @param enable true 启用预览模式，false 禁用预览模式
 */
async function togglePreviewMode(enable: boolean) {
    //await vscode.workspace.getConfiguration('workbench.editor').update('enablePreview', enable, true);
    const config = vscode.workspace.getConfiguration();
    // 修改 enablePreview 设置
    config.update(
        'workbench.editor.enablePreview', // 配置项名称
        enable,                          // 新值
        vscode.ConfigurationTarget.Workspace// 作用范围（Global/Workspace）
    ).then(() => {
        console.log(`enablePreview 已${enable ? '启用' : '禁用'}`);
    });
}

/**
 * 切换悬停预览模式
 * @param enable true 启用悬停预览，false 禁用悬停预览
 */
async function toggleHoverPreviewMode(enable: boolean) {
    g_hoverPreviewEnabled = enable;
    await saveSettings();
}

/**
 * 切换悬停添加历史记录
 * @param enable true 启用悬停添加历史记录，false 禁用悬停添加历史记录
 */
async function toggleHoverAddHistory(enable: boolean) {
    g_hoverAddHistory = enable;
    await saveSettings();
}

/**
 * 切换右窗口打开
 * @param enable true 启用右窗口打开，false 禁用右窗口打开
 */
async function toggleOpenRightWindow(enable: boolean) {
    g_openRightWindow = enable;
    await saveSettings();
}

/**
 * Toggle function structure only mode
 * @param enable true to enable function structure only mode, false to disable
 */
async function toggleFnStructOnly(enable: boolean) {
    g_fnstructOnly = enable;
    await saveSettings();
}

/**
 * Toggle call hierarchy add history mode
 * @param enable true to enable call hierarchy add history, false to disable
 */
async function toggleCallHierarchyAddHistory(enable: boolean) {
    g_callhierarchyAddHistory = enable;
    await saveSettings();
}

/**
 * Toggle check symbol kind mode
 * @param enable true to enable check symbol kind mode, false to disable
 */
async function toggleCheckSymbolKind(enable: boolean) {
    g_checkSymbolKind = enable;
    await saveSettings();
}

/**
 * 切换悬停调用层级
 * @param enable true 启用悬停调用层级，false 禁用悬停调用层级
 */
async function toggleHoverCallerEnabled(enable: boolean) {
    g_hoverCallerEnabled = enable;
    await saveSettings();
}

/**
 * 切换悬停调用层级点击触发
 * @param enable true 启用悬停调用层级点击触发，false 禁用悬停调用层级点击触发
 */
async function toggleHoverCallerClickTrigger(enable: boolean) {
    g_hoverCallerClickTrigger = enable;
    await saveSettings();
}

/**
 * 显示模式选择菜单
 */
async function showModeSelectionMenu() {
    const previewEnabled = vscode.workspace.getConfiguration('workbench.editor').get('enablePreview');

    const items = [
        {
            label: `${g_hoverPreviewEnabled ? '$(check) ' : '$(dash)'}Enable Preview`,
            description: g_hoverPreviewEnabled ? 'On' : 'Off',
            value: 'preview'
        },
        {
            label: `${!g_openInNewWindow ? '$(check) ' : '$(dash)'}Same Tab to Preview`,
            description: !g_openInNewWindow ? 'On' : 'Off',
            value: 'sameTab'
        },
        {
            label: `${g_hoverAddHistory ? '$(check) ' : '$(dash)'}Hover add to History`,
            description: g_hoverAddHistory ? 'On' : 'Off',
            value: 'hoverhistory'
        },
        {
            label: `${g_openRightWindow ? '$(check) ' : '$(dash)'}Open in Right Window`,
            description: g_openRightWindow ? 'On' : 'Off',
            value: 'rightwindow'
        },
        {
            label: `${g_fnstructOnly ? '$(check) ' : '$(dash)'}Function Structure Only`,
            description: g_fnstructOnly ? 'On' : 'Off',
            value: 'fnstructonly'
        },
        {
            label: `${g_callhierarchyAddHistory ? '$(check) ' : '$(dash)'}Call Hierarchy Add History`,
            description: g_callhierarchyAddHistory ? 'On' : 'Off',
            value: 'callhierarchyhistory'
        },
        {
            label: `${g_checkSymbolKind ? '$(check) ' : '$(dash)'}Check Symbol Kind`,
            description: g_checkSymbolKind ? 'On' : 'Off',
            value: 'checksymbolkind'
        },
        {
            label: `${g_hoverCallerEnabled ? '$(check) ' : '$(dash)'}Enable Hover CallHierarchy`,
            description: g_hoverCallerEnabled ? 'On' : 'Off',
            value: 'hovercallhierarchy'
        },
        {
            label: `${g_hoverCallerClickTrigger ? '$(check) ' : '$(dash)'}Hover Caller Click Trigger`,
            description: g_hoverCallerClickTrigger ? 'On' : 'Off',
            value: 'hovercallerclicktrigger'
        }
    ];

    const selected = await vscode.window.showQuickPick(items, {
        placeHolder: 'Select SourceSeek Mode'
    });

    if (selected) {
        switch (selected.value) {
            case 'preview':
                await toggleHoverPreviewMode(!g_hoverPreviewEnabled);
                break;
            case 'sameTab':
                //await togglePreviewMode(!previewEnabled);
                await toggleSymbolOpenMode();
                break;
            case 'hoverhistory':
                await toggleHoverAddHistory(!g_hoverAddHistory);
                break;
            case 'rightwindow':
                await toggleOpenRightWindow(!g_openRightWindow);
                break;
            case 'fnstructonly':
                await toggleFnStructOnly(!g_fnstructOnly);
                break;
            case 'callhierarchyhistory':
                await toggleCallHierarchyAddHistory(!g_callhierarchyAddHistory);
                break;
            case 'checksymbolkind':
                await toggleCheckSymbolKind(!g_checkSymbolKind);
                break;
            case 'hovercallhierarchy':
                await toggleHoverCallerEnabled(!g_hoverCallerEnabled);
                break;
            case 'hovercallerclicktrigger':
                await toggleHoverCallerClickTrigger(!g_hoverCallerClickTrigger);
                break;
        }
        updateStatusBar();
    }
}

export class SymbolPreviewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'symbolPreview';
    private _view?: vscode.WebviewView;
    private _readtagsProvider: ReadtagsProvider;
    private _disposables: vscode.Disposable[] = [];

    constructor(private readonly _extensionUri: vscode.Uri, context: vscode.ExtensionContext) {
        // 创建 ReadtagsProvider 实例
        this._readtagsProvider = new ReadtagsProvider(context, wrapExec(promisify(exec)));
    }



    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken
    ) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this._extensionUri]
        };

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

        // 添加消息处理
        webviewView.webview.onDidReceiveMessage(
            async (message) => {
                if (message.command === 'jumpToPosition') {
                    try {
                        // 打开文件并跳转到指定位置
                        const document = await vscode.workspace.openTextDocument(message.filePath);
                        const editor = await vscode.window.showTextDocument(document);

                        // 创建位置和选择
                        const position = new vscode.Position(message.line, 0);
                        const lineEnd = document.lineAt(message.line).range.end;

                        // 设置光标位置和视图
                        editor.selection = new vscode.Selection(position, position);
                        editor.revealRange(
                            new vscode.Range(position, lineEnd),
                            vscode.TextEditorRevealType.InCenter
                        );
                    } catch (error) {
                        console.error('jump to position error:', error);
                        vscode.window.showErrorMessage(`can not jump to file position: ${error instanceof Error ? error.message : String(error)}`);
                    }
                }
            },
            undefined,
            this._disposables
        );
    }

    public async updateContent(document: vscode.TextDocument, position: vscode.Position) {
        if (!this._view) {
            return;
        }

        // 获取当前悬停的单词范围
        const range = document.getWordRangeAtPosition(position);
        if (!range) {
            return;
        }

        const symbol = document.getText(range);

        try {
            // 使用 ReadtagsProvider 获取符号定义
            const symbolInfos = await this._readtagsProvider.getDefinition(document, position);
            const filteredSymbolInfos = filterSymbolInfos(symbolInfos);
            const definitions = filteredSymbolInfos.map(({ location }) => location);

            if (definitions && definitions.length > 0) {
                // 使用第一个定义位置
                const definition = definitions[0];
                const definitionUri = definition.uri;
                const definitionRange = definition.range;

                // 打开定义文件
                const definitionDocument = await vscode.workspace.openTextDocument(definitionUri);

                // 获取文件内容
                const fileContent = definitionDocument.getText();

                // 获取文件名
                const fileName = path.basename(definitionUri.fsPath);

                // 获取文件语言
                const languageId = definitionDocument.languageId;

                // 提取函数或方法代码
                const functionCode = _extractFunctionCode(definitionDocument, definitionRange.start.line);

                // 更新视图内容
                this._view.webview.postMessage({
                    type: 'update',
                    fileName: fileName,
                    filePath: definitionUri.fsPath,
                    symbol: symbol,
                    content: this._formatContent(fileContent),
                    line: definitionRange.start.line + 1, // 行号从1开始
                    lineContent: definitionDocument.lineAt(definitionRange.start.line).text,
                    keyword: symbol, // 使用符号作为关键词进行高亮
                    languageId: languageId
                });
            } else {
                // 如果没有找到定义，显示当前文件
                const fileName = path.basename(document.fileName);

                this._view.webview.postMessage({
                    type: 'update',
                    fileName: fileName,
                    filePath: document.uri.fsPath,
                    symbol: symbol,
                    content: this._formatContent(document.getText()),
                    line: position.line + 1,
                    lineContent: document.lineAt(position.line).text,
                    keyword: symbol,
                    languageId: document.languageId
                });
            }
        } catch (error) {
            console.error('get symbol definition error:', error);

            // 出错时显示当前文件
            const fileName = path.basename(document.fileName);

            this._view.webview.postMessage({
                type: 'update',
                fileName: fileName,
                filePath: document.uri.fsPath,
                symbol: symbol,
                content: this._formatContent(document.getText()),
                line: position.line + 1,
                lineContent: document.lineAt(position.line).text,
                keyword: symbol,
                languageId: document.languageId,
                message: `Error: ${error instanceof Error ? error.message : String(error)}`
            });
        }
    }


    private _formatContent(content: string): string {
        // 转义HTML特殊字符
        return content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Symbol Preview</title>
            <style>
                body {
                    font-family: var(--vscode-editor-font-family);
                    font-size: var(--vscode-editor-font-size);
                    padding: 0;
                    margin: 0;
                }
                .header {
                    padding: 10px;
                    border-bottom: 1px solid var(--vscode-panel-border);
                    background-color: var(--vscode-editor-background);
                    position: sticky;
                    top: 0;
                    z-index: 10;
                }
                .file-name {
                    font-weight: bold;
                    margin-bottom: 5px;
                }
                .symbol {
                    color: var(--vscode-symbolIcon-functionForeground);
                }
                .message {
                    color: var(--vscode-errorForeground);
                    margin-top: 5px;
                    display: none;
                }
                pre {
                    margin: 0;
                    padding: 0;
                    background-color: var(--vscode-editor-background);
                }
                .code-container {
                    display: flex;
                    line-height: 1.5;
                    cursor: pointer;
                }
                .code-container:hover {
                    background-color: var(--vscode-editor-hoverHighlightBackground);
                }
                .highlight {
                    background-color: var(--vscode-editor-lineHighlightBackground);
                    border-left: 2px solid var(--vscode-editor-lineHighlightBorder, #0077ff);
                }
                .line-number {
                    color: var(--vscode-editorLineNumber-foreground);
                    text-align: right;
                    padding-right: 10px;
                    min-width: 40px;
                    user-select: none;
                }
                .code-content {
                    white-space: pre;
                    flex: 1;
                }
                .keyword-highlight {
                    background-color: var(--vscode-editor-findMatchHighlightBackground, #ffdf0055);
                    border: 1px solid var(--vscode-editor-findMatchHighlightBorder, #ffdf0055);
                    border-radius: 2px;
                }

                /* 语法高亮样式 */
                .c-keyword {
                    color: var(--vscode-editor-foreground, #569cd6);
                    font-weight: bold;
                }
                .c-type {
                    color: var(--vscode-symbolIcon-typeParameter-foreground, #4ec9b0);
                }
                .c-string {
                    color: var(--vscode-editor-foreground, #ce9178);
                }
                .c-comment {
                    color: var(--vscode-editorLineNumber-foreground, #6a9955);
                    font-style: italic;
                }
                .c-number {
                    color: var(--vscode-symbolIcon-number-foreground, #b5cea8);
                }
                .c-preprocessor {
                    color: var(--vscode-symbolIcon-property-foreground, #c586c0);
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="file-name">File: <span id="fileName"></span></div>
                <div>Symbol: <span id="symbol" class="symbol"></span></div>
                <div id="message" class="message"></div>
            </div>
            <div id="content"></div>

            <script>
                const vscode = acquireVsCodeApi();
                let currentFilePath = '';

                // C语言关键字列表
                const cKeywords = [
                    'auto', 'break', 'case', 'char', 'const', 'continue', 'default', 'do', 'double',
                    'else', 'enum', 'extern', 'float', 'for', 'goto', 'if', 'int', 'long', 'register',
                    'return', 'short', 'signed', 'sizeof', 'static', 'struct', 'switch', 'typedef',
                    'union', 'unsigned', 'void', 'volatile', 'while'
                ];

                // C++关键字列表
                const cppKeywords = [
                    ...cKeywords, 'class', 'namespace', 'template', 'try', 'catch', 'new', 'delete',
                    'private', 'protected', 'public', 'this', 'virtual', 'friend', 'inline', 'operator',
                    'throw', 'bool', 'true', 'false', 'using', 'nullptr'
                ];

                // 高亮关键词的函数
                function highlightKeyword(text, keyword) {
                    if (!keyword || keyword.trim() === '') {
                        return text;
                    }

                    // 转义正则表达式特殊字符
                    const regex0 = new RegExp('/[.*+?^$\{\}()|[\]\\]/', 'g');
                    const escapedKeyword = keyword.replace(regex0, '\\$&');

                    // 创建正则表达式，匹配整个单词
                    const regex = new RegExp('\\\\b(' + escapedKeyword + ')\\\\b', 'gi');

                    // 替换关键词为带高亮的版本
                    return text.replace(regex, '<span class="keyword-highlight">$1</span>');
                }

                // 语法高亮函数
                function applySyntaxHighlighting(text, languageId) {
                    if (!languageId || (languageId !== 'c' && languageId !== 'cpp')) {
                        return text;
                    }

                    // 选择关键字列表
                    const keywords = languageId === 'cpp' ? cppKeywords : cKeywords;

                    // 高亮C/C++关键字
                    let result = text;
                    for (const keyword of keywords) {
                        const regex = new RegExp('\\\\b(' + keyword + ')\\\\b', 'g');
                        result = result.replace(regex, '<span class="c-keyword">$1</span>');
                    }

                    // 高亮字符串
                    result = result.replace(/(".*?")/g, '<span class="c-string">$1</span>');

                    // 高亮数字
                    result = result.replace(/\\b(\\d+)\\b/g, '<span class="c-number">$1</span>');

                    // 高亮预处理指令
                    result = result.replace(/(#\\w+)/g, '<span class="c-preprocessor">$1</span>');

                    // 高亮单行注释 (不完美，但足够简单的情况)
                    result = result.replace(/(\/\/.*?)$/gm, '<span class="c-comment">$1</span>');

                    return result;
                }

                window.addEventListener('message', event => {
                    const message = event.data;
                    switch (message.type) {
                        case 'update':
                            document.getElementById('fileName').textContent = message.fileName;
                            document.getElementById('symbol').textContent = message.symbol;

                            // 存储当前文件路径
                            currentFilePath = message.filePath || '';

                            // 显示消息（如果有）
                            const messageElement = document.getElementById('message');
                            if (message.message) {
                                messageElement.textContent = message.message;
                                messageElement.style.display = 'block';
                            } else {
                                messageElement.style.display = 'none';
                            }

                            // 创建代码显示
                            const contentDiv = document.getElementById('content');

                            // 将内容分割成行
                            const lines = message.content.split('\\n');

                            // 创建代码显示
                            let codeHtml = '';
                            for (let i = 0; i < lines.length; i++) {
                                const lineNumber = i + 1;
                                const isHighlighted = lineNumber === message.line;

                                // 应用语法高亮
                                let lineContent = lines[i];
                                if (message.languageId) {
                                    lineContent = applySyntaxHighlighting(lineContent, message.languageId);
                                }

                                // 高亮搜索关键词
                                if (message.keyword) {
                                    lineContent = highlightKeyword(lineContent, message.keyword);
                                }

                                codeHtml += '<div class="code-container' + (isHighlighted ? ' highlight' : '') + '" data-line="' + lineNumber + '">';
                                codeHtml += '<span class="line-number">' + lineNumber + '</span>';
                                codeHtml += '<span class="code-content">' + lineContent + '</span>';
                                codeHtml += '</div>';
                            }

                            contentDiv.innerHTML = '<pre>' + codeHtml + '</pre>';

                            // 添加点击事件处理
                            document.querySelectorAll('.code-container').forEach(element => {
                                element.addEventListener('click', function() {
                                    const lineNumber = parseInt(this.getAttribute('data-line'), 10) - 1; // 转为0-based
                                    if (currentFilePath) {
                                        vscode.postMessage({
                                            command: 'jumpToPosition',
                                            filePath: currentFilePath,
                                            line: lineNumber
                                        });
                                    }
                                });
                            });

                            // 滚动到高亮行
                            if (message.line) {
                                const highlightedElement = document.querySelector('.highlight');
                                if (highlightedElement) {
                                    highlightedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                }
                            }
                            break;

                        case 'notFound':
                            // 显示符号未找到的消息
                            document.getElementById('symbol').textContent = message.symbol;
                            document.getElementById('fileName').textContent = '';
                            document.getElementById('message').textContent = message.message;
                            document.getElementById('message').style.display = 'block';
                            document.getElementById('content').innerHTML = '';
                            currentFilePath = '';
                            break;

                        case 'error':
                            // 显示错误消息
                            document.getElementById('symbol').textContent = message.symbol;
                            document.getElementById('fileName').textContent = '';
                            document.getElementById('message').textContent = message.message;
                            document.getElementById('message').style.display = 'block';
                            document.getElementById('content').innerHTML = '';
                            currentFilePath = '';
                            break;
                    }
                });
            </script>
        </body>
        </html>`;
    }
}

export async function getSymbolDefinitions(document: vscode.TextDocument, position: vscode.Position, readtagsProvider: ReadtagsProvider): Promise<vscode.SymbolInformation[]> {
    try {
        // 获取当前悬停的单词范围
        const range = document.getWordRangeAtPosition(position);
        if (!range) {
            return [];
        }
        //const symbol = document.getText(range);

        // 检查是否与上次打开的定义相同（防抖）
        const currentTime = Date.now();
        const currentUri = document.uri.toString();
        if (
            currentUri === lastOpenedDefinition.uri &&
            position.line === lastOpenedDefinition.line &&
            currentTime - lastOpenedDefinition.timestamp < DEBOUNCE_DELAY
        ) {
            return []; // 跳过重复打开
        }

        // 更新上次打开的定义记录
        lastOpenedDefinition = {
            uri: currentUri,
            line: position.line,
            character: position.character,
            timestamp: currentTime
        };

        // 使用 ReadtagsProvider 获取符号定义
        let symbolInfos = await readtagsProvider.getDefinition(document, position);
        if (symbolInfos.length === 0) {
            // 如果没有找到定义，尝试获取函数指针定义
            const word = document.getText(range);
            symbolInfos = await findDefinition(word);
        }
        return symbolInfos;
    } catch (error) {
        console.error('获取符号定义时出错:', error);
        return [];
    }
}

/**
 * 在编辑窗口中打开符号定义
 * @param document 当前文档
 * @param position 符号位置
 * @param readtagsProvider 符号定义提供者
 */
async function openSymbolDefinitionInEditor(
    symbolInfos: vscode.SymbolInformation[],
    document: vscode.TextDocument,
    position: vscode.Position,
): Promise<boolean> {
    try {

        const definitions = symbolInfos.map(({ location }) => location);

        if (definitions && definitions.length > 0) {
            // 使用第一个定义位置
            const definition = definitions[0];
            const definitionUri = definition.uri;
            const definitionRange = definition.range;

            // 打开定义文件
            const definitionDocument = await vscode.workspace.openTextDocument(definitionUri);

            let editor: vscode.TextEditor;

            if (g_openInNewWindow) {
                // 在新窗口中打开
                editor = await vscode.window.showTextDocument(definitionDocument, {
                    viewColumn: vscode.ViewColumn.Beside,
                    preserveFocus: true // 保持焦点在原窗口
                });

                // 保存这个编辑器引用和视图列
                lastSymbolEditor = editor;
                lastSymbolViewColumn = editor.viewColumn;
            } else {
                // 在相同标签页中替换内容
                if (lastSymbolViewColumn) {
                    // 如果有上次的视图列，则在相同位置打开
                    editor = await vscode.window.showTextDocument(definitionDocument, {
                        viewColumn: lastSymbolViewColumn,
                        preserveFocus: true,
                        preview: true // 使用预览模式，这样会替换当前预览标签页
                    });
                } else {
                    // 如果没有上次的视图列，则在侧边打开
                    editor = await vscode.window.showTextDocument(definitionDocument, {
                        viewColumn: vscode.ViewColumn.Beside,
                        preserveFocus: true,
                        preview: true // 使用预览模式
                    });
                }

                // 更新上次打开的编辑器引用和视图列
                lastSymbolEditor = editor;
                lastSymbolViewColumn = editor.viewColumn;
            }

            // 高亮显示定义行
            const selectionRange = new vscode.Range(
                definitionRange.start,
                definitionRange.end
            );

            // 设置选择并滚动到视图中央
            editor.selection = new vscode.Selection(selectionRange.start, selectionRange.end);
            editor.revealRange(selectionRange, vscode.TextEditorRevealType.InCenter);

            // 添加到浏览历史
            if (g_browserHistory && g_hoverAddHistory) {
                try {
                    // 创建一个 CallHierarchyItem 来表示符号
                    const symbolItem = new vscode.CallHierarchyItem(
                        vscode.SymbolKind.Function, // 默认为函数类型
                        symbolInfos[0].name,
                        '', // 详情可以留空
                        definitionUri,
                        selectionRange,
                        selectionRange
                    );

                    // 添加到历史记录
                    g_browserHistory.addCallHierarchyItem(symbolItem);
                    g_browserHistoryViewProvider?.refresh();
                } catch (error) {
                    console.error('添加到浏览历史时出错:', error);
                }
            }

            return true;
        }

        return false;
    } catch (error) {
        console.error('打开符号定义时出错:', error);
        vscode.window.showErrorMessage(`无法打开符号定义: ${error instanceof Error ? error.message : String(error)}`);
        return false;
    }
}


/**
 * 从文档中提取包含指定行的函数或方法代码
 * @param document 文档
 * @param definitionLine 定义所在行
 * @returns 提取的代码和高亮行号
*/
function _extractFunctionCode(document: vscode.TextDocument, definitionLine: number): { code: string, highlightLine: number } {
const text = document.getText();
const lines = text.split('\n');

// 默认情况下，如果无法提取函数，则显示定义行周围的代码
const contextLines = 10; // 上下文行数
let startLine = Math.max(0, definitionLine - contextLines);
let endLine = Math.min(lines.length - 1, definitionLine + contextLines);

// 尝试找到函数的开始和结束
let bracketCount = 0;
let foundStart = false;
let functionStartLine = definitionLine;
let functionEndLine = definitionLine;

// 向上查找函数开始
for (let i = definitionLine; i >= 0; i--) {
    const line = lines[i];

    // 检查是否有左大括号
    if (line.includes('{')) {
        bracketCount++;
        if (!foundStart) {
            foundStart = true;
            functionStartLine = i;
        }
    }

    // 检查是否有右大括号
    if (line.includes('}')) {
        bracketCount--;
    }

    // 如果找到了函数声明行（通常在左大括号之前）
    if (foundStart && bracketCount === 0 &&
        /^\s*(public|private|protected|static|void|function|[A-Za-z0-9_]+\s+[A-Za-z0-9_]+\s*\()/.test(line)) {
        functionStartLine = i;
        break;
    }

    // 如果已经向上查找了很多行，但仍未找到函数开始，则停止查找
    if (foundStart && i < functionStartLine - 50) {
        break;
    }
}

// 向下查找函数结束
bracketCount = 0;
for (let i = functionStartLine; i < lines.length; i++) {
    const line = lines[i];

    // 计算大括号数量
    for (const char of line) {
        if (char === '{') bracketCount++;
        if (char === '}') bracketCount--;

        // 当大括号计数归零时，我们找到了函数结束
        if (bracketCount === 0 && i > functionStartLine && line.includes('}')) {
            functionEndLine = i;
            break;
        }
    }

    if (functionEndLine !== definitionLine) {
        break;
    }
}

// 如果找到了完整的函数，则使用函数的开始和结束行
if (functionStartLine !== definitionLine || functionEndLine !== definitionLine) {
    startLine = functionStartLine;
    endLine = functionEndLine;
}

// 提取代码
const codeLines = lines.slice(startLine, endLine + 1);
const code = codeLines.join('\n');

// 计算高亮行相对于提取代码的行号
const highlightLine = definitionLine - startLine + 1;

return { code, highlightLine };
}

async function getSymbolDefinitionCode(symbolInfos: vscode.SymbolInformation[]): Promise<vscode.MarkdownString> {
    try {
        if (symbolInfos && symbolInfos.length > 0) {
            // Use the first definition location
            const definition = symbolInfos[0].location;
            const definitionUri = definition.uri;
            const definitionRange = definition.range;

            // Open definition file but don't show
            const definitionDocument = await vscode.workspace.openTextDocument(definitionUri);

            // Get definition line and context
            const startLine = Math.max(0, definitionRange.start.line);
            const codeText = getFunctionCode(definitionDocument, startLine + 1);
            const relPath = vscode.Uri.file(path.relative(vscode.workspace.workspaceFolders![0].uri.fsPath, definitionUri.fsPath));
            
            // Create a proper markdown code block with language identifier
            const language = definitionDocument.languageId || 'c';
            
            // Create command arguments as a JSON string
            const args = JSON.stringify({
                uri: definitionUri.toString(),
                line: definitionRange.start.line,
                character: definitionRange.start.character
            });

            // Create a markdown string with a clickable link
            const mdString = new vscode.MarkdownString();

            // Add file path and clickable link
            const filepath = relPath.path;
            const openstr = `(command:symbolPreview.openInRightWindow?${encodeURIComponent(args)})`;
            mdString.appendMarkdown(`### [${filepath}]${openstr}\n`);
            mdString.appendCodeblock(codeText, language);

            // Enable command execution in the markdown
            mdString.isTrusted = true;

            return mdString;
        }
    } catch (error: any) {
        console.error('open symbol definition error:', error);
    }
    return new vscode.MarkdownString("Not found");
}

/**
 * 处理鼠标悬停和点击的组合事件
 * @param editor 当前活动的文本编辑器
 */
function handleHoverAndClick(editor: vscode.TextEditor) {
    const position = editor.selection.active;
    const document = editor.document;
    if (g_hoverCallerEnabled && g_hoverCallerClickTrigger) {
        const range = document.getWordRangeAtPosition(position);
    
        if (range ) {
            // 在点击时立即恢复焦点到编辑器
            let docEditor = vscode.window.activeTextEditor;
            if (docEditor) {
                const originalEditor = docEditor;
                // 执行调用层级命令

                const cursorPosition = g_hoverPosition;
                const hoverRange = document.getWordRangeAtPosition(cursorPosition);
                if ( range.start.line == hoverRange?.start.line &&
                    range.start.character == hoverRange?.start.character
                 ) {
                    //   Math.abs(cursorPosition.character - position.character) < 8) {
                    // 位置匹配，执行命令
                    const originalEditor = docEditor;
                    g_hoverCallTrigger = true;
                    // 执行调用层级命令
                    vscode.commands.executeCommand('references-view.showCallHierarchy');
                    // 延迟1秒后恢复焦点到原始编辑器
                    if (g_findDefinitionTrigger) {
                        setFindDefinitionTrigger(false);
                    } else {
                        setTimeout(() => {
                            vscode.window.showTextDocument(originalEditor.document, {
                                viewColumn: originalEditor.viewColumn,
                                preserveFocus: false // 强制聚焦
                            });
                        }, 1000); // 1秒延迟
                    }
                }
            }
        }
    }
}

let g_hoverThreads = 0;
let g_isHovering = false; // 跟踪鼠标悬停状态
let g_hoverPosition:vscode.Position;

export function registerSymbolPreviewView(context: vscode.ExtensionContext) {
    // 加载设置
    loadSettings();

    // 创建 ReadtagsProvider 实例
    const readtagsProvider = new ReadtagsProvider(context,wrapExec(promisify(exec)));

    // 监听鼠标点击事件
    context.subscriptions.push(
        vscode.window.onDidChangeTextEditorSelection(e => {
            if (g_isHovering ) {
                // 如果当前正在悬停并且发生了点击，触发自定义事件处理
                g_isHovering = false;
                handleHoverAndClick(e.textEditor);
            }
        })
    );

    // 注册视图提供者
    /*
    const provider = new SymbolPreviewProvider(context.extensionUri);
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(SymbolPreviewProvider.viewType, provider)
    );
    */

    // 创建状态栏项目
    symbolModeStatusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    symbolModeStatusBarItem.command = 'symbolPreview.showModeMenu';
    updateStatusBar();
    symbolModeStatusBarItem.show();
    context.subscriptions.push(symbolModeStatusBarItem);

    // 注册菜单命令
    context.subscriptions.push(
        vscode.commands.registerCommand('symbolPreview.showModeMenu', showModeSelectionMenu)
    );

    // 监听编辑器变化，更新 lastSymbolEditor 和 lastSymbolViewColumn
    context.subscriptions.push(
        vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor && editor.viewColumn === vscode.ViewColumn.Beside) {
                lastSymbolEditor = editor;
                lastSymbolViewColumn = editor.viewColumn;
            }
        })
    );

    // 监听鼠标悬停事件
    context.subscriptions.push(
        vscode.languages.registerHoverProvider(['c', 'cpp'], {
            async provideHover(document, position, token) {
                g_isHovering = true; // 设置悬停状态
                
                // 创建一个延时器，在悬停结束后重置状态
                g_hoverPosition = position;

                // 原有的悬停处理逻辑
                if (g_hoverThreads > 1) {
                    outputChannel.appendLine("g_hoverThreads > 1: " + g_hoverThreads);
                    console.log("g_hoverThreads > 1:", g_hoverThreads);
                    return null;
                }
                g_hoverThreads++;

                let docEditor = vscode.window.activeTextEditor;
                // Find the editor containing this document and get its view column
                const visibleEditors = vscode.window.visibleTextEditors.filter(
                    editor => editor.document.uri.toString() === document.uri.toString()
                );
                if (visibleEditors.length > 1) {
                    // 比较可见范围 - 假设分屏显示文档的不同部分
                    docEditor = visibleEditors.find(e =>
                        e.visibleRanges.some(r =>
                            position.line >= r.start.line &&
                            position.line <= r.end.line
                        )
                    );

                    if (docEditor) {
                        const side = docEditor.viewColumn === vscode.ViewColumn.One ? '左' : '右';
                        console.log(`鼠标在${side}侧编辑器`);
                    }
                } else if (visibleEditors.length == 1) {
                    docEditor = visibleEditors[0];
                } else {

                }
                const currentViewColumn = docEditor?.viewColumn || lastSymbolViewColumn || vscode.ViewColumn.Two;
                console.log("Hover in document at view column:", currentViewColumn);
                
                const range = document.getWordRangeAtPosition(position);
                if (range) {
                    const word = document.getText(range);
                    if (g_hoverCallerEnabled && !g_hoverCallerClickTrigger) {
                        // 先设置焦点并保持，异步执行命令并在延迟后恢复焦点
                        if (docEditor ) {
                            // 检查当前光标位置与悬停位置是否相同
                            const cursorPosition = docEditor.selection.active;
                            const hoverRange = document.getWordRangeAtPosition(cursorPosition);
                            if (range.start.line == hoverRange?.start.line &&
                                range.start.character == hoverRange?.start.character
                            ) {
                                // 位置匹配，执行命令
                                const originalEditor = docEditor;
                                g_hoverCallTrigger = true;
                                // 执行调用层级命令
                                vscode.commands.executeCommand('references-view.showCallHierarchy');
                                // 延迟1秒后恢复焦点到原始编辑器
                                setTimeout(() => {
                                    vscode.window.showTextDocument(originalEditor.document, {
                                        viewColumn: originalEditor.viewColumn,
                                        preserveFocus: false // 强制聚焦
                                    });
                                }, 1000); // 1秒延迟
                            }
                        }
                    }
                    // Directly open symbol definition
                    if (g_hoverPreviewEnabled) {
                        const symbolInfos = await getSymbolDefinitions(document, position, readtagsProvider);
                        const filteredSymbolInfos = filterSymbolInfos(symbolInfos);
                        if (filteredSymbolInfos.length == 0) {
                            g_hoverThreads--;
                            return new vscode.Hover('');
                        }

                        const istips = filteredSymbolInfos[0].kind !== vscode.SymbolKind.Function;

                        if (g_openRightWindow && !istips && currentViewColumn !== vscode.ViewColumn.Two) {
                            openSymbolDefinitionInEditor(filteredSymbolInfos, document, position).catch(error => {
                                console.error('open symbol definition error:', error);
                            });
                        } else {
                            const mdString = await getSymbolDefinitionCode(filteredSymbolInfos).catch(error => {
                                console.error('open symbol definition error:', error);
                                g_hoverThreads--;
                                return new vscode.MarkdownString("Error loading definition");
                            });

                            g_hoverThreads--;
                            return new vscode.Hover(mdString);
                        }
                    }
                    
                    //g_hoverThreads--;
                    // Still return hover info, but without links
                    //return new vscode.Hover('');
                }            
                g_hoverThreads--;
                return null;
            }
        })
    );

    // 保留打开定义命令，以便可以从其他地方调用
    context.subscriptions.push(
        vscode.commands.registerCommand('symbolPreview.openDefinition', async (uriString: string, line: number, character: number) => {
            try {
                const uri = vscode.Uri.parse(uriString);
                const document = await vscode.workspace.openTextDocument(uri);
                const position = new vscode.Position(line, character);

                // 在编辑窗口中打开符号定义
                const symbolInfos = await getSymbolDefinitions(document, position, readtagsProvider);
                const filteredSymbolInfos = filterSymbolInfos(symbolInfos);
                if (filteredSymbolInfos.length == 0) {
                    return;
                }
                await openSymbolDefinitionInEditor(filteredSymbolInfos, document, position);
            } catch (error) {
                console.error('execute open definition command error:', error);
                vscode.window.showErrorMessage(`can not open definition: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );

    // 添加命令以手动聚焦视图
    context.subscriptions.push(
        vscode.commands.registerCommand('symbolPreview.focus', () => {
            vscode.commands.executeCommand('symbolPreview.focus');
        })
    );

    // 监听配置变化
    context.subscriptions.push(
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('sourceseek')) {
                loadSettings();
            }
        })
    );

    // Register the command to open in right window
    context.subscriptions.push(
        vscode.commands.registerCommand('symbolPreview.openInRightWindow', async (args) => {
            try {
                const uri = vscode.Uri.parse(args.uri);
                const position = new vscode.Position(args.line, args.character);

                // Open the document in the right editor group
                await vscode.commands.executeCommand('vscode.openWith', uri, 'default', {
                    viewColumn: vscode.ViewColumn.Two,
                    preserveFocus: false // Focus on the new window
                });

                // Get the active editor in the right editor group
                const rightEditor = vscode.window.visibleTextEditors.find(
                    e => e.viewColumn === vscode.ViewColumn.Two &&
                         e.document.uri.toString() === uri.toString()
                );

                if (rightEditor) {
                    // Reveal the range and position the cursor at the beginning of the symbol
                    rightEditor.revealRange(
                        new vscode.Range(position, position),
                        vscode.TextEditorRevealType.InCenter // Show in the middle of the window
                    );
                    rightEditor.selection = new vscode.Selection(position, position);
                } else {
                    vscode.window.showErrorMessage('Failed to open editor in right window');
                }

            } catch (error) {
                console.error('Failed to open in right window:', error);
                vscode.window.showErrorMessage(`Failed to open in right window: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );
}

// 导出用于在扩展停用时保存设置的函数
export async function saveSymbolPreviewSettings() {
    await saveSettings();
}