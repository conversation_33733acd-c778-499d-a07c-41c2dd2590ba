import * as vscode from 'vscode';

/**
 * Command handler for exporting call hierarchy to clipboard
 */
export async function exportCallHierarchy() {
    try {
        // First, try to get the references view panel
        // The references view is a custom view container in VS Code
        const referencesViewType = 'references-view';
        
        // Check for references-view or call hierarchy editors
        const referencesViewEditors = vscode.window.visibleTextEditors.filter(
            editor => editor.document.uri.scheme === referencesViewType || 
                     editor.document.uri.toString().includes(referencesViewType)
        );
        
        // If we found references view editors, check for call hierarchy content
        if (referencesViewEditors.length > 0) {
            for (const editor of referencesViewEditors) {
                const content = editor.document.getText();
                
                // Check if it's a call hierarchy view
                const isCallHierarchy = content.includes('Call Hierarchy:') || 
                                      content.includes('Callers of') || 
                                      content.includes('Calls to');
                                      
                if (isCallHierarchy) {
                    // Format and export the call hierarchy content
                    const exportContent = formatCallHierarchyContent(content);
                    await vscode.env.clipboard.writeText(exportContent);
                    vscode.window.showInformationMessage('Call hierarchy exported to clipboard');
                    return;
                }
            }
            
            // If we get here, it's a regular references view
            const referencesContent = referencesViewEditors[0].document.getText();
            const exportContent = formatReferencesContent(referencesContent);
            await vscode.env.clipboard.writeText(exportContent);
            vscode.window.showInformationMessage('References exported to clipboard');
            return;
        }
        
        // If we couldn't get the references view directly, try to get the active editor
        // and extract call hierarchy information from there
        const activeEditor = vscode.window.activeTextEditor;
        if (!activeEditor) {
            vscode.window.showInformationMessage('No active references view found');
            return;
        }
        
        const position = activeEditor.selection.active;
        const document = activeEditor.document;
        
        // Try to prepare call hierarchy
        const callHierarchyItems = await vscode.commands.executeCommand<any[]>(
            'vscode.prepareCallHierarchy',
            document.uri,
            position
        );
        
        if (callHierarchyItems && callHierarchyItems.length > 0) {
            // Get incoming calls
            const incomingCalls = await vscode.commands.executeCommand<any[]>(
                'vscode.provideIncomingCalls',
                callHierarchyItems[0]
            );
            
            // Get outgoing calls
            const outgoingCalls = await vscode.commands.executeCommand<any[]>(
                'vscode.provideOutgoingCalls',
                callHierarchyItems[0]
            );
            
            // Format the call hierarchy
            let exportContent = `# Call Hierarchy for ${callHierarchyItems[0].name}\n\n`;
            
            // Add the current item
            exportContent += `## Current Symbol\n`;
            exportContent += `- Name: ${callHierarchyItems[0].name}\n`;
            exportContent += `- File: ${callHierarchyItems[0].uri.fsPath}\n`;
            exportContent += `- Line: ${callHierarchyItems[0].range.start.line + 1}\n\n`;
            
            // Add incoming calls
            if (incomingCalls && incomingCalls.length > 0) {
                exportContent += `## Incoming Calls (${incomingCalls.length})\n\n`;
                incomingCalls.forEach((call, index) => {
                    exportContent += `### ${index + 1}. ${call.from.name}\n`;
                    exportContent += `- File: ${call.from.uri.fsPath}\n`;
                    exportContent += `- Line: ${call.from.range.start.line + 1}\n`;
                    if (call.from.detail) {
                        exportContent += `- Detail: ${call.from.detail}\n`;
                    }
                    exportContent += '\n';
                });
            }
            
            // Add outgoing calls
            if (outgoingCalls && outgoingCalls.length > 0) {
                exportContent += `## Outgoing Calls (${outgoingCalls.length})\n\n`;
                outgoingCalls.forEach((call, index) => {
                    exportContent += `### ${index + 1}. ${call.to.name}\n`;
                    exportContent += `- File: ${call.to.uri.fsPath}\n`;
                    exportContent += `- Line: ${call.to.range.start.line + 1}\n`;
                    if (call.to.detail) {
                        exportContent += `- Detail: ${call.to.detail}\n`;
                    }
                    exportContent += '\n';
                });
            }
            
            // Copy to clipboard
            await vscode.env.clipboard.writeText(exportContent);
            vscode.window.showInformationMessage('Call hierarchy exported to clipboard');
        } else {
            // Try to access the references view through the VS Code API
            try {
                const references = await vscode.commands.executeCommand<vscode.Location[]>(
                    'vscode.executeReferenceProvider',
                    document.uri,
                    position
                );
                
                if (references && references.length > 0) {
                    let exportContent = `# References for symbol at ${document.fileName}:${position.line + 1}:${position.character + 1}\n\n`;
                    
                    references.forEach((reference, index) => {
                        exportContent += `${index + 1}. ${reference.uri.fsPath}:${reference.range.start.line + 1}:${reference.range.start.character + 1}\n`;
                    });
                    
                    await vscode.env.clipboard.writeText(exportContent);
                    vscode.window.showInformationMessage('References exported to clipboard');
                } else {
                    vscode.window.showInformationMessage('No references found at current position');
                }
            } catch (error) {
                vscode.window.showInformationMessage('No references or call hierarchy data available at current position');
            }
        }
    } catch (error) {
        console.error('Error exporting references:', error);
        vscode.window.showErrorMessage(`Failed to export references: ${error}`);
    }
}

/**
 * Format call hierarchy content specifically
 * @param content The raw content from the call hierarchy view
 * @returns Formatted content for export
 */
function formatCallHierarchyContent(content: string): string {
    // Extract the title/header if present
    const lines = content.split('\n');
    let title = 'Call Hierarchy';
    
    // Find if there's a title line for the call hierarchy
    const titleLine = lines.find(line => 
        line.includes('Call Hierarchy:') || 
        line.startsWith('Callers of') || 
        line.startsWith('Calls to'));
    
    if (titleLine) {
        title = titleLine;
    }
    
    // Create a markdown formatted output
    let formattedContent = `# ${title}\n\n`;
    
    // We need to handle indentation to represent the tree structure
    let currentIndent = 0;
    let previousIndent = 0;
    let indentStack: number[] = [];
    
    for (const line of lines) {
        // Skip empty lines and the title line we already processed
        if (!line.trim() || line === titleLine) {
            continue;
        }
        
        // Check if this is a section header
        if (line.endsWith(':') || 
            line.startsWith('Callers of') || 
            line.startsWith('Calls to')) {
            // This is a section header
            formattedContent += `## ${line.trim()}\n\n`;
            currentIndent = 0;
            previousIndent = 0;
            indentStack = [];
            continue;
        }
        
        // Count leading spaces to determine indentation level
        const leadingSpaces = line.search(/\S|$/);
        currentIndent = Math.floor(leadingSpaces / 2); // Assume 2 spaces per level
        
        // Generate the right number of list markers for the markdown
        let prefix = '';
        if (currentIndent > previousIndent) {
            // Indent increased, start a new nested list
            indentStack.push(currentIndent);
        } else if (currentIndent < previousIndent) {
            // Indent decreased, pop from stack
            while (indentStack.length > 0 && indentStack[indentStack.length - 1] > currentIndent) {
                indentStack.pop();
            }
        }
        
        // Generate indentation for markdown
        prefix = '  '.repeat(indentStack.length - 1) + '- ';
        if (indentStack.length === 0) {
            prefix = '- ';
        }
        
        // Extract file info if present
        const fileMatch = line.trim().match(/([^(]+)\((\d+),\s*(\d+)\)/);
        if (fileMatch) {
            const [_, filePath, lineNum, colNum] = fileMatch;
            formattedContent += `${prefix}\`${filePath.trim()}\` - Line ${lineNum}, Column ${colNum}\n`;
        } else {
            formattedContent += `${prefix}${line.trim()}\n`;
        }
        
        previousIndent = currentIndent;
    }
    
    return formattedContent;
}

/**
 * Format the references view content for export
 * @param content The raw content from the references view
 * @returns Formatted content for export
 */
function formatReferencesContent(content: string): string {
    // The references view content is already somewhat formatted, but we can enhance it
    // Extract the title/header if present
    const lines = content.split('\n');
    let title = 'References';
    
    // Find if there's a title line (usually starts with "# " or similar)
    const titleLine = lines.find(line => line.startsWith('# ') || 
                                        line.startsWith('References for '));
    if (titleLine) {
        title = titleLine;
    }
    
    // Create a markdown formatted output
    let formattedContent = `# ${title}\n\n`;
    
    // Process each line to create a better formatted output
    let currentSection = '';
    let itemCount = 0;
    
    for (const line of lines) {
        // Skip empty lines and the title line we already processed
        if (!line.trim() || line === titleLine) {
            continue;
        }
        
        // Check if this is a section header
        if (line.endsWith(':') || line.endsWith('calls') || line.endsWith('references')) {
            currentSection = line;
            formattedContent += `## ${currentSection}\n\n`;
            itemCount = 0;
            continue;
        }
        
        // This is a reference item
        itemCount++;
        
        // Try to extract file path and line number if present
        const fileMatch = line.match(/([^(]+)\((\d+),\s*(\d+)\)/);
        if (fileMatch) {
            const [_, filePath, lineNum, colNum] = fileMatch;
            formattedContent += `${itemCount}. \`${filePath.trim()}\` - Line ${lineNum}, Column ${colNum}\n`;
        } else {
            formattedContent += `${itemCount}. ${line.trim()}\n`;
        }
    }
    
    return formattedContent;
}

/**
 * Registers the export call hierarchy command
 */
export function registerExportCallHierarchyCommand(context: vscode.ExtensionContext) {
    const disposable = vscode.commands.registerCommand('sourceseek.exportCallHierarchy', exportCallHierarchy);
    context.subscriptions.push(disposable);
} 