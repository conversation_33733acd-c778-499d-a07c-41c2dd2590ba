import * as vscode from 'vscode';
import * as path from 'path';
import { absoluteToRelative, outputChannel, relativeToAbsolute } from './UtilFuns';

export class MyCallHierarchyItem extends vscode.CallHierarchyItem {
    head?: boolean;  // Add optional head property
    
    constructor(
        kind: vscode.SymbolKind,
        name: string,
        detail: string,
        uri: vscode.Uri,
        range: vscode.Range,
        selectionRange: vscode.Range,
        head?: boolean
    ) {
        super(kind, name, detail, uri, range, selectionRange);
        this.head = head;
    }
    
    // Static method to create from a regular CallHierarchyItem
    static fromCallHierarchyItem(item: vscode.CallHierarchyItem, head?: boolean): MyCallHierarchyItem {
        return new MyCallHierarchyItem(
            item.kind,
            item.name,
            item.detail || '',
            item.uri,
            item.range,
            item.selectionRange,
            head
        );
    }
}
// Define the history entry structure
export interface HistoryEntry {
    size: number;
    items: MyCallHierarchyItem[];  // Change to MyCallHierarchyItem
    isCallStack?: boolean;  // true for call stack, false/undefined for definition
    isCalling?: boolean;
    description?: string;   // Optional description for hover
    parent?: HistoryEntry;
}

export class BrowserHistory {
    private history: HistoryEntry[];
    private limit: number;
    private head: number; // Points to the newest item
    private tail: number; // Points to the oldest item
    private size: number;
    private storageUri: string;
    private last_name: string;
    private isDirty: boolean = false;
    private autoSaveInterval: NodeJS.Timeout | null = null;
    private _newestFirst: boolean = true; // Default order is newest first
    private _view?: vscode.TreeView<any>; // Add this property

    constructor(limit: number, context: vscode.ExtensionContext) {
        this.history = new Array(limit);
        this.limit = limit;
        this.head = limit; // No items initially
        this.tail = limit - 1;  // Where the first item will go
        this.size = 0;
        this.storageUri = path.join(vscode.workspace.rootPath || '', 'sourceseek-his.json');
        this.last_name = '';
        
        // Start auto-save timer
        this.startAutoSave();
    }

    public getSize(): number {
        return this.size;
    }

    public getAbsIndex(index: number): number {
        if (this._newestFirst) {
            return (this.head + index) % this.limit;
        } else {
            return (this.tail - index + this.limit) % this.limit;
        }
    }


    // Start auto-save timer
    private startAutoSave(): void {
        // Clear any existing interval
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }
        
        // Set up new interval - save every 10 seconds if dirty
        this.autoSaveInterval = setInterval(() => {
            if (this.isDirty) {
                this.save().then(() => {
                    this.isDirty = false;
                }).catch(err => {
                    console.error('Auto-save failed:', err);
                });
            }
        }, 10000); // 10 seconds
    }

    // Stop auto-save timer (call when disposing)
    public stopAutoSave(): void {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }
        
        // Final save if dirty
        if (this.isDirty) {
            this.save().catch(err => {
                console.error('Final save failed:', err);
            });
        }
    }

    public addEntry(entry: HistoryEntry): void {
                   
        // Update head to point to the position of the new item
        this.head = (this.head - 1 + this.limit) % this.limit;
        this.history[this.head] = entry;

        // If we're at capacity, move tail forward
        if (this.size === this.limit) {
            this.tail = (this.tail - 1 + this.limit) % this.limit;
        } else {
            this.size++;
        }

        // Add this line to update the view title
        this.updateViewTitle();

        // Mark as dirty for auto-save
        this.isDirty = true;
    }
    // Add a new HistoryEntry
    public add(entry: HistoryEntry): boolean {
        // Check for duplicates based on the first item's name (if any)

        // If the entry already exists, move it to the head
        for (let i = 0; i < this.size; i = i + 1 ) {
            const existingEntry = this.history[(this.head + i) % this.limit];
            if (existingEntry && existingEntry.size > 0 && existingEntry.items[0].name === entry.items[0].name) {
                this.deleteByIndex(i);
                this.addEntry(existingEntry);
                return false;
            }
        }
        this.addEntry(entry);
        return true;
    }

    // Add a single CallHierarchyItem by wrapping it in a HistoryEntry
    public addCallHierarchyItem(item: vscode.CallHierarchyItem): boolean {
        const entry: HistoryEntry = {
            size: 1,
            items: [item],
            isCallStack: false,
            isCalling: false,
            description: '',
            parent: undefined
        };
        return this.add(entry);
    }

    public async addSymbolInformationAbsPath(symbolInfos: vscode.SymbolInformation[], scope: vscode.WorkspaceFolder | undefined,
        caller_symbol_info: vscode.SymbolInformation | null): Promise<boolean> {
        let relativeSymbolInfos: vscode.SymbolInformation[] = [];
        if (scope !== undefined) {
            relativeSymbolInfos = symbolInfos.map(symbolInfo => absoluteToRelative(symbolInfo, scope));
        } else {
            relativeSymbolInfos = symbolInfos;
        }
        return await this.addSymbolInformation(relativeSymbolInfos, caller_symbol_info);
    }

    public async addSymbolInformation(symbolInfos: vscode.SymbolInformation[], caller_symbol_info: vscode.SymbolInformation | null): Promise<boolean> {
        if (symbolInfos.length === 0) {
            return false;
        }
        
        // Convert all SymbolInformation items to MyCallHierarchyItems
        const callHierarchyItems = symbolInfos.map(symbolInfo => 
            new MyCallHierarchyItem(
                symbolInfo.kind,
                symbolInfo.name,
                symbolInfo.containerName || '',
                symbolInfo.location.uri,
                symbolInfo.location.range,
                symbolInfo.location.range,
                false // Set head to false by default
            )
        );

        if (caller_symbol_info?.name === symbolInfos[0].name) {
            caller_symbol_info = null;
        }

        let entry: HistoryEntry|null = null;
        let index: number = 0;
        if (caller_symbol_info !== null) {
            const result = await this.findEntryByNameNoCalled(caller_symbol_info.name);
            if (result !== null) {
                entry = result[0];
                index = result[1];
            }
        }
        if (entry === null) {
            entry = {
                size: callHierarchyItems.length,
                items: callHierarchyItems
            };
            return this.add(entry);
        } else {
            // Check if each item already exists in entry.items before adding
            const itemsToAdd = callHierarchyItems.filter(newItem => {
                // Check if this item already exists in the entry
                return !entry!.items.some(existingItem => 
                    // Compare by name, URI, and position
                    existingItem.name === newItem.name && 
                    existingItem.uri.toString() === newItem.uri.toString() &&
                    existingItem.range.start.line === newItem.range.start.line
                );
            });
            
            // Only add items that don't already exist
            if (itemsToAdd.length === 0) {
                this.add(entry);
                return false;
            }
            let callerIndex = 0;
            callerIndex = entry.items.findIndex(item => item.name === caller_symbol_info?.name 
                && item.uri.path.replace(/[\\\/]/g, '') === caller_symbol_info?.location.uri.path.replace(/[\\\/]/g, ''));
            if (callerIndex === -1) {
                callerIndex = entry.items.findIndex(item => item.name === caller_symbol_info?.name);
            }
            if (entry.isCallStack) {
                // Find the index of the caller symbol in the items array
                
                if (callerIndex !== -1) {
                    // Set head property for new items
                    itemsToAdd.forEach((item, index) => {
                        if (index !== 0) {
                            (item as MyCallHierarchyItem).head = true;
                        }
                    });
                    
                    // Insert the new items before or after the caller symbol
                    if (!entry.isCalling) {
                        if (entry.items[callerIndex + 1] != undefined) {
                            entry.items[callerIndex + 1].head = true;
                        }
                        entry.items.splice(callerIndex, 0, ...itemsToAdd);
                    } else {
                        entry.items.splice(callerIndex + 1, 0, ...itemsToAdd);
                    }
                } else {
                    // If caller symbol not found, just append to the end
                    outputChannel.appendLine(`Caller symbol not found: ${caller_symbol_info?.name}`);
                    vscode.window.showErrorMessage(`Caller symbol not found: ${caller_symbol_info?.name}`);
                    return false;
                }
            } else {
                entry.isCallStack = true;
                entry.isCalling = true;
                // Set head property for new items
                itemsToAdd.forEach(item => (item as MyCallHierarchyItem).head = false);
                entry.items.push(...itemsToAdd);
            }

            entry.size += itemsToAdd.length;
            if (callerIndex !== -1) {
                entry.items[callerIndex].range = caller_symbol_info?.location.range ?? new vscode.Range(0, 0, 0, 0);
                if (caller_symbol_info?.location.uri) {
                    entry.items[callerIndex].uri = caller_symbol_info?.location.uri;
                }
            }
            this.deleteByIndex(index);
            this.addEntry(entry);
            return false;
        }
    }
    
    public async findEntryByNameNoCalled(name: string): Promise<[HistoryEntry, number] | null> {
        for (let i = 0; i < this.size; i++) {
            const entry = this.history[(this.head + i) % this.limit];
            if (entry.isCallStack && entry.isCalling == false) {
                continue;
            }
            const idx = entry.items.findIndex(item => item.name === name);
            if (idx !== -1) {
                return [entry, i];
            }
        }
        return null;
    }

    public findRootParentEntryByItem(item: vscode.CallHierarchyItem): HistoryEntry | null {
        for (let i = 0; i < this.size; i++) {
            const entry = this.history[(this.head + i) % this.limit];
            if (entry.items.includes(item)) {
                return entry;
            }
        }
        return null;
    }   

    public findRootEntryByName(name: string): HistoryEntry | null {
        for (let i = 0; i < this.size; i++) {
            const entry = this.history[(this.head + i) % this.limit];
            if (entry.items[0].name === name) {
                return entry;
            }
        }
        return null;
    }

    public addCallStackItem(root_item: vscode.CallHierarchyItem, item: vscode.CallHierarchyItem, parent_item: vscode.CallHierarchyItem | null): void {
        if (root_item.name === item.name) {
            // Convert to MyCallHierarchyItem and set head=true
            const myItem = MyCallHierarchyItem.fromCallHierarchyItem(item, false);
            this.addCallHierarchyItem(myItem);
        } else {
            let entry = this.findRootEntryByName(root_item.name);
            if (!entry) {
                // Convert to MyCallHierarchyItem and set head=true for root
                const myRootItem = MyCallHierarchyItem.fromCallHierarchyItem(root_item, false);
                this.addCallHierarchyItem(myRootItem);
                entry = this.findRootEntryByName(root_item.name);
            }
            if (!entry) {
                outputChannel.appendLine(`Root entry not found: ${root_item.name}`);
                return;
            }
            
            // Check if this item is a parent symbol
            const isParent = entry.items.length > 0 && parent_item && entry.items[entry.items.length - 1].name === parent_item.name;
            
            // Create MyCallHierarchyItem with head property
            const myItem = MyCallHierarchyItem.fromCallHierarchyItem(item, !isParent);
            
            entry.isCallStack = true;
            entry.isCalling = false;
            entry.items.push(myItem);
            entry.size++;
            this.add(entry);
            
            // Mark as dirty for auto-save
            this.isDirty = true;
        }
    }

    // Add an array of CallHierarchyItems directly
    public addCallHierarchyItems(items: vscode.CallHierarchyItem[]): void {
        if (items.length === 0) {
            return;
        }
        
        // Create a HistoryEntry with the items
        const entry: HistoryEntry = {
            size: items.length,
            items: items
        };
        
        // Add the entry
        this.add(entry);
    }
// In BrowserHistoryStore.ts

    // Add this method to the BrowserHistory class
    public moveEntries(sourceIndices: number[], targetIndex: number): void {
        // Sort indices in descending order to avoid index shifting during removal
        const sortedIndices = [...sourceIndices].sort((a, b) => b - a);

        // Extract the entries to move
        const entriesToMove: HistoryEntry[] = [];
        for (const index of sortedIndices) {
            if (index >= 0 && index < this.history.length) {
                const entry = this.history[index];
                entriesToMove.unshift(entry); // Add to beginning to maintain original order
                this.deleteByAbsoluteIndex(index);
            }
            break; //only one entry can be moved at a time
        }

        // Adjust target index if needed (if target was after removed items)
        let adjustedTargetIndex = targetIndex;
        for (const sourceIndex of sortedIndices) {
            if (this.head <= targetIndex && sourceIndex > targetIndex) {
                adjustedTargetIndex++;
            }
        }

        // Insert entries at the target position
        const targetEntry = this.history[adjustedTargetIndex];
        if (targetEntry) {
            const items = entriesToMove[0].items;
            targetEntry.items.push(...items);
            targetEntry.size += items.length;
            if (!targetEntry.isCallStack) {
                targetEntry.isCallStack = true;
                if (entriesToMove[0].isCallStack) {
                    targetEntry.isCalling = entriesToMove[0].isCalling;
                } else {
                    targetEntry.isCalling = true;
                }
            }
        } else {
            /* do nothing */
        }
        this.isDirty = true;
    }

    public deleteByIndex(index: number): void {
            // Calculate actual index in the array
        if (index >= 0 && index < this.size) {
            const actualIndex = (this.head + index) % this.limit;
            this.deleteByAbsoluteIndex(actualIndex);
        }
    }

    // Delete an entry by index (0 is newest, size-1 is oldest)
    public deleteByAbsoluteIndex(actualIndex: number): void {
        // Shift items to fill the gap
        for (let i = actualIndex; i !== this.head && this.head != this.limit; i = (i - 1 + this.limit) % this.limit) {
            this.history[i] = this.history[(i - 1 + this.limit) % this.limit];
        }

        // Update head
        this.head = (this.head + 1) % this.limit;
        this.size--;

        // Add this line to update the view title
        this.updateViewTitle();

        // If we deleted all items, reset head and tail
        if (this.size === 0) {
            this.head = this.limit;
            this.tail = this.limit - 1;
        }
        this.isDirty = true;
    }

    public deleteByItemAbs(item: vscode.CallHierarchyItem,
        idx: number, wholetree: boolean = false): void {
        if (this.history[idx] && this.history[idx].items.includes(item)) {
            if (this.history[idx].size === 1 || wholetree) {
                this.deleteByAbsoluteIndex(idx);
            } else {
                this.history[idx].items = this.history[idx].items.filter(it => it !== item);
                this.history[idx].size--;
            }
            //break;
        }
        //}
        this.isDirty = true;
    }
    // Delete an entry by reference to one of its items
    public deleteByItem(item: vscode.CallHierarchyItem, i: number, wholetree: boolean = false): void {
        //for (let i = 0; i < this.size; i++) {
            const idx = (this.head + i) % this.limit;
            this.deleteByItemAbs(item, idx, wholetree);

    }

    // Get the list of HistoryEntries from newest to oldest
    public getList(fix_order: boolean = false): HistoryEntry[] {
        const list: HistoryEntry[] = [];
        if (this._newestFirst && !fix_order) {
            for (let i = 0; i < this.size; i++) {
                list.push(this.history[(this.head + i) % this.limit]);
            }
        } else {
            for (let i = this.size - 1; i >= 0; i--) {
                list.push(this.history[(this.head + i) % this.limit]);
            }
        }
        return list; // Already in newest to oldest order
    }

    // Get a flattened list of all CallHierarchyItems from newest to oldest
    public getAllItems(): vscode.CallHierarchyItem[] {
        const items: vscode.CallHierarchyItem[] = [];
        for (let i = 0; i < this.size; i++) {
            const entry = this.history[(this.head + i) % this.limit];
            items.push(...entry.items);
        }
        return items;
    }

    // Save history to a file
    public async saveToFile(filePath: string): Promise<void> {
        // Create a serializable representation of the history
        const serializedData = Array.from({ length: this.size }, (_, i) => {
            const entry = this.getEntryByIndex(i, true);
            if (!entry) return null;
            
            return {
                size: entry.size,
                isCallStack: entry.isCallStack,
                isCalling: entry.isCalling,
                description: entry.description,
                items: entry.items.map(item => ({
                    name: item.name,
                    kind: item.kind,
                    detail: item.detail || '',
                    uri: item.uri.toString(),
                    head: (item as MyCallHierarchyItem).head, // Save the head property
                    range: {
                        start: { line: item.range.start.line, character: item.range.start.character },
                        end: { line: item.range.end.line, character: item.range.end.character }
                    },
                    selectionRange: {
                        start: { line: item.selectionRange.start.line, character: item.selectionRange.start.character },
                        end: { line: item.selectionRange.end.line, character: item.selectionRange.end.character }
                    }
                }))
            }
        }).filter(Boolean);

        try {
            await vscode.workspace.fs.writeFile(
                vscode.Uri.file(filePath),
                Buffer.from(JSON.stringify(serializedData, null, 2))
            );
        } catch (error) {
            throw new Error(`Failed to save history to file: ${error}`);
        }
    }

    // Load history from a file
    public async loadFromFile(filePath: string): Promise<void> {
        try {
            const fileContent = await vscode.workspace.fs.readFile(vscode.Uri.file(filePath));
            const serializedData = JSON.parse(fileContent.toString());
            
            // Clear current history
            this.history = new Array(this.limit);
            this.head = this.limit;
            this.tail = this.limit - 1;
            this.size = 0;

            // Reconstruct HistoryEntries and add them to history
            for (const entryData of serializedData) {
                const callHierarchyItems = entryData.items.map((item: any) => {
                    return new MyCallHierarchyItem(
                        item.kind,
                        item.name,
                        item.detail,
                        vscode.Uri.parse(item.uri),
                        new vscode.Range(
                            new vscode.Position(item.range.start.line, item.range.start.character),
                            new vscode.Position(item.range.end.line, item.range.end.character)
                        ),
                        new vscode.Range(
                            new vscode.Position(item.selectionRange.start.line, item.selectionRange.start.character),
                            new vscode.Position(item.selectionRange.end.line, item.selectionRange.end.character)
                        ),
                        item.head
                    );
                });
                
                if (entryData.description === undefined) {
                    entryData.description = '';
                }

                    const entry: HistoryEntry = {
                        size: entryData.size,
                        items: callHierarchyItems,
                        isCallStack: entryData.isCallStack,
                        isCalling: entryData.isCalling,
                        description: entryData.description,
                        parent: entryData.parent
                    };
                
                this.add(entry);
            }
        } catch (error) {
            outputChannel.appendLine(`Failed to load history from file: ${error}`);
        }
    }

    public async save(): Promise<void> {
        return this.saveToFile(this.storageUri);
    }

    public async load(): Promise<void> {
        return this.loadFromFile(this.storageUri);
    }

    // Clean up resources
    public dispose(): void {
        this.stopAutoSave();
    }

    public clear(): void {
        this.history = new Array(this.limit);
        this.head = this.limit;
        this.tail = this.limit - 1;
        this.size = 0;
        
        // Add this line to update the view title
        this.updateViewTitle();
        
        this.isDirty = true;
    }

    public get newestFirst(): boolean {
        return this._newestFirst;
    }
    
    public reverseOrder(): void {
        this._newestFirst = !this._newestFirst;
    }

    public getEntryByAbsIndex(index: number): HistoryEntry | undefined {
        return this.history[index];
    }
    // Get a specific history entry by index
    public getEntryByIndex(index: number, fix_order: boolean = false): HistoryEntry | undefined {
        if (this._newestFirst && !fix_order) {
            return this.history[(this.head + index) % this.limit];
        } else {
            return this.history[(this.tail - index + this.limit) % this.limit];
        }
    }

    // Change order of items
    public toggleOrder(): void {
        this._newestFirst = !this._newestFirst;
    }

    // Update the history size limit
    public setLimit(newLimit: number): void {
        if (newLimit === this.limit) {
            return; // No change needed
        }
        
        // Create a new array with the new limit
        const newHistory = new Array(newLimit);
        
        // Copy existing entries to the new array
        const copyCount = Math.min(this.size, newLimit);
        for (let i = 0; i < copyCount; i++) {
            const oldIndex = (this.head + i) % this.limit;
            newHistory[i] = this.history[oldIndex];
        }
        
        // Update the properties
        this.history = newHistory;
        this.limit = newLimit;
        this.head = 0;
        this.tail = (copyCount > 0) ? copyCount - 1 : newLimit - 1;
        this.size = copyCount;
        
        this.isDirty = true;
    }

    // Add this setter
    public set view(v: vscode.TreeView<any>) {
        this._view = v;
        this.updateViewTitle();
    }

    // Add this method
    private updateViewTitle(): void {
        if (this._view) {
            this._view.title = `History (${this.size})`;
        }
    }
}

export default BrowserHistory;

export function activate(context: vscode.ExtensionContext) {
    // Get the maximum history size from settings (default to 100 if not specified)
    const config = vscode.workspace.getConfiguration('sourceseek');
    const maxHistorySize = config.get('maxHistorySize') as number || 100;
    
    const browserHistory = new BrowserHistory(maxHistorySize, context);
    
    // Register the dispose method to be called when the extension is deactivated
    context.subscriptions.push({ dispose: () => browserHistory.dispose() });
    
    // 加载保存的历史记录
    browserHistory.load().then(async () => {
        // Initial load complete
    });
    
    // Register a listener for configuration changes
    context.subscriptions.push(
        vscode.workspace.onDidChangeConfiguration(event => {
            if (event.affectsConfiguration('sourceseek.maxHistorySize')) {
                const newConfig = vscode.workspace.getConfiguration('sourceseek');
                const newMaxHistorySize = newConfig.get('maxHistorySize') as number || 100;
                
                // Update the history size limit
                browserHistory.setLimit(newMaxHistorySize);
            }
        })
    );
}
