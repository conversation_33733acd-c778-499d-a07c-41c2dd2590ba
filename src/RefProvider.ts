'use strict';

import * as vscode from 'vscode';

//import CscopeExecutor from './CscopeExecutor';
import { PassThrough } from 'stream';
import { findReferences } from './CallHierarchyProvider';

var path = require('path');

export class RefProvider implements vscode.ReferenceProvider {


    constructor (){
    }

    public provideReferences(
        document: vscode.TextDocument, position: vscode.Position,
        options: { includeDeclaration: boolean }, token: vscode.CancellationToken):
        Thenable<vscode.Location[]> {
            const symbol = document.getText(document.getWordRangeAtPosition(position));

            return new Promise<vscode.Location[]>(async (resolve, reject) => {

                //const fileList = await this.executor.findReferences(symbol);
                let references = await findReferences(symbol);
                if (!references) {
                    return resolve([]);
                }
                let list : vscode.Location[] = [];
                references.forEach((line) =>{
                    let fileName = line.filePath;
                    if (!path.isAbsolute(fileName))
                        fileName = vscode.workspace.rootPath + '/' + fileName;
//                    console.log(fileName);
                    const lineNum = line.linePosition - 1;
                    let start_pos = new vscode.Position(lineNum, 0);
                    let end_pos = new vscode.Position(lineNum, 0);
                    let loc = new vscode.Location(vscode.Uri.file(fileName), new vscode.Range(start_pos, end_pos));
                    list.push(loc);
                });

                return resolve(list);
        });
    }
}