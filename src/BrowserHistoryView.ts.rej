--- src/BrowserHistoryView.ts
+++ src/BrowserHistoryView.ts
@@ -1,6 +1,8 @@
 import * as vscode from 'vscode';
 import BrowserHistory from './BrowserHistoryStore';
 import { g_browserHistory } from './extension';
+import * as fs from 'fs';
+import * as path from 'path';
 
 // Define the HistoryEntry interface to match BrowserHistoryStore
 interface HistoryEntry {
@@ -75,6 +77,14 @@ export class BrowserHistoryViewProvider implements vscode.TreeDataProvider<Brows
     // 添加一个方法来切换显示模式
     public toggleFunctionOnlyMode(): void {
         this._functionOnlyMode = !this._functionOnlyMode;
+
+        // Update the command title to reflect the current mode
+        vscode.commands.executeCommand(
+            'setContext',
+            'browserHistoryView.functionOnlyMode',
+            this._functionOnlyMode
+        );
+
         this.refresh();
     }
     
@@ -97,6 +107,10 @@ export class BrowserHistoryViewProvider implements vscode.TreeDataProvider<Brows
     }
     
     getTreeItem(element: BrowserHistoryItem): vscode.TreeItem {
+        // If this is the first item and we're in function-only mode, add a note to the tooltip
+        if (!element.parentItem && this._functionOnlyMode) {
+            element.tooltip = `${element.tooltip || element.item.name}\n\nFunction Only Mode is ON`;
+        }
         return element;
     }
     
@@ -279,6 +293,43 @@ export function registerBrowserHistoryView(context: vscode.ExtensionContext, bro
         showCollapseAll: true // Enable collapse all button since we now have a hierarchical view
     });
     
+    // Create a status bar item for the function-only mode
+    const functionOnlyStatusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
+    functionOnlyStatusBarItem.command = 'browserHistoryView.toggleFunctionOnly';
+    context.subscriptions.push(functionOnlyStatusBarItem);
+
+    // Function to update the status bar item
+    function updateFunctionOnlyStatus() {
+        if (g_browserHistoryViewProvider) {
+            const mode = g_browserHistoryViewProvider.functionOnlyMode ? 'Function Only' : 'All Symbols';
+            functionOnlyStatusBarItem.text = `$(filter) ${mode}`;
+            functionOnlyStatusBarItem.tooltip = `Symbol History: ${mode} mode`;
+            functionOnlyStatusBarItem.show();
+        }
+    }
+
+    // Register a command to toggle function-only mode
+    context.subscriptions.push(
+        vscode.commands.registerCommand('browserHistoryView.toggleFunctionOnly', () => {
+            if (g_browserHistoryViewProvider) {
+                g_browserHistoryViewProvider.toggleFunctionOnlyMode();
+
+                // Update the command title based on the current mode
+                const mode = g_browserHistoryViewProvider.functionOnlyMode ? 'ON' : 'OFF';
+                vscode.commands.getCommands().then(commands => {
+                    if (commands.includes('browserHistoryView.toggleFunctionOnly')) {
+                        // This doesn't actually work because command titles are defined in package.json
+                        // But we can show a notification instead
+                        vscode.window.showInformationMessage(`Function Only Mode: ${mode}`);
+                    }
+                });
+            }
+        })
+    );
+
+    // Initialize the status bar
+    updateFunctionOnlyStatus();
+
     // Register a command to refresh the view
     context.subscriptions.push(
         vscode.commands.registerCommand('browserHistoryView.refresh', () => {
@@ -286,12 +337,22 @@ export function registerBrowserHistoryView(context: vscode.ExtensionContext, bro
         })
     );
     
-    // Register a command to clear the history
+    // Register a command to clear the history with confirmation
     context.subscriptions.push(
-        vscode.commands.registerCommand('browserHistoryView.clear', () => {
-            // Clear the history (you'll need to add a clear method to BrowserHistory)
-            // browserHistory.clear();
-            g_browserHistoryViewProvider?.refresh();
+        vscode.commands.registerCommand('browserHistoryView.clear', async () => {
+            // Show confirmation dialog
+            const answer = await vscode.window.showWarningMessage(
+                'Are you sure you want to clear all symbol history?',
+                { modal: true },
+                'Yes', 'No'
+            );
+
+            if (answer === 'Yes') {
+                // Clear the history
+                browserHistory.clear();
+                g_browserHistoryViewProvider?.refresh();
+                vscode.window.showInformationMessage('Symbol history cleared');
+            }
         })
     );
     
@@ -309,35 +370,151 @@ export function registerBrowserHistoryView(context: vscode.ExtensionContext, bro
             g_browserHistoryViewProvider?.exportToClipboard(item);
         })
     );
-/*
-    // 创建状态栏项目
-    const statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right);
-    statusBarItem.command = 'browserHistoryView.toggleFunctionOnly';
-    updateStatusBar();
-
-    // 更新状态栏的函数
-    function updateStatusBar() {
-        if (g_browserHistoryViewProvider) {
-            const mode = g_browserHistoryViewProvider.functionOnlyMode ? 'Function Only' : 'All Symbols';
-            statusBarItem.text = `$(symbol-method) ${mode}`;
-            statusBarItem.tooltip = `Symbol History: ${mode} mode`;
-            statusBarItem.show();
-        }
-    }
 
-    // 在切换模式时更新状态栏
+    // Register a command to save history to file
     context.subscriptions.push(
-        vscode.commands.registerCommand('browserHistoryView.toggleFunctionOnly', () => {
-            if (g_browserHistoryViewProvider) {
-                g_browserHistoryViewProvider.toggleFunctionOnlyMode();
-                updateStatusBar();
+        vscode.commands.registerCommand('browserHistoryView.saveToFile', async () => {
+            try {
+                // Get the history data
+                const historyEntries = browserHistory.getList();
+
+                if (historyEntries.length === 0) {
+                    vscode.window.showInformationMessage('No history to save.');
+                    return;
+                }
+
+                // Open file save dialog
+                const defaultPath = vscode.workspace.workspaceFolders
+                    ? path.join(vscode.workspace.workspaceFolders[0].uri.fsPath, 'symbol_history.json')
+                    : 'symbol_history.json';
+
+                const fileUri = await vscode.window.showSaveDialog({
+                    defaultUri: vscode.Uri.file(defaultPath),
+                    filters: {
+                        'JSON Files': ['json'],
+                        'All Files': ['*']
+                    },
+                    title: 'Save Symbol History'
+                });
+
+                if (!fileUri) {
+                    // User cancelled the dialog
+                    return;
+                }
+
+                // Prepare data for serialization
+                const serializableHistory = historyEntries.map(entry => ({
+                    size: entry.size,
+                    items: entry.items.map(item => ({
+                        name: item.name,
+                        kind: item.kind,
+                        detail: item.detail || '',
+                        uri: item.uri.toString(),
+                        range: {
+                            start: { line: item.range.start.line, character: item.range.start.character },
+                            end: { line: item.range.end.line, character: item.range.end.character }
+                        },
+                        selectionRange: {
+                            start: { line: item.selectionRange.start.line, character: item.selectionRange.start.character },
+                            end: { line: item.selectionRange.end.line, character: item.selectionRange.end.character }
+                        }
+                    }))
+                }));
+
+                // Convert to JSON and write to file
+                const jsonData = JSON.stringify(serializableHistory, null, 2);
+                fs.writeFileSync(fileUri.fsPath, jsonData);
+
+                vscode.window.showInformationMessage(`Symbol history saved to ${fileUri.fsPath}`);
+            } catch (error) {
+                vscode.window.showErrorMessage(`Failed to save history: ${error instanceof Error ? error.message : String(error)}`);
             }
         })
     );
 
-    // 注册状态栏项目以便清理
-    context.subscriptions.push(statusBarItem);
-*/
+    // Register a command to load history from file
+    context.subscriptions.push(
+        vscode.commands.registerCommand('browserHistoryView.loadFromFile', async () => {
+            try {
+                // Open file open dialog
+                const fileUris = await vscode.window.showOpenDialog({
+                    canSelectMany: false,
+                    filters: {
+                        'JSON Files': ['json'],
+                        'All Files': ['*']
+                    },
+                    title: 'Load Symbol History'
+                });
+
+                if (!fileUris || fileUris.length === 0) {
+                    // User cancelled the dialog
+                    return;
+                }
+
+                const fileUri = fileUris[0];
+
+                // Read and parse the file
+                const fileContent = fs.readFileSync(fileUri.fsPath, 'utf8');
+                const loadedData = JSON.parse(fileContent);
+
+                // Validate the data structure
+                if (!Array.isArray(loadedData)) {
+                    throw new Error('Invalid history file format');
+                }
+
+                // Clear current history
+                browserHistory.clear();
+
+                // Convert the loaded data back to CallHierarchyItems and add to history
+                for (const entry of loadedData) {
+                    if (!entry.items || !Array.isArray(entry.items) || entry.items.length === 0) {
+                        continue;
+                    }
+
+                    // Process each item in the entry
+                    for (const itemData of entry.items) {
+                        try {
+                            const uri = vscode.Uri.parse(itemData.uri);
+
+                            // Create range objects
+                            const range = new vscode.Range(
+                                new vscode.Position(itemData.range.start.line, itemData.range.start.character),
+                                new vscode.Position(itemData.range.end.line, itemData.range.end.character)
+                            );
+
+                            const selectionRange = new vscode.Range(
+                                new vscode.Position(itemData.selectionRange.start.line, itemData.selectionRange.start.character),
+                                new vscode.Position(itemData.selectionRange.end.line, itemData.selectionRange.end.character)
+                            );
+
+                            // Create CallHierarchyItem
+                            const item = new vscode.CallHierarchyItem(
+                                itemData.kind,
+                                itemData.name,
+                                itemData.detail || '',
+                                uri,
+                                range,
+                                selectionRange
+                            );
+
+                            // Add to history
+                            browserHistory.addCallHierarchyItem(item);
+                        } catch (itemError) {
+                            console.error('Error processing history item:', itemError);
+                            // Continue with other items
+                        }
+                    }
+                }
+
+                // Refresh the view
+                g_browserHistoryViewProvider?.refresh();
+
+                vscode.window.showInformationMessage(`Symbol history loaded from ${fileUri.fsPath}`);
+            } catch (error) {
+                vscode.window.showErrorMessage(`Failed to load history: ${error instanceof Error ? error.message : String(error)}`);
+            }
+        })
+    );
 
     g_browserHistoryViewProvider?.refresh();
     
