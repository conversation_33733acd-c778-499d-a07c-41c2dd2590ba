"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CallTree = exports.CallNode = void 0;
var vscode = require("vscode");
// Node in the call tree
var CallNode = /** @class */ (function () {
    function CallNode(symbol) {
        this.children = [];
        this.parent = null;
        this.symbol = symbol;
    }
    // Add a child node
    CallNode.prototype.addChild = function (node) {
        node.parent = this;
        this.children.push(node);
    };
    // Check if this node has the given symbol
    CallNode.prototype.hasSymbol = function (symbolName) {
        return this.symbol.name === symbolName;
    };
    return CallNode;
}());
exports.CallNode = CallNode;
// Tree structure to store calling relationships
var CallTree = /** @class */ (function () {
    function CallTree() {
        this.nodeMap = new Map();
        // Create a root node with a placeholder symbol
        this.root = new CallNode(new vscode.CallHierarchyItem(vscode.SymbolKind.Null, "ROOT", "", vscode.Uri.parse("file://root"), new vscode.Range(0, 0, 0, 0), new vscode.Range(0, 0, 0, 0)));
    }
    // Generate a unique key for a symbol
    CallTree.prototype.getSymbolKey = function (symbol) {
        return "".concat(symbol.name, ":").concat(symbol.uri.toString(), ":").concat(symbol.range.start.line, ":").concat(symbol.range.start.character);
    };
    // Find a node by symbol name
    CallTree.prototype.findNodeBySymbol = function (symbolName) {
        // First try the map for quick lookup
        var nodes = this.nodeMap.get(symbolName);
        if (nodes && nodes.length > 0) {
            return nodes[0]; // Return the first matching node
        }
        // Fallback to tree traversal if not found in map
        return this.findNodeInSubtree(this.root, symbolName);
    };
    // Recursively search for a node with the given symbol name
    CallTree.prototype.findNodeInSubtree = function (node, symbolName) {
        if (node.hasSymbol(symbolName)) {
            return node;
        }
        for (var _i = 0, _a = node.children; _i < _a.length; _i++) {
            var child = _a[_i];
            var found = this.findNodeInSubtree(child, symbolName);
            if (found) {
                return found;
            }
        }
        return null;
    };
    // Add a symbol and its callers to the tree
    CallTree.prototype.add = function (symbol, callers) {
        // Find the node for this symbol
        var symbolNode = this.findNodeBySymbol(symbol.name);
        // If not found, create a new node and add it to the root
        if (!symbolNode) {
            symbolNode = new CallNode(symbol);
            this.root.addChild(symbolNode);
            // Add to the map for quick lookup
            if (!this.nodeMap.has(symbol.name)) {
                this.nodeMap.set(symbol.name, []);
            }
            this.nodeMap.get(symbol.name).push(symbolNode);
        }
        var _loop_1 = function (caller) {
            // Check if this caller already exists as a child
            var existingChild = symbolNode.children.find(function (child) { return child.symbol.name === caller.name &&
                child.symbol.uri.toString() === caller.uri.toString() &&
                child.symbol.range.isEqual(caller.range); });
            if (!existingChild) {
                var callerNode = new CallNode(caller);
                symbolNode.addChild(callerNode);
                // Add to the map for quick lookup
                if (!this_1.nodeMap.has(caller.name)) {
                    this_1.nodeMap.set(caller.name, []);
                }
                this_1.nodeMap.get(caller.name).push(callerNode);
            }
        };
        var this_1 = this;
        // Add all callers as children of the symbol node
        for (var _i = 0, callers_1 = callers; _i < callers_1.length; _i++) {
            var caller = callers_1[_i];
            _loop_1(caller);
        }
    };
    // Get all nodes for a symbol name
    CallTree.prototype.getNodesForSymbol = function (symbolName) {
        return this.nodeMap.get(symbolName) || [];
    };
    // Get the entire call tree
    CallTree.prototype.getTree = function () {
        return this.root;
    };
    // Get all symbols in the tree
    CallTree.prototype.getAllSymbols = function () {
        var _this = this;
        var symbols = [];
        this.traverseTree(this.root, function (node) {
            if (node !== _this.root) { // Skip the root node
                symbols.push(node.symbol);
            }
        });
        return symbols;
    };
    // Traverse the tree and call the callback for each node
    CallTree.prototype.traverseTree = function (node, callback) {
        callback(node);
        for (var _i = 0, _a = node.children; _i < _a.length; _i++) {
            var child = _a[_i];
            this.traverseTree(child, callback);
        }
    };
    // Get the path from root to a specific symbol
    CallTree.prototype.getPathToSymbol = function (symbolName) {
        var node = this.findNodeBySymbol(symbolName);
        if (!node) {
            return [];
        }
        var path = [];
        var current = node;
        while (current && current !== this.root) {
            path.unshift(current);
            current = current.parent;
        }
        return path;
    };
    // Clear the tree
    CallTree.prototype.clear = function () {
        this.root.children = [];
        this.nodeMap.clear();
    };
    // Find the parent symbol of a given symbol
    CallTree.prototype.findParentSymbol = function (symbolName) {
        var node = this.findNodeBySymbol(symbolName);
        if (!node || !node.parent || node.parent === this.root) {
            return null;
        }
        return node.parent.symbol;
    };
    // Find the root symbol of a given symbol
    CallTree.prototype.findRootSymbol = function (symbolName) {
        var _a;
        var node = this.findNodeBySymbol(symbolName);
        if (!node) {
            return null;
        }
        var current = node;
        while (current && current.parent !== this.root) {
            current = current.parent;
        }
        return (_a = current === null || current === void 0 ? void 0 : current.symbol) !== null && _a !== void 0 ? _a : null;
    };
    return CallTree;
}());
exports.CallTree = CallTree;
