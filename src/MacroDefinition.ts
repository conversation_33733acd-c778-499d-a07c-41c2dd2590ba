import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { outputChannel } from './UtilFuns';
import { setNonActiveCodeAsGrey } from './EditWindow';

/**
 * Class to manage macro definitions
 */
export class MacroDefinitionManager {
    private macroTable: Map<string, string> = new Map();
    private lastLoadedFile: string = '';

    /**
     * Load macro definitions from a file
     * @param filePath Path to the macro definition file
     */
    public async loadFromFile(filePath: string, clear: boolean = true): Promise<void> {
        try {
            // Read the file content
            const fileContent = await vscode.workspace.fs.readFile(vscode.Uri.file(filePath));
            const content = Buffer.from(fileContent).toString('utf8');

            if (clear) {
                this.clear();

                // Store the file path for future reference
                this.lastLoadedFile = filePath;

                // Save the file path to workspace settings
                this.saveMacroFilePathToWorkspace();
            }
            // Parse the file content
            const count = this.parseMacroDefinitions(content);

            // Show success message
            const macroCount = this.macroTable.size;
            vscode.window.showInformationMessage(`Successfully loaded ${count}/${macroCount} macro definitions from ${path.basename(filePath)}`);
            outputChannel.appendLine(`Loaded ${count} macro definitions from ${filePath}, total: ${macroCount}`);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to load macro definitions: ${error instanceof Error ? error.message : String(error)}`);
            outputChannel.appendLine(`Error loading macro definitions: ${error}`);
            throw error;
        }
    }

    /**
     * Parse macro definitions from content
     * @param content Content of the macro definition file
     */
    private parseMacroDefinitions(content: string): number {
        // Clear existing macros

        // Split content into lines
        const lines = content.split('\n');
        let count = 0;

        // Process each line
        for (const line of lines) {
            // Skip empty lines and comments
            if (!line.trim() || line.trim().startsWith('//') || line.trim().startsWith('/*')) {
                continue;
            }

            // Look for #define statements - format: "#define MY_MACRO 1"
            if (line.trim().startsWith('#define')) {
                // Split the line into parts
                const parts = line.trim().split(/\s+/);

                // Check if we have at least 3 parts (#define, name, value)
                if (parts.length >= 3 && parts[0] === '#define') {
                    const macroName = parts[1];
                    // The value could be the rest of the line (for multi-part values)
                    const macroValue = parts.slice(2).join(' ');

                    // Store in the hash table
                    this.macroTable.set(macroName, macroValue);
                    count++;
                    outputChannel.appendLine(`Parsed macro: ${macroName} = ${macroValue}`);
                }
            }
        }
        return count;
    }

    /**
     * Get the value of a macro
     * @param macroName Name of the macro
     * @returns Value of the macro or 0 if not found
     */
    public getMacroValue(macroName: string, visitedMacros: Set<string> = new Set()): string {
        // Check for circular references
        if (visitedMacros.has(macroName)) {
            outputChannel.appendLine(`Warning: Circular reference detected in macro: ${macroName}`);
            return '0';
        }

        const value = this.macroTable.get(macroName);
        if (!value) {
            return '0';
        }

        // Add this macro to the visited set
        visitedMacros.add(macroName);

        // Check if the value is another macro (simple case)
        if (this.macroTable.has(value)) {
            return this.getMacroValue(value, visitedMacros); // Recursive lookup with visited set
        }

        return value;
    }

    /**
     * Get the numeric value of a macro
     * @param macroName Name of the macro
     * @returns Numeric value of the macro or 0 if not found or not a number
     */
    public getMacroNumericValue(macroName: string): number {
        const value = this.getMacroValue(macroName);
        if (value === '0') {
            return 0;
        }

        // Try to parse the value as a number
        try {
            // Handle hexadecimal values (0x...)
            if (value.toLowerCase().startsWith('0x')) {
                return parseInt(value, 16);
            }
            // Handle binary values (0b...)
            else if (value.toLowerCase().startsWith('0b')) {
                return parseInt(value.substring(2), 2);
            }
            // Handle octal values (0...)
            else if (value.startsWith('0') && value.length > 1 && !value.includes('.')) {
                return parseInt(value, 8);
            }
            // Handle decimal values
            else {
                return Number(value);
            }
        } catch (error) {
            outputChannel.appendLine(`Error parsing macro value as number: ${value}`);
            return 0;
        }
    }

    /**
     * Get the number of loaded macros
     */
    public getMacroCount(): number {
        return this.macroTable.size;
    }

    /**
     * Get the last loaded file path
     */
    public getLastLoadedFile(): string {
        return this.lastLoadedFile;
    }

    /**
     * Clear all loaded macros
     */
    public clear(): void {
        this.macroTable.clear();
        this.lastLoadedFile = '';
    }

    /**
     * Get all macro definitions as an array of [name, value] pairs
     */
    public getAllMacros(): [string, string][] {
        return Array.from(this.macroTable.entries());
    }

    /**
     * Save the macro definition file path to workspace settings
     */
    public saveMacroFilePathToWorkspace(): void {
        if (!this.lastLoadedFile || !vscode.workspace.workspaceFolders) {
            return;
        }

        // Save to workspace settings
        const config = vscode.workspace.getConfiguration('sourceseek');
        config.update('macroDefinitionFilePath', this.lastLoadedFile, vscode.ConfigurationTarget.Workspace);
        outputChannel.appendLine(`Saved macro definition file path to workspace settings: ${this.lastLoadedFile}`);
    }

    /**
     * Load the macro definition file path from workspace settings
     * @returns The loaded file path or undefined if not found
     */
    public async loadMacroFilePathFromWorkspace(): Promise<string | undefined> {
        if (!vscode.workspace.workspaceFolders) {
            return undefined;
        }

        // Load from workspace settings
        const config = vscode.workspace.getConfiguration('sourceseek');
        const filePath = config.get<string>('macroDefinitionFilePath');

        if (filePath && fs.existsSync(filePath)) {
            try {
                await this.loadFromFile(filePath);
                setNonActiveCodeAsGrey(true);
                return filePath;
            } catch (error) {
                outputChannel.appendLine(`Error loading macro definition file from workspace settings: ${error}`);
                return undefined;
            }
        }

        return undefined;
    }

    /**
     * Add a macro to the macro table
     * @param name Name of the macro
     * @param value Value of the macro
     */
    public addMacro(name: string, value: string): void {
        this.macroTable.set(name, value);
        outputChannel.appendLine(`Added macro: ${name} = ${value}`);
    }

    /**
     * Remove a macro from the macro table
     * @param name Name of the macro to remove
     * @returns true if the macro was removed, false if it didn't exist
     */
    public removeMacro(name: string): boolean {
        const existed = this.macroTable.has(name);
        if (existed) {
            this.macroTable.delete(name);
            outputChannel.appendLine(`Removed macro: ${name}`);
        }
        return existed;
    }
}

// Singleton instance
export let g_macroDefinitionManager: MacroDefinitionManager = new MacroDefinitionManager();

/**
 * Show a dialog for managing custom macros
 */
export async function showCustomMacroDialog(): Promise<void> {
    try {
        // Create a WebviewPanel
        const panel = vscode.window.createWebviewPanel(
            'customMacroManager',
            'Custom Macro Manager',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        // Get workspace path
        const workspacePath = vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0
            ? vscode.workspace.workspaceFolders[0].uri.fsPath
            : '';

        // Define custom macros file path - this is separate from the loaded macro definition file
        const customMacrosFilePath = path.join(workspacePath, 'custom-macros.h');

        // Load existing custom macros from file if it exists
        let existingCustomMacros: [string, string][] = [];
        if (fs.existsSync(customMacrosFilePath)) {
            try {
                const content = fs.readFileSync(customMacrosFilePath, 'utf8');
                const lines = content.split('\n');

                for (const line of lines) {
                    // Look for #define statements - format: "#define MY_MACRO 1"
                    if (line.trim().startsWith('#define')) {
                        // Split the line into parts
                        const parts = line.trim().split(/\s+/);

                        // Check if we have at least 3 parts (#define, name, value)
                        if (parts.length >= 3 && parts[0] === '#define') {
                            const macroName = parts[1];
                            // The value could be the rest of the line (for multi-part values)
                            const macroValue = parts.slice(2).join(' ');

                            existingCustomMacros.push([macroName, macroValue]);
                        }
                    }
                }
            } catch (error) {
                outputChannel.appendLine(`Error reading custom macros file: ${error}`);
            }
        }

        // HTML content for the webview
        panel.webview.html = `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Custom Macro Manager</title>
            <style>
                body {
                    font-family: var(--vscode-font-family);
                    padding: 20px;
                    color: var(--vscode-foreground);
                }
                .container {
                    display: flex;
                    flex-direction: column;
                    gap: 20px;
                }
                .input-group {
                    display: flex;
                    flex-direction: column;
                    gap: 8px;
                }
                .input-row {
                    display: flex;
                    gap: 8px;
                }
                input, button {
                    padding: 6px 10px;
                    border: 1px solid var(--vscode-input-border);
                    background: var(--vscode-input-background);
                    color: var(--vscode-input-foreground);
                }
                input {
                    flex-grow: 1;
                }
                button {
                    cursor: pointer;
                    background: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                    border: none;
                }
                button:hover {
                    background: var(--vscode-button-hoverBackground);
                }
                .macro-list {
                    border: 1px solid var(--vscode-input-border);
                    background: var(--vscode-input-background);
                    height: 300px;
                    overflow-y: auto;
                    padding: 10px;
                }
                .macro-item {
                    display: flex;
                    justify-content: space-between;
                    padding: 5px 0;
                    border-bottom: 1px solid var(--vscode-input-border);
                }
                .macro-item:last-child {
                    border-bottom: none;
                }
                .action-buttons {
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                }
                h2 {
                    margin-top: 0;
                    color: var(--vscode-editor-foreground);
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h2>Custom Macro Manager</h2>

                <div class="input-group">
                    <label for="macroName">Macro Name:</label>
                    <input type="text" id="macroName" placeholder="Enter macro name (e.g., MY_MACRO)">
                </div>

                <div class="input-group">
                    <label for="macroValue">Macro Value:</label>
                    <div class="input-row">
                        <input type="text" id="macroValue" value='1'>
                        <button id="addMacroButton">Add Macro</button>
                    </div>
                </div>

                <div class="input-group">
                    <label>Custom Macros:</label>
                    <div class="macro-list" id="macroList">
                        <!-- Macro items will be added here dynamically -->
                    </div>
                </div>

                <div class="action-buttons">
                    <button id="openFileButton">Open Custom Macros File</button>
                    <button id="saveButton">Close</button>
                </div>
            </div>

            <script>
                (function() {
                    // Get vscode API
                    const vscode = acquireVsCodeApi();

                    // Get elements
                    const macroNameInput = document.getElementById('macroName');
                    const macroValueInput = document.getElementById('macroValue');
                    const addMacroButton = document.getElementById('addMacroButton');
                    const macroList = document.getElementById('macroList');
                    const openFileButton = document.getElementById('openFileButton');
                    const saveButton = document.getElementById('saveButton');
                    const cancelButton = document.getElementById('cancelButton');

                    // Store macros in memory
                    const macros = new Map();

                    // Initialize with existing custom macros only
                    ${existingCustomMacros.map(([name, value]) => `macros.set("${name}", "${value}");`).join('\n')}

                    // Update the macro list display
                    function updateMacroList() {
                        macroList.innerHTML = '';
                        macros.forEach((value, name) => {
                            const macroItem = document.createElement('div');
                            macroItem.className = 'macro-item';
                            macroItem.innerHTML = \`
                                <div>\${name} = \${value}</div>
                                <button class="delete-button" data-name="\${name}">Delete</button>
                            \`;
                            macroList.appendChild(macroItem);
                        });

                        // Add event listeners to delete buttons
                        document.querySelectorAll('.delete-button').forEach(button => {
                            button.addEventListener('click', (e) => {
                                const name = e.target.getAttribute('data-name');
                                macros.delete(name);

                                // Notify VS Code to remove the macro from g_macroDefinitionManager
                                vscode.postMessage({
                                    command: 'removeMacro',
                                    name: name
                                });

                                updateMacroList();
                            });
                        });
                    }

                    // Initial update
                    updateMacroList();

                    // Add macro button
                    addMacroButton.addEventListener('click', () => {
                        const name = macroNameInput.value.trim();
                        const value = macroValueInput.value.trim();

                        if (!name) {
                            vscode.postMessage({ command: 'showError', message: 'Macro name cannot be empty' });
                            return;
                        }

                        macros.set(name, value);
                        updateMacroList();

                        // Notify VS Code to add the macro to g_macroDefinitionManager
                        vscode.postMessage({
                            command: 'addMacro',
                            name: name,
                            value: value
                        });

                        // Clear inputs
                        macroNameInput.value = '';
                        macroValueInput.value = '1';
                    });

                    // Open file button
                    openFileButton.addEventListener('click', () => {
                        vscode.postMessage({ command: 'openCustomMacrosFile' });
                    });

                    // Save button
                    saveButton.addEventListener('click', () => {
                        vscode.postMessage({
                            command: 'saveCustomMacros',
                            macros: Array.from(macros.entries())
                        });
                    });


                    // Handle messages from the extension
                    window.addEventListener('message', event => {
                        const message = event.data;
                        switch (message.command) {
                            case 'updateMacros':
                                // Update macros from extension
                                macros.clear();
                                message.macros.forEach(([name, value]) => {
                                    macros.set(name, value);
                                });
                                updateMacroList();
                                break;
                        }
                    });

                    // Signal that the webview is ready
                    vscode.postMessage({ command: 'webviewReady' });
                })();
            </script>
        </body>
        </html>`;

        // Handle messages from the webview
        panel.webview.onDidReceiveMessage(async message => {
            switch (message.command) {
                case 'webviewReady':
                    // Send existing macros to the webview
                    panel.webview.postMessage({
                        command: 'updateMacros',
                        macros: existingCustomMacros
                    });
                    break;

                case 'showError':
                    vscode.window.showErrorMessage(message.message);
                    break;

                case 'openCustomMacrosFile':
                    try {
                        // Generate file content
                        let fileContent = '/* Custom macros for SourceSeek extension */\n';
                        fileContent += '/* This file is automatically generated - do not edit manually */\n';
                        fileContent += '/* Last updated: ' + new Date().toISOString() + ' */\n\n';

                        // Add each macro as a #define
                        existingCustomMacros.forEach(([name, value]: [string, string]) => {
                            fileContent += `#define ${name} ${value}\n`;
                        });

                        // Write to file
                        fs.writeFileSync(customMacrosFilePath, fileContent);

                        vscode.window.showInformationMessage(`Saved ${existingCustomMacros.length} custom macros to ${customMacrosFilePath}`);
                        panel.dispose();
                    } catch (error) {
                        vscode.window.showErrorMessage(`Failed to save custom macros: ${error instanceof Error ? error.message : String(error)}`);
                    }

                    try {
                        // Check if file exists, create if not
                        const document = await vscode.workspace.openTextDocument(customMacrosFilePath);
                        await vscode.window.showTextDocument(document);
                    } catch (error) {
                        vscode.window.showErrorMessage(`Failed to open custom macros file: ${error instanceof Error ? error.message : String(error)}`);
                    }
                    break;

                case 'saveCustomMacros':
                    try {
                        // Generate file content
                        let fileContent = '/* Custom macros for SourceSeek extension */\n';
                        fileContent += '/* This file is automatically generated - do not edit manually */\n';
                        fileContent += '/* Last updated: ' + new Date().toISOString() + ' */\n\n';

                        // Add each macro as a #define
                        existingCustomMacros.forEach(([name, value]: [string, string]) => {
                            fileContent += `#define ${name} ${value}\n`;
                        });

                        // Write to file
                        fs.writeFileSync(customMacrosFilePath, fileContent);

                        vscode.window.showInformationMessage(`Saved ${message.macros.length} custom macros to ${customMacrosFilePath}`);
                    } catch (error) {
                        vscode.window.showErrorMessage(`Failed to save custom macros: ${error instanceof Error ? error.message : String(error)}`);
                    }
                    panel.dispose();
                    break;

                case 'cancel':
                    panel.dispose();
                    break;

                case 'addMacro':
                    // Add the macro to g_macroDefinitionManager
                    g_macroDefinitionManager.addMacro(message.name, message.value);
                    existingCustomMacros.push([message.name, message.value]);
                    break;

                case 'removeMacro':
                    // Remove the macro from g_macroDefinitionManager
                    g_macroDefinitionManager.removeMacro(message.name);
                    existingCustomMacros = existingCustomMacros.filter(([name, value]: [string, string]) => name !== message.name);
                    break;
            }
        });
    } catch (error) {
        vscode.window.showErrorMessage(`Error showing custom macro dialog: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Load custom macros from the workspace file
 */
export async function loadCustomMacros(): Promise<String> {
    if (!vscode.workspace.workspaceFolders) {
        return "Error";
    }

    const workspacePath = vscode.workspace.workspaceFolders[0].uri.fsPath;
    const customMacrosFilePath = path.join(workspacePath, 'custom-macros.h');

    if (fs.existsSync(customMacrosFilePath)) {
        try {
            await g_macroDefinitionManager.loadFromFile(customMacrosFilePath, false);
            setNonActiveCodeAsGrey(true);
            outputChannel.appendLine(`Loaded custom macros from ${customMacrosFilePath}`);
        } catch (error) {
            outputChannel.appendLine(`Error loading custom macros: ${error}`);
        }
    }
    return customMacrosFilePath;
}

/**
 * Register the macro definition command
 * @param context Extension context
 */
export function registerMacroDefinitionCommand(context: vscode.ExtensionContext): void {
    // Set the global reference in extension.ts
    const extension = require('./extension');
    extension.g_macroDefinitionManager = g_macroDefinitionManager;

    // Try to load custom macros first
        // Then try to load macro definition file from workspace settings
    g_macroDefinitionManager.loadMacroFilePathFromWorkspace().then(filePath => {
        if (filePath) {
            outputChannel.appendLine(`Loaded macro definition file from workspace settings: ${filePath}`);
        }
        loadCustomMacros().then(filePath => {
            outputChannel.appendLine('Loaded custom macros: ${filePath}');
        });
    }).catch(error => {
        outputChannel.appendLine(`Error loading macro definition file from workspace settings: ${error}`);
    });

    // Register the loadMacroDefinition command
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.loadMacroDefinition', async () => {
            try {
                // Get default path
                const defaultPath = vscode.workspace.workspaceFolders
                    ? path.join(vscode.workspace.workspaceFolders[0].uri.fsPath, 'out/debug-gcc_12.2-64/build/kernel/include/generated/autoconf.h')
                    : '';

                // Show file picker dialog
                const fileUris = await vscode.window.showOpenDialog({
                    canSelectMany: false,
                    filters: {
                        'Header Files': ['h'],
                        'All Files': ['*']
                    },
                    title: 'Select Macro Definition File',
                    defaultUri: defaultPath ? vscode.Uri.file(defaultPath) : undefined
                });

                if (!fileUris || fileUris.length === 0) {
                    // User cancelled the dialog
                    return;
                }

                // Load the selected file
                await g_macroDefinitionManager.loadFromFile(fileUris[0].fsPath);
                setNonActiveCodeAsGrey(true);

                // Show some stats in the output channel
                outputChannel.appendLine(`Loaded ${g_macroDefinitionManager.getMacroCount()} macros from ${fileUris[0].fsPath}`);
                outputChannel.show();
            } catch (error) {
                vscode.window.showErrorMessage(`Error loading macro definitions: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );

    // Register the custom macro manager command
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.manageCustomMacros', async () => {
            await showCustomMacroDialog();
        })
    );
}
