import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { determineScope, definitionToSymbolInformation, relativeToAbsolute, findFunctionNameByLine, filterSymbolInfos, outputChannel } from './UtilFuns';
import { g_browserHistory } from './extension';
import { g_browserHistoryViewProvider } from './BrowserHistoryView';
import { readtagsGoToSymbolInWorkspaceCommand, ctagsGoToSymbolInEditorCommand, readtagsGoToDefinitionCommand, setFindDefinitionTrigger } from './globalSettings';
import { readtagsGoToSymbolInWorkspaceCommandWin, ctagsGoToSymbolInEditorCommandWin, readtagsGoToDefinitionCommandWin } from './globalSettings';
import { show_command } from './UtilFuns';
import { checkDatabase, LogLevel, findDefinition, findFiles } from './CallHierarchyProvider';
import { DatabaseType } from './BuildDatabase';
import { g_functionPointerMap } from './extension';
import { g_fnstructOnly } from './SymbolPreviewView';
import { g_functionOnlyFilter, g_nonstatic } from './EditWindow';
import { g_macroDefinitionManager } from './MacroDefinition';
import { setDefaultResultOrder } from 'dns';

interface ExecOptions {
    cwd: string;
    maxBuffer?: number;
}

interface Definition {
    // 由于原代码中没有明确的 Definition 类型定义，这里添加一个基础接口
    [key: string]: any;
}

let g_readtagThreads = 0;


// Definitions provider based on readtags command line utility
// https://docs.ctags.io/en/latest/man/readtags.1.html
class ReadtagsProvider {
    private exec: (command: string, options: ExecOptions) => Promise<string[]>;
    private context: vscode.ExtensionContext;
    private _onDidChangeDocumentSymbols = new vscode.EventEmitter<void>();
    public readonly onDidChangeDocumentSymbols = this._onDidChangeDocumentSymbols.event;

    constructor(context: vscode.ExtensionContext, exec: (command: string, options: ExecOptions) => Promise<string[]>) {
        this.exec = exec;
        this.context = context;
    }

    public refresh(): void {
        this._onDidChangeDocumentSymbols.fire();
    }

    getCommandPath(command: string): string {
        return path.join(this.context.extensionPath, command);
    }

    async getDefinition(document: vscode.TextDocument, position: vscode.Position, showCommand: boolean = false): Promise<vscode.SymbolInformation[]> {

        outputChannel.appendLine(`getDefinition: ${position.line}, ${position.character}`);
        const range = document.getWordRangeAtPosition(position);
        const symbol = document.getText(range);
        const scope = determineScope(document);
        if (scope === undefined) {
            return [];
        }
        if (range != undefined) {
            const uri = vscode.Uri.file(path.relative(vscode.workspace.workspaceFolders![0].uri.fsPath, document.uri.fsPath));

            const symbolInfo = new vscode.SymbolInformation(symbol, vscode.SymbolKind.Function, '', new vscode.Location(uri, range));
            const fp_definitions = g_functionPointerMap.getDefinitions(symbolInfo);
            if (fp_definitions != undefined && fp_definitions.length > 0) {
                return fp_definitions.map(d => relativeToAbsolute(d, scope));
            }

        } else {
            const fp_definitions = g_functionPointerMap.getDefinitionsBySymbol(symbol);
            if (fp_definitions.length > 0) {
                return fp_definitions.map(d => relativeToAbsolute(d, scope));
            }
        }

        const cmd = process.platform === 'win32' ? readtagsGoToDefinitionCommandWin : readtagsGoToDefinitionCommand;
        const command = this.getCommandPath(cmd);
        const cwd = scope.uri.fsPath;
        const cmdline = `${command} '${symbol}'`;
        if (showCommand) {
            show_command(cmdline + " " + cwd);
        }

        const definitions = await this.exec(cmdline, { cwd });
        let symbolInfos = definitions.map(d => definitionToSymbolInformation(d, scope));

        return symbolInfos;
    }

    async provideDefinition(document: vscode.TextDocument, position: vscode.Position): Promise<vscode.Location[]> {
        //await checkDatabaseoutput
        if (g_readtagThreads > 1) {
            return [];
        }
        g_readtagThreads++;
        setFindDefinitionTrigger(true);
        outputChannel.appendLine(`provideDefinition: ${position.line}, ${position.character}`);
        const scope = determineScope(document);

        let text = document.lineAt(position.line).text;
        let regex = /#include\s*[<"]?(?<fileName>[\w\/]+.h)[">]?\s*/;

        if (regex.test(text)) {
            let match = regex.exec(text);
            let fileName = match!.groups!.fileName;
            const symbolInfos = await findFiles(fileName);
            g_readtagThreads--;
            return symbolInfos.map(({ location }) => location);
        }

        let wordrange = document.getWordRangeAtPosition(position);
        let symbolInfos: vscode.SymbolInformation[] = [];
        let origSymbolInfos: vscode.SymbolInformation[] = [];
        if (! await checkDatabase(DatabaseType.CTAGS, false)) {
            if (!await checkDatabase(DatabaseType.CSCOPE, false)) {
                g_readtagThreads--;
                outputChannel.append(`No ctags or cscope database found for ${document.uri.fsPath}`);
                return [];
            }
            origSymbolInfos = await findDefinition(document.getText(wordrange));
        } else {
            origSymbolInfos = await this.getDefinition(document, position, true);
            if (origSymbolInfos.length === 0) {
                const word = document.getText(wordrange);
                if (word === "") {
                    outputChannel.append(`No definition found for empty word in ${document.uri.fsPath}, try cscope...`);
                    g_readtagThreads--;
                    return [];
                } else {
                    outputChannel.append(`No definition found for ${word} in ${document.uri.fsPath}, try cscope...`);
                }
                // If no definition found, try to find it using cscope
                origSymbolInfos = await findDefinition(word);
            }
        }
        if (origSymbolInfos.length === 0) {
            g_readtagThreads--;
            outputChannel.append(`No definition found for ${document.uri.fsPath} at line ${position.line + 1}`);
            return [];
        }
        const filteredSymbolInfos = filterSymbolInfos(origSymbolInfos);
        if (filteredSymbolInfos.length > 0) {
            symbolInfos = filteredSymbolInfos;
        } else {
            symbolInfos = origSymbolInfos;
        }
        const caller_symbol = findFunctionNameByLine(document, position.line + 1);
        wordrange ??= new vscode.Range(position, position);
        const uri = vscode.Uri.file(path.relative(vscode.workspace.workspaceFolders![0].uri.fsPath, document.uri.fsPath));
        const caller_symbol_info = caller_symbol ? new vscode.SymbolInformation(caller_symbol,
            vscode.SymbolKind.Function, '', new vscode.Location(uri, wordrange)) : null;
        g_browserHistory.addSymbolInformationAbsPath(symbolInfos[0].kind === vscode.SymbolKind.Function ?
            symbolInfos : origSymbolInfos, scope, caller_symbol_info).then(() => {
                g_browserHistoryViewProvider?.refresh();
            });
        g_readtagThreads--;
        return symbolInfos.map(({ location }) => location);
    }

    async provideWorkspaceSymbols(query: string): Promise<vscode.SymbolInformation[]> {
        if (!query) return [];

        const results = await Promise.all(vscode.workspace.workspaceFolders!.map(async scope => {
            const cmd = process.platform === 'win32' ? readtagsGoToSymbolInWorkspaceCommandWin : readtagsGoToSymbolInWorkspaceCommand;
            const command = this.getCommandPath(cmd);
            const cwd = scope ? scope.uri.fsPath : "";
            const cmdline = `${command} '${query}'`;
            show_command(cmdline + " " + cwd);
            const definitions: string[] = await this.exec(cmdline, { cwd, maxBuffer: Infinity });
            let syminfos = definitions.map(d => definitionToSymbolInformation(d, scope));
            if (g_fnstructOnly) {
                syminfos = syminfos.filter(s => s.kind === vscode.SymbolKind.Function || s.kind === vscode.SymbolKind.Struct);
            }
            return syminfos;
        }));
        return results.flat();
    }

    async provideDocumentSymbols(document: vscode.TextDocument): Promise<vscode.DocumentSymbol[]> {
        const scope = determineScope(document);
        let macro_file = g_macroDefinitionManager.getLastLoadedFile();
        if (macro_file == undefined || macro_file == "" ) {
            macro_file = "";
        } else
            macro_file = "-m " + macro_file;

        if (vscode.workspace.workspaceFolders) {
            const workspacePath = vscode.workspace.workspaceFolders[0].uri.fsPath;
            const customMacrosFilePath = path.join(workspacePath, 'custom-macros.h');
            if (fs.existsSync(customMacrosFilePath)) {
                macro_file += " -m " + customMacrosFilePath;
            }
        }
        let cmd = process.platform === 'win32' ? ctagsGoToSymbolInEditorCommandWin : ctagsGoToSymbolInEditorCommand;
        cmd = eval(`\`${cmd}\``);
        const command = this.getCommandPath(cmd);
        const relativePath = vscode.workspace.asRelativePath(document.uri, false);
        if (!scope) {
            return [];
        }
        const cwd = scope.uri.fsPath;
        let ignoreWord = '-I ';
        ignoreWord += 'EXPORT_SYMBOL+';
        ignoreWord += ' EXPORT_SYMBOL_GPL+';
        ignoreWord += ' final override';
        const cmdline = `${command} -u '${ignoreWord}' '${relativePath}'`;
        show_command(cmdline + " " + cwd);
        const definitions = await this.exec(cmdline, { cwd });
        let symbolInfos = definitions.map(d => definitionToSymbolInformation(d, scope));

        if (g_functionOnlyFilter) {
            if (g_nonstatic) {
                symbolInfos = symbolInfos.filter((d) => d.kind === vscode.SymbolKind.Function && !d.containerName.includes("static "));
            } else {
                symbolInfos = symbolInfos.filter((d) => d.kind === vscode.SymbolKind.Function);
            }
        }

        // 将平面的 symbolInfos 转换为树状结构
        const symbolMap = new Map<string, vscode.DocumentSymbol>();
        const rootSymbols: vscode.DocumentSymbol[] = [];
        
        for (const sym of symbolInfos) {
            const range = new vscode.Range(
                new vscode.Position(sym.location.range.start.line, 0),
                new vscode.Position(sym.location.range.start.line, 100) // 假设行长度
            );
            
            const symbol = new vscode.DocumentSymbol(
                sym.name,
                sym.containerName || '',
                sym.kind,
                range,
                range
            );
            
            if (sym.containerName) {
                // 处理嵌套的命名空间/类，如 aaa::bbb::ccc
                let containerFound = false;
                let containerName = sym.containerName;
                
                // 尝试完整的容器名
                if (symbolMap.has(containerName)) {
                    symbolMap.get(containerName)!.children.push(symbol);
                    containerFound = true;
                } else {
                    // 如果完整名称不存在，尝试逐层查找
                    const parts = containerName.split('::');
                    
                    // 从最具体的部分开始，逐渐扩大范围
                    for (let i = parts.length - 1; i >= 0; i--) {
                        const partialContainer = parts[i];
                        if (symbolMap.has(partialContainer)) {
                            const fsym = symbolMap.get(partialContainer);
                            if (fsym!.kind == vscode.SymbolKind.Function) {
                                continue;
                            }
                            if (i == parts.length - 1) {
                                symbol.detail = '';
                            } else {
                                symbol.detail = parts.slice(i + 1).join('::');
                            }
                            symbol.detail = parts[i + 1];
                            fsym!.children.push(symbol);
                            containerFound = true;
                            break;
                        }
                    }
                }
                
                // 如果所有尝试都失败，添加到根
                if (!containerFound) {
                    rootSymbols.push(symbol);
                }
            } else {
                // 没有容器名，直接添加到根
                rootSymbols.push(symbol);
            }
            
            // 存储符号以便子成员引用
            symbolMap.set(sym.name, symbol);
        }
        
        return rootSymbols;
    }
}

export { ReadtagsProvider };
