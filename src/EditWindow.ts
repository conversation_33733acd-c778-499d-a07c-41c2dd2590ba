import { g_browserHistory } from "./extension";
import { g_macroDefinitionManager } from './MacroDefinition';

import * as vscode from 'vscode';
const fs = require('fs');

import path = require('path');
import { g_browserHistoryViewProvider, registerBrowserHistoryView } from './BrowserHistoryView';
import { getSymbolDefinitions, registerSymbolPreviewView, saveSymbolPreviewSettings } from './SymbolPreviewView';
import { filterSymbolInfos, findFunctionNameByLine, outputChannel, wrapExec } from './UtilFuns';
import { ReadtagsProvider } from './ReadTags';
import { promisify } from 'util';
import { exec } from 'child_process';


export let g_functionOnlyFilter: boolean = false;
export let g_nonstatic: boolean = false;
export let g_showNonActiveCodeAsGrey: boolean = false; // Default to showing non-active code as grey
let g_middleClick = false;

export function setNonActiveCodeAsGrey(enable: boolean) {
    g_showNonActiveCodeAsGrey = enable;
    refreshDocumentSymbols();
}
// Function to refresh document symbols by triggering a document change
async function refreshDocumentSymbols() {
    const editor = vscode.window.activeTextEditor;
    if (editor) {
        // Create a temporary edit
        const edit = new vscode.WorkspaceEdit();
        const position = new vscode.Position(0, 0);
        const tempText = ' '; // Temporary space

        // Apply and immediately undo the edit
        edit.insert(editor.document.uri, position, tempText);
        await vscode.workspace.applyEdit(edit);

        // Undo the edit
        await vscode.commands.executeCommand('undo');
    }
}

// Add this function to create decorations for ifdef blocks
function createIfdefDecorations() {
    // Create a decoration type for greyed-out code
    const ifdefDecorationType = vscode.window.createTextEditorDecorationType({
        opacity: '0.6', // Makes text appear grey/faded
        color: '#6a9955' // Use a grey color
    });

    return ifdefDecorationType;
}

// Add this function to apply decorations to ifdef blocks
async function updateIfdefDecorations(editor: vscode.TextEditor, decorationType: vscode.TextEditorDecorationType) {
    if (!editor || !editor.document) return;

    // Only apply to C/C++ files
    if (editor.document.languageId !== 'c' && editor.document.languageId !== 'cpp') return;

    // Check if we should show non-active code as grey
    if (!g_showNonActiveCodeAsGrey) {
        // Clear any existing decorations
        editor.setDecorations(decorationType, []);
        return;
    }

    // Check if macro definition manager is available
    if (!g_macroDefinitionManager) {
        outputChannel.appendLine('Macro definition manager not available');
        return;
    }

    const text = editor.document.getText();
    const decorationsArray: vscode.DecorationOptions[] = [];

    // Track ifdef nesting level and state
    interface IfdefState {
        level: number;
        startLine: number;
        endLine: number;
        macroName: string;
        shouldGrey: boolean;
        isElseBranch: boolean;
        conditionHit: boolean;
        expression?: string; // Store the original expression for complex conditions
    }

    /**
     * Evaluates a complex preprocessor condition using the macro definition manager
     * @param expression The preprocessor condition to evaluate
     * @returns true if the condition evaluates to a non-zero value, false otherwise
     */
    function evaluatePreprocessorCondition(expression: string): boolean {
        //outputChannel.appendLine(`Evaluating preprocessor condition: ${expression}`);

        // Handle simple defined() and !defined() cases
        if (expression.match(/^\s*defined\s*\(\s*\w+\s*\)\s*$/)) {
            // Simple defined(MACRO) case
            const match = expression.match(/defined\s*\(\s*(\w+)\s*\)/);
            if (match && match[1]) {
                const macroName = match[1];
                const macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName);
                return macroValue !== 0;
            }
        } else if (expression.match(/^\s*!\s*defined\s*\(\s*\w+\s*\)\s*$/)) {
            // Simple !defined(MACRO) case
            const match = expression.match(/!\s*defined\s*\(\s*(\w+)\s*\)/);
            if (match && match[1]) {
                const macroName = match[1];
                const macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName);
                return macroValue === 0;
            }
        }

        // Handle complex expressions
        let result = false;
        try {
            // Replace defined(X) with the actual value (1 or 0)


            let definedRegex = /defined\s*\(\s*(\w+)\s*\)/g;
            let processedExpr = expression.replace(definedRegex, (match, macroName) => {
                const macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName);
                return macroValue !== 0 ? '1' : '0';
            });

            definedRegex = /defined\s+(\w+)/g;
            processedExpr = processedExpr.replace(definedRegex, (match, macroName) => {
                const macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName);
                return macroValue !== 0 ? '1' : '0';
            });

            // Replace !defined(X) with the negated value
            let notDefinedRegex = /!\s*defined\s*\(\s*(\w+)\s*\)/g;
            processedExpr = processedExpr.replace(notDefinedRegex, (match, macroName) => {
                const macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName);
                return macroValue === 0 ? '1' : '0';
            });

            notDefinedRegex = /!\s*defined\s+(\w+)/g;
            processedExpr = processedExpr.replace(notDefinedRegex, (match, macroName) => {
                const macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName);
                return macroValue === 0 ? '1' : '0';
            });

            let enabledRegex = /IS_ENABLED\(\s*(\w+)\s*\)/g;
            processedExpr = processedExpr.replace(enabledRegex, (match, macroName) => {
                let macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName);
                if (macroValue === 0) {
                    macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName + "_MODULE");
                }
                return macroValue !== 0 ? '1' : '0';
            });

            enabledRegex = /!\s*IS_ENABLED\(\s*(\w+)\s*\)/g;
            processedExpr = processedExpr.replace(enabledRegex, (match, macroName) => {
                let macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName);
                if (macroValue === 0) {
                    macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName + "_MODULE");
                }
                return macroValue === 0 ? '1' : '0';
            });

            // Find all potential macro names (alphanumeric + underscore)
            const macroPattern = /\b[A-Za-z_][A-Za-z0-9_]*\b/g;
            let match;

            while ((match = macroPattern.exec(processedExpr)) !== null) {
                const macroName = match[0];
                // Skip common C keywords and operators
                if (!['if', 'else', 'endif', 'defined', 'true', 'false', 'NULL'].includes(macroName)) {
                    const macroValue = g_macroDefinitionManager.getMacroValue(macroName);
                    if (macroValue !== '0' || g_macroDefinitionManager.getAllMacros().some(([name]) => name === macroName)) {
                        // Replace all occurrences of this macro with its value
                        const macroRegex = new RegExp(`\\b${macroName}\\b`, 'g');
                        processedExpr = processedExpr.replace(macroRegex, macroValue);
                    }
                }
            } 
            
            // // Replace && with logical AND
            // processedExpr = processedExpr.replace(/&&/g, '&');

            // // Replace || with logical OR
            // processedExpr = processedExpr.replace(/\|\|/g, '|');


            // // Use a safer evaluation approach
            // // Convert the expression to a form that can be evaluated
            // const safeExpr = processedExpr
            //     .replace(/&/g, ' && ')
            //     .replace(/\|/g, ' || ')
            //     .replace(/!/g, ' ! ');
            
            const safeExpr = processedExpr;
            //outputChannel.appendLine(`Processed expression: ${safeExpr}`);
            result = new Function(`return !!(${safeExpr})`)();
            //outputChannel.appendLine(`Expression result: ${result}`);
        } catch (error) {
            outputChannel.appendLine(`Error evaluating expression: ${error}`);
        }

        return result;

    }

    const ifdefStack: IfdefState[] = [];

    // Process the document line by line
    const lines = text.split('\n');
    for (let i = 0; i < lines.length; i++) {
        let line = lines[i].trim();

        while (line.endsWith('\\')) {
            line = line.slice(0, -1) + lines[i + 1];
            i++;
            line = line.trim();
            if ( i == lines.length)
                break;
        }
        // Check for complex #if expressions with defined() operators
        if (line.startsWith('#if ')) {
            // Check if it's a complex expression with && or ||
            const isComplexExpression = line.includes('&&') || line.includes('||');

            if (true) {
                // Extract the condition part after #if
                const condition = line.substring(3).trim();

                // Evaluate the complex condition
                let conditionResult = false;
                let shouldGrey = true;

                try {
                    conditionResult = evaluatePreprocessorCondition(condition);
                    shouldGrey = !conditionResult;

                    // If we're inside a greyed-out block, this block should also be greyed out
                    if (ifdefStack.length > 0) {
                        const parentState = ifdefStack[ifdefStack.length - 1];
                        if (parentState.shouldGrey) {
                            shouldGrey = true;
                        }
                    }

                    // Push state to stack
                    ifdefStack.push({
                        level: ifdefStack.length,
                        startLine: i + 1,
                        endLine: 0,
                        macroName: 'complex_condition',
                        shouldGrey: shouldGrey,
                        isElseBranch: false,
                        conditionHit: conditionResult,
                        expression: condition
                    });

                    //outputChannel.appendLine(`#if ${condition}: result=${conditionResult}, shouldGrey=${shouldGrey}`);
                } catch (error) {
                    outputChannel.appendLine(`Error evaluating complex condition: ${error}`);
                    // Default to not greying out on error
                    ifdefStack.push({
                        level: ifdefStack.length,
                        startLine: i + 1,
                        endLine: 0,
                        macroName: 'complex_condition',
                        shouldGrey: false,
                        isElseBranch: false,
                        conditionHit: true,
                        expression: condition
                    });
                }

                continue; // Skip to the next line
            }
        }

        // Check for #ifdef
        if (line.startsWith('#ifdef') ) {
            // Extract macro name
            let match = line.match(/#ifdef\s+(\w+)/);

            if (match && match[1]) {
                const macroName = match[1];
                // Check if macro is defined and has a non-zero value
                let shouldGrey = false;
                let macroValue = 0;
                if (ifdefStack.length == 0) {
                    macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName);
                    shouldGrey = (macroValue === 0);
                } else if (ifdefStack.length > 0) {
                    const parentState = ifdefStack[ifdefStack.length - 1];
                    if (parentState.shouldGrey) {
                        shouldGrey = true;
                    } else {
                        macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName);
                        shouldGrey = (macroValue === 0);
                    }
                }
                // Push state to stack
                ifdefStack.push({
                    level: ifdefStack.length,
                    startLine: i + 1,
                    endLine: 0,
                    macroName: macroName,
                    shouldGrey: shouldGrey,
                    isElseBranch: false,
                    conditionHit: !shouldGrey
                });

                //outputChannel.appendLine(`#ifdef ${macroName}: value=${macroValue}, shouldGrey=${shouldGrey}`);
            }
        }
        // Check for #ifndef
        else if (line.startsWith('#ifndef')) {
            // Extract macro name
            let match = line.match(/#ifndef\s+(\w+)/);
            if (line.startsWith('#if !defined'))
                // Extract macro name - handle both defined(X) and defined (X) formats
                match = line.match(/#if\s+!defined\s*\(\s*(\w+)\s*\)/);
            if (match && match[1]) {
                const macroName = match[1];
                // Check if macro is defined and has a non-zero value
                const macroValue = g_macroDefinitionManager.getMacroNumericValue(macroName);
                let shouldGrey = (macroValue !== 0); // Opposite of #ifdef
                if (ifdefStack.length > 0) {
                    const parentState = ifdefStack[ifdefStack.length - 1];
                    if (parentState.shouldGrey) {
                        shouldGrey = true;
                    }
                }
                // Push state to stack
                ifdefStack.push({
                    level: ifdefStack.length,
                    startLine: i + 1,
                    endLine: 0,
                    macroName: macroName,
                    shouldGrey: shouldGrey,
                    isElseBranch: false,
                    conditionHit: !shouldGrey
                });

                //outputChannel.appendLine(`#ifndef ${macroName}: value=${macroValue}, shouldGrey=${shouldGrey}`);
            }
        }
        // Handle #elif directives
        else if (line.startsWith('#elif')) {
            if (ifdefStack.length > 0) {
                const currentState = ifdefStack.pop()!;

                // Extract the condition part after #elif
                const condition = line.substring(5).trim();

                // If the previous condition was true (not greyed out),
                // then we should grey out this #elif block
                if (!currentState.shouldGrey) {
                    // Previous condition was true, so grey out this block
                    ifdefStack.push({
                        level: currentState.level,
                        startLine: i + 1,
                        endLine: 0,
                        macroName: currentState.macroName,
                        shouldGrey: true,
                        isElseBranch: true,
                        conditionHit: true,
                        expression: condition
                    });

                    //outputChannel.appendLine(`#elif ${condition}: Previous condition was true, greying out this block`);
                } else {
                    // Previous condition was false, evaluate this condition
                    let conditionResult = false;
                    let shouldGrey = true;

                    // Check if it's a complex expression with defined() operators
                    try {
                        conditionResult = evaluatePreprocessorCondition(condition);
                        shouldGrey = !conditionResult;
                    } catch (error) {
                        outputChannel.appendLine(`Error evaluating #elif condition: ${error}`);
                        // Default to not greying out on error
                        shouldGrey = false;
                    }
                    let startLine = currentState.startLine;
                    let endLine = 0;

                    // If already meet condition, elif shall ignore
                    if (currentState.conditionHit) {
                        shouldGrey = true;
                        conditionResult = true;
                    } else {
                        if (!shouldGrey) {
                            //last block grey stop
                            currentState.endLine = i - 1;
                            currentState.conditionHit = true;
                            const state = currentState;
                                                // Make sure the range is valid
                            if (state.startLine <= state.endLine && state.startLine >= 0 && state.endLine < lines.length) {
                                decorationsArray.push({
                                    range: new vscode.Range(state.startLine, 0,
                                        state.endLine, lines[state.endLine].length)
                                });

                                //outputChannel.appendLine(`Adding decoration for lines ${state.startLine} to ${state.endLine}`);
                            }
                            startLine = i + 1;
                            endLine = 0;
                        }
                    }

                    ifdefStack.push({
                        level: currentState.level,
                        startLine: startLine,
                        endLine: endLine,
                        macroName: currentState.macroName,
                        shouldGrey: shouldGrey,
                        isElseBranch: true,
                        conditionHit: conditionResult,
                        expression: condition
                    });

                    //outputChannel.appendLine(`#elif ${condition}: result=${conditionResult}, shouldGrey=${shouldGrey}`);
                }
            }
        }
        // Handle #else directives
        else if (line.startsWith('#else')) {
            // Toggle the grey state for the current level
            if (ifdefStack.length > 0) {
                const currentState = ifdefStack.pop()!;
                let shouldGrey = currentState.shouldGrey;

                if (currentState.shouldGrey && currentState.endLine == 0 && !currentState.conditionHit) {
                    currentState.endLine = i - 1;
                    const state = currentState;
                    if (state.startLine <= state.endLine && state.startLine >= 0 && state.endLine < lines.length) {
                        decorationsArray.push({
                            range: new vscode.Range(state.startLine, 0,
                                state.endLine, lines[state.endLine].length)
                        });

                        //outputChannel.appendLine(`Adding decoration for lines ${state.startLine} to ${state.endLine}`);
                    }
                    currentState.startLine = i + 1;
                    currentState.endLine = 0;
                    shouldGrey = false
                } else {
                    if (currentState.conditionHit && !currentState.shouldGrey) {
                        currentState.startLine = i + 1;
                        shouldGrey = true;
                    } else if (currentState.conditionHit && currentState.shouldGrey) {
                        currentState.endLine = 0;
                    } else {

                    }
                }
                ifdefStack.push({
                    ...currentState,
                    shouldGrey: shouldGrey,
                    isElseBranch: true
                });
            }
        }

        // Check for #endif
        else if (line.startsWith('#endif')) {
            // Process the block if we have a matching #ifdef/#ifndef/#if defined
            if (ifdefStack.length > 0) {
                const state = ifdefStack.pop()!;

                // If we should grey out this block, add decorations
                if (state.shouldGrey) {
                    // Find the range to grey out
                    if (state.endLine == 0) {
                        state.endLine = i - 1;
                    }

                    // Make sure the range is valid
                    if (state.startLine <= state.endLine && state.startLine >= 0 && state.endLine < lines.length) {
                        decorationsArray.push({
                            range: new vscode.Range(state.startLine, 0,
                                state.endLine, lines[state.endLine].length)
                        });

                        //outputChannel.appendLine(`Adding decoration for lines ${state.startLine} to ${state.endLine}`);
                    } else {
                        //outputChannel.appendLine(`Invalid decoration range: ${state.startLine} to ${state.endLine}`);
                    }
                }

                // Log the state being popped
                const stateInfo = state.expression
                    ? `${state.macroName} (${state.expression})`
                    : state.macroName;

                //outputChannel.appendLine(`#endif: Popped state for ${stateInfo}, shouldGrey=${state.shouldGrey}`);
            }
        }
    }

    // Apply decorations
    editor.setDecorations(decorationType, decorationsArray);
}

export function registerEditWindowCommand(context: vscode.ExtensionContext) {
    // Create decoration type
    const ifdefDecorationType = createIfdefDecorations();

    // Update decorations when a text editor is opened or changed

    context.subscriptions.push(
        vscode.window.onDidChangeActiveTextEditor(editor => {
            if (editor) {
                outputChannel.appendLine(`onDidChangeActiveTextEditor: ${editor.document.uri.fsPath}`);
                updateIfdefDecorations(editor, ifdefDecorationType);
            }
        })
    );



    // Update decorations when text document changes
    context.subscriptions.push(
        vscode.workspace.onDidChangeTextDocument(event => {
            const editor = vscode.window.activeTextEditor;
            if (editor && event.document === editor.document) {
                //outputChannel.appendLine(`onDidChangeTextDocument: ${event.document.uri.fsPath}`);
                updateIfdefDecorations(editor, ifdefDecorationType);
            }
        })
    );

    // Register a callback when a file is opened
/*
    context.subscriptions.push(
        vscode.workspace.onDidOpenTextDocument(document => {
            // Apply decorations if this is a text editor
            const editor = vscode.window.visibleTextEditors.find(e => e.document === document);
            if (editor) {
                outputChannel.appendLine(`onDidOpenTextDocument: ${document.uri.fsPath}`);
                updateIfdefDecorations(editor, ifdefDecorationType);
            }
        })
    );
*/
    // Apply to current editor if any
    if (vscode.window.activeTextEditor) {
        updateIfdefDecorations(vscode.window.activeTextEditor, ifdefDecorationType);
    }

    vscode.commands.executeCommand(
        'setContext',
        'sourceseek.nonStaticTitle',
        'Show Non-Static Only');

    // Initialize the context variable
    vscode.commands.executeCommand('setContext', 'sourceseek.functionOnlyFilter', g_functionOnlyFilter);

    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.toggleFunctionOnlyFilter', () => {
            g_functionOnlyFilter = !g_functionOnlyFilter;
            vscode.commands.executeCommand('setContext', 'sourceseek.functionOnlyFilter', g_functionOnlyFilter);
            //provider.refresh();
            //refreshOutlineByForceSave();
            refreshDocumentSymbols();
            // Refresh the outline view
            // vscode.commands.executeCommand('outline.refresh');
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.toggleNonStatic', () => {
            g_nonstatic = !g_nonstatic;

            // Update configuration
            vscode.workspace.getConfiguration('sourceseek').update(
                'nonStaticTitle',
                g_nonstatic ? 'Show All Functions' : 'Show Non-Static Only',
                true
            );

            // Rest of your code...
            vscode.commands.executeCommand('setContext', 'sourceseek.nonStaticOnly', g_nonstatic);
            vscode.commands.executeCommand('outline.refresh');

            // Show a notification
            vscode.window.showInformationMessage(
                g_nonstatic ? 'Showing non-static functions only' : 'Showing all functions'
            );
            g_functionOnlyFilter = true;
            refreshDocumentSymbols();
        })
    );

        // Register the sourceseek.searchSymbol command
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.searchSymbol', () => {
            const editor = vscode.window.activeTextEditor;
            if (editor) {
                const selection = editor.selection;
                if (!selection.isEmpty) {
                    const selectedText = editor.document.getText(selection);
                    // Open the search view with the selected text pre-filled
                    vscode.commands.executeCommand('workbench.action.findInFiles', {
                        query: selectedText,
                        triggerSearch: true,
                        matchWholeWord: true,
                        isCaseSensitive: true
                    });
                } else {
                    // If no text is selected, get the word at the cursor position
                    const position = editor.selection.active;
                    const wordRange = editor.document.getWordRangeAtPosition(position);
                    if (wordRange) {
                        const word = editor.document.getText(wordRange);
                        vscode.commands.executeCommand('workbench.action.findInFiles', {
                            query: word,
                            triggerSearch: true,
                            matchWholeWord: true,
                            isCaseSensitive: true
                        });
                    }
                }
            }
        })
    );
    // Register the command to open definition in right window
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.openInRightWindow', async () => {
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                return;
            }

            // Get the current position
            const position = editor.selection.active;
            const document = editor.document;

            try {
                // Create ReadtagsProvider instance
                const readtagsProvider = new ReadtagsProvider(context, wrapExec(promisify(exec)));

                // Get symbol definitions
                const symbolInfos = await getSymbolDefinitions(document, position, readtagsProvider);
                const filteredSymbolInfos = filterSymbolInfos(symbolInfos);

                if (filteredSymbolInfos.length === 0) {
                    vscode.window.showInformationMessage('No definition found');
                    return;
                }

                // Get the first definition
                const definition = filteredSymbolInfos[0].location;
                const definitionUri = definition.uri;
                const definitionRange = definition.range;

                // Open the document in the right editor group
                await vscode.commands.executeCommand('vscode.openWith', definitionUri, 'default', {
                    viewColumn: vscode.ViewColumn.Two,
                    preserveFocus: true // Keep focus on the original window
                });

                // Get the active editor in the right editor group
                const rightEditor = vscode.window.visibleTextEditors.find(
                    e => e.viewColumn === vscode.ViewColumn.Two &&
                         e.document.uri.toString() === definitionUri.toString()
                );

                if (rightEditor) {
                    // Reveal the range and position the cursor at the beginning of the symbol
                    rightEditor.revealRange(definitionRange, vscode.TextEditorRevealType.InCenter);
                    rightEditor.selection = new vscode.Selection(definitionRange.start, definitionRange.start);
                } else {
                    vscode.window.showErrorMessage('Failed to open editor in right window');
                }

                // Add to browser history
                const callHierarchyItem = new vscode.CallHierarchyItem(
                    filteredSymbolInfos[0].kind,
                    filteredSymbolInfos[0].name,
                    filteredSymbolInfos[0].containerName || '',
                    definitionUri,
                    definitionRange,
                    definitionRange
                );

                g_browserHistory.addCallHierarchyItem(callHierarchyItem);
                g_browserHistoryViewProvider?.refresh();

            } catch (error) {
                vscode.window.showErrorMessage(`Failed to open definition: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );

    // Register a handler for middle-click in the editor
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.markBootmark', () => {
        //vscode.window.onDidChangeTextEditorSelection(async (event) => {
            // Check if this is a middle-click event
            // Unfortunately, VS Code API doesn't directly tell us which mouse button was clicked
            // We'll need to use a workaround by checking if the selection is empty and different from the previous one

            // Get the active editor
            // const editor = event.textEditor;
            const editor = vscode.window.activeTextEditor;
            if (!editor) {
                return;
            }

            // Check if the selection is empty (just a cursor position)
            /*
            if (!event.selections[0].isEmpty) {
                return;
            }
                */

            // We need to detect middle-click through keyboard modifiers
            // Since VS Code doesn't expose mouse button info directly, we'll use a command context
            //const isMiddleClick = await vscode.commands.executeCommand('sourceseek.isMiddleClick');
            if (!g_middleClick) {
                g_middleClick = false;
            }


            try {
                // Get the current position
                //const position = event.selections[0].active;
                const position = editor.selection.active;
                const document = editor.document;

                // Create ReadtagsProvider instance
                // const readtagsProvider = new ReadtagsProvider(context, wrapExec(promisify(exec)));
                let callname = findFunctionNameByLine(editor.document, position.line + 1);
                let line: number = position.line + 1;
                let kind = vscode.SymbolKind.Function;
                if (!callname) {
                    callname = path.basename(editor.document.uri.fsPath) + '+' + line;
                    kind = vscode.SymbolKind.Null;
                } else {
                    callname = callname + '+' + line;
                }
                const uri = vscode.Uri.file(path.relative(vscode.workspace.workspaceFolders![0].uri.fsPath, document.uri.fsPath));
                // Create a CallHierarchyItem from the SymbolInformation
                const callHierarchyItem = new vscode.CallHierarchyItem(
                    kind,
                    callname,
                    '',
                    uri,
                    new vscode.Range(position.line, position.character, position.line, position.character),
                    new vscode.Range(position.line, position.character, position.line, position.character)
                );

                // Add to browser history
                g_browserHistory.addCallHierarchyItem(callHierarchyItem);
                g_browserHistoryViewProvider?.refresh();

            } catch (error) {
                console.error('Failed to add symbol to history:', error);
            }
        })
    );

    // Register a command to check for middle-click
    // This will be used with a keybinding
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.isMiddleClick', () => {
            // This is a placeholder - the actual detection will be done through keybindings
            g_middleClick = true;
        })
    );

    // Register a command to toggle showing non-active code as grey
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.toggleNonActiveCodeAsGrey', () => {
            g_showNonActiveCodeAsGrey = !g_showNonActiveCodeAsGrey;

            // Update all visible editors
            vscode.window.visibleTextEditors.forEach(editor => {
                updateIfdefDecorations(editor, ifdefDecorationType);
            });

            // Show a notification
            vscode.window.showInformationMessage(
                g_showNonActiveCodeAsGrey
                    ? 'Showing non-active code as grey'
                    : 'Showing all code normally'
            );
        })
    );

    // Register a command to show the macro definition file
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.showMacroDefinitionFile', async () => {
            if (!g_macroDefinitionManager) {
                vscode.window.showErrorMessage('Macro definition manager not available');
                return;
            }

            const filePath = g_macroDefinitionManager.getLastLoadedFile();
            if (!filePath) {
                vscode.window.showInformationMessage('No macro definition file has been loaded');
                return;
            }

            try {
                // Open the file in the editor
                const document = await vscode.workspace.openTextDocument(filePath);
                await vscode.window.showTextDocument(document);
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to open macro definition file: ${error instanceof Error ? error.message : String(error)}`);
            }
        })
    );
}
