import * as vscode from 'vscode';
import { EXTENSION_ID } from './globalSettings';
import { outputChannel } from './UtilFuns';

/**
 * Comment format types
 */
export enum CommentFormat {
    SINGLE_LINE = '//',
    BLOCK = '/* */'
}

/**
 * Toggle comments for selected lines in the active editor
 */
export async function toggleComment(): Promise<void> {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showWarningMessage('No active editor found');
        return;
    }

    const document = editor.document;
    const selection = editor.selection;

    // If no selection, select the current line
    let startLine = selection.start.line;
    let endLine = selection.end.line;

    // If selection is empty, use current line
    if (selection.isEmpty) {
        startLine = endLine = selection.active.line;
    }

    // Get comment format from configuration
    const config = vscode.workspace.getConfiguration(EXTENSION_ID);
    const commentFormatSetting = config.get<string>('commentFormat', '//');
    const commentFormat = commentFormatSetting === '/* */' ? CommentFormat.BLOCK : CommentFormat.SINGLE_LINE;

    try {
        await editor.edit(editBuilder => {
            // Process each line in the selection
            for (let lineNumber = startLine; lineNumber <= endLine; lineNumber++) {
                const line = document.lineAt(lineNumber);
                const lineText = line.text;
                const trimmedText = lineText.trim();

                // Skip empty lines
                if (trimmedText.length === 0) {
                    continue;
                }

                if (commentFormat === CommentFormat.SINGLE_LINE) {
                    toggleSingleLineComment(editBuilder, line, lineText, trimmedText);
                } else {
                    toggleBlockComment(editBuilder, line, lineText, trimmedText);
                }
            }
        });

        outputChannel.appendLine(`Toggled comments for lines ${startLine + 1} to ${endLine + 1} using ${commentFormatSetting} format`);
    } catch (error) {
        vscode.window.showErrorMessage(`Error toggling comments: ${error instanceof Error ? error.message : String(error)}`);
        outputChannel.appendLine(`Error toggling comments: ${error}`);
    }
}

/**
 * Toggle single-line comments (//)
 */
function toggleSingleLineComment(editBuilder: vscode.TextEditorEdit, line: vscode.TextLine, lineText: string, trimmedText: string): void {
    const lineRange = line.range;
    
    // Check if line is already commented
    if (trimmedText.startsWith('//')) {
        // Uncomment: remove // and any following space
        const commentIndex = lineText.indexOf('//');
        let newText = lineText.substring(0, commentIndex);
        let afterComment = lineText.substring(commentIndex + 2);
        
        // Remove one space after // if it exists
        if (afterComment.startsWith(' ')) {
            afterComment = afterComment.substring(1);
        }
        
        newText += afterComment;
        editBuilder.replace(lineRange, newText);
    } else {
        // Comment: add // at the beginning of non-whitespace content
        const firstNonWhitespaceIndex = lineText.search(/\S/);
        if (firstNonWhitespaceIndex !== -1) {
            const beforeContent = lineText.substring(0, firstNonWhitespaceIndex);
            const afterContent = lineText.substring(firstNonWhitespaceIndex);
            const newText = beforeContent + '// ' + afterContent;
            editBuilder.replace(lineRange, newText);
        } else {
            // Line has only whitespace, add comment at the beginning
            editBuilder.replace(lineRange, '// ' + lineText);
        }
    }
}

/**
 * Toggle block comments (/ ** /)
 */
function toggleBlockComment(editBuilder: vscode.TextEditorEdit, line: vscode.TextLine, lineText: string, trimmedText: string): void {
    const lineRange = line.range;
    
    // Check if line is already commented with block comments
    if (trimmedText.startsWith('/*') && trimmedText.endsWith('*/')) {
        // Uncomment: remove /* and */ 
        let newText = trimmedText.substring(2, trimmedText.length - 2).trim();
        
        // Preserve original indentation
        const firstNonWhitespaceIndex = lineText.search(/\S/);
        if (firstNonWhitespaceIndex !== -1) {
            const indentation = lineText.substring(0, firstNonWhitespaceIndex);
            newText = indentation + newText;
        }
        
        editBuilder.replace(lineRange, newText);
    } else {
        // Comment: wrap content with /* */
        const firstNonWhitespaceIndex = lineText.search(/\S/);
        if (firstNonWhitespaceIndex !== -1) {
            const beforeContent = lineText.substring(0, firstNonWhitespaceIndex);
            const afterContent = lineText.substring(firstNonWhitespaceIndex);
            const newText = beforeContent + '/* ' + afterContent + ' */';
            editBuilder.replace(lineRange, newText);
        } else {
            // Line has only whitespace, add comment around the whitespace
            editBuilder.replace(lineRange, '/* ' + lineText + ' */');
        }
    }
}

/**
 * Register the comment toggle command
 */
export function registerCommentToggleCommand(context: vscode.ExtensionContext): void {
    const disposable = vscode.commands.registerCommand('sourceseek.toggleComment', toggleComment);
    context.subscriptions.push(disposable);
    
    outputChannel.appendLine('Comment toggle command registered');
}
