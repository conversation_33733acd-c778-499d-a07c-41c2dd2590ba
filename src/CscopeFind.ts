import * as vscode from 'vscode';
import { outputChannel } from './UtilFuns';
import { setFindAssignment, setFindGrep, setFindInclude } from './CallHierarchyProvider';

export function registerCscopeMoreCommand(context: vscode.ExtensionContext): void {
    // Set the global reference in extension.ts
    const extension = require('./extension');

    // Register the loadMacroDefinition command
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.findAssignment', async () => {
            try {
                // Get default path
                setFindAssignment(true);
                //vscode.commands.executeCommand('references-view.showCallHierarchy');
                vscode.commands.executeCommand('editor.action.goToReferences');
           
            } catch (error) {
            }
        })
    );

    // Register the custom macro manager command
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.gotoFile', async () => {
                await vscode.commands.executeCommand('editor.action.revealDefinition');
        })
    );
    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.findInclude', async () => {
                setFindInclude(true);
                //vscode.commands.executeCommand('references-view.showCallHierarchy');
                vscode.commands.executeCommand('editor.action.goToReferences');
        })
    );

    context.subscriptions.push(
        vscode.commands.registerCommand('sourceseek.findSymbol', async () => {
                setFindGrep(true);
                vscode.commands.executeCommand('editor.action.goToReferences');
        })
    );

}