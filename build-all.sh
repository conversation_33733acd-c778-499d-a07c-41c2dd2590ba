git co master
tsc --outDir dist --incremental
rm out/*
for ff in `ls dist/*.js`;do
	ff=`basename $ff`
	echo "generate out/$ff"
	echo terser dist/$ff --compress --mangel -o out/$ff
	terser dist/$ff --compress --mangel -o out/$ff
done 
vsce package $1

git co hobot
git rebase master
tsc --outDir dist --incremental
rm out/*
for ff in `ls dist/*.js`;do
	ff=`basename $ff`
	echo "generate out/$ff"
	echo terser dist/$ff --compress --mangel -o out/$ff
	terser dist/$ff --compress --mangel -o out/$ff
done 
vsce package $1

#cp SourceSeek-0.6.*.vsix /var/www/html/vscode/


