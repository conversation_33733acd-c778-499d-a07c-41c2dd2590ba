# 地平线C语言规范-linux内核

# 8 Linux内核检查项

## 8.1 全局/局部资源使用

### 8.1.1 Linux驱动中尽量把全局变量放到device参数变量里传递；

### 8.1.2 内核态局部变量占用堆栈不超过过512字节

## 8.2 锁与信号量

**Mutex**

### 8.2.1 确保mutex在lock和unlock之前被初始化（mutex_init）过

### 8.2.2 mutex的所有操作不能出现在中断上下文中

### 8.2.3 同一个mutex保护的API不能嵌套调用

### 8.2.4 确保mutex的lock和unlock的操作在所有执行分支上是成对出现的, 如果一个函数里面只加锁不解锁，需要在函数前加注释说明持有什么锁

### 8.2.5 确保没有拿了mutex未释放返回用户空间的情况

### 8.2.6 上层应用通过ioctl/read/write等系统调用方式调用到，如果关键区内有休眠或可能等待较久的硬件操作，用mutex_lock_interruptible，其它情况都用mutex_lock.

### 8.2.7 使用mutex_lock_interruptible，必须判断返回值，如果被信号打断，必须返回EAGAIN，让信号处理得于执行，同时HAL层库或应用需要判断系统调用的返回值，重新调用。

**Spinlock**

### 8.2.8 确保spinlock在lock和unlock之前被初始化（spin_lock_init）过

### 8.2.9 不推荐使用spin_lock_irq和spin_unlock_irq，如果一定要使用，需要加注释说明保护区的中断上下文属性

### 8.2.10 如果一个自旋锁需要同时出现在中断上半部和进程上下文中，在进程上下文中只能使用spin_lock_irqsave和spin_unlock_irqrestore，在中断上下文中可以使用spin_lock/spin_unlock

### 8.2.11 如果一个自旋锁只会出现在中断上半部，在中断不可重入的情况下使用spin_lock/spin_unlock，在中断可重入的情况下使用spin_lock_irqsave和spin_unlock_irqrestore

### 8.2.12 如果一个自旋锁只会出现在进程上下文中，尽量使用spin_lock/spin_unlock，避免关中断降低实时性，增加重要中断的响应时间

### 8.2.13 如果一个自旋锁需要同时出现在进程上下文和软中断中，尽量使用spin_lock_bh/spin_unlock_bh

### 8.2.14 同一个spinlock保护的API不能嵌套调用

### 8.2.15 确保spinlock的lock和unlock的操作在所有执行分支上是成对出现的, 如果一个函数里面只加锁不解锁，需要在函数前加注释说明持有什么锁

### 8.2.16 spinlock代码段中不能休眠和主动调度（如：sleep，schedule，mutex_lock等）

### 8.2.17 spinlock中间代码正常路径不要加打印（pr_debug除外），异常分支可以加打印，正常路径如果一定要加，需要注释；

## 8.3 原子性检查

### 8.3.1 设备驱动在一段代码中修改多个设备寄存器时需要锁保护，保证一致性，不加锁需要注释说明

### 8.3.2 设备驱动修改某个设备寄存器的某些位时，是先读到CPU寄存器，修改后再写回，这种情况需要锁保证原子性, 不加锁要加注释说明, 如果存在异构核访问的,需要硬件锁。

### 8.3.3 修改某个设备寄存器时,需要在拿锁的情况下进行设备有效性判断，防止并发的上下文中关闭时钟，关闭硬件等操作。

### 8.3.4 在关闭某个设备的时钟，或使能寄存器时，需要在拿锁的情况下设置状态变量，阻止后续或并发上下文的访问。

### 8.3.5 驱动中并发上下文除了应用的多进程多线程访问，还需考虑中断上半部，下半部，内核线程之间对全局变量以及全局结构衍生的指针指向的变量访问的原子性。

### 8.3.6 驱动中需考虑同一个进程的多线程调用，如果不支持，需在内核入口处就加上互斥，如果支持，需考虑共同一个fd的关联结构并发访问的互斥。

## 8.4 中断

### 8.4.1 上半部中无动态内存申请函数调用

### 8.4.2 无除了error用的log信息输出（调试版本中也可以加入pr_debug作为调试log）

### 8.4.3 无可以被抢占调度或者睡眠的函数（如：sleep，schedule，mutex_lock等）的调用

### 8.4.4 中断上半程序尽量简短，大部分中断处理流程需要在下半部运行

### 8.4.5 中断上半部不能使用无限循环，或者较多次数的循环

## 8.5 分配与释放

### 8.5.1 kmalloc中参数GFP_KERNEL改成GFP_ATOMIC，除非在init过程中调用；

### 8.5.2 gpio_request, request_irq等内核申请资源的API，建议使用devm_gpio_request, devm_request_irq等带devm的对应API，这样在相应module退出时，会自动释放相应资源；

### 8.5.3 超过32K不建议使用kmalloc，可用vmalloc, 如果需要大块的物理连续内存，需要单独考虑,用ION等 建议在设备驱动中使用devm_xxxx开头函数，devm_kmalloc等, 自动释放

### 8.5.4 kfree或其它封装的释放内存函数，指针变量如果为全局变量或入参结构的中指针变量，该指针变量要设成NULL，防止野指针被意外访问，导致程序崩溃

### 8.5.5 驱动中使用的内存尽量在初始化时一次性申请，驱动卸载时的释放，减少在运行过程中的动态申请和释放，如果在驱动初始化时不能明确，就在设备被打开时一次性分配，或由应用调用专门的初始化ioctl接口传递所需的内存参数，如果必须使用动态内存申请和释放，需要加注释解释理由

### 8.5.6 为防止资源泄漏，工作队列有初始化调用，同时必须有对应的cancel/destroy调用，类似的资源使用方式也是如此；

### 8.5.7 资源申请/创建型接口，无论是返回值而还是出参，都需要检查申请到的资源的有效性

### 8.5.8 注意kstrdup, kstrndup, kmemdup, kmemdup_nul等隐性内存分配函数的使用，要检查对应释放

## 8.6 退出处理

### 8.6.1 驱动Close回调函数中需要释放打开实例分配的所有资源，全局共享的资源需要在最后一个实例关闭时释放

### 8.6.2 驱动Close回调函数中释放资源前需要等待实例相关的内核线程, tasklet, worker停止工作，DMA操作结束，固件停止工作，中断屏蔽,  最后关闭时钟。

### 8.6.3 内核模块卸载时释放所有资源，关闭中断。

### 8.6.4 如果硬件IP支持FUSA功能，驱动最后一个应用实例关闭时需关闭硬件Fusa功能。
