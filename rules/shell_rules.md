# Shell脚本规范

**版本** 1.0.0

# 1 文件头

## 1.1 每个文件的开头必须对文件进行描述
对于产品中包含的脚本使用，加上版权和功能描述
对于内部使用的工具类脚本，加上作者和日期，便于使用者和作者探讨
```
#!/bin/bash
#Copyright: Horizon Robotic
#Function: This scripts is used to generate boot.img
```

## 1.2 注意脚本运行的范围，正确的指定解释器和路径
是在地平线芯片上运行还是在主机上运行，在文件头上指定所用的解释路径，非标准shell语法，需要指定特定的解释器。
```
#!/usr/bin/env sh
#!sh
#!/usr/bin/bash
```

## 1.3 通过 set 命令添加脚本调试
如果报错，脚本直接退出，不继续执行，对于管道错误也可以直接退出,未定义变量的。
调试时可以加上set -x，显示执行命令
```
#!/bin/bash

set -eu
set -o pipefail
```

# 2 风格

## 2.1 1. 1. 每行最大长度为80个字，长的字符串可以用特殊方式

```
#DO use 'here document's
cat <<END
I am an exceptionally long
string.
END

# Embedded newlines are ok too
long_string="I am an exceptionally
long string."

```

## 2.2 缩进采用tab, 按4个空格计算


## 2.3 判断与循环
将 ; do , ; then 和 while , for , if 放在同一行，else单独一行, 分号后面空一格

```
if condition ; then
    command
else
    command
fi
for name in $(cat file); do
    echo ${name}
done
```

## 2.4 多路判断
case 如果命令只有一行，命令可以和分号放在一行，其他情况下;单独一行，条件对齐case，命令缩进

```
case "${expression}" in
a)
    command
    ;;
b)
    command
    ;;
*)
    command
    ;;
esac
```

## 2.5 函数
函数名小写和下划线分割单词, 函数名和() 没有空格, 函数定义加function前缀
```
function my_func() {
    echo "hello"
}
```

## 2.6 常量和环境变量名采用大写和下划线分割单词

## 2.7 变量名大写还是小写在一个文件中统一, 引用时加{}
```
echo "${var}"
```

## 2.8 文件名使用小写, 用.sh作后缀
 作为直接给用户用的命令形的脚本，可以没有后缀，作为库被包含的文件，必须有后缀。命令默认加上可执行权限。

# 3.语法建议

## 3.1 局部变量加local标识

## 3.2 判断语句使用 [[，不使用test 和 [

在 [[ 和 ]] 之间不会有路径名称扩展或单词分割发生，所以使用[[ … ]]能够减少错误。而且 [[ … ]]允许正则表达式匹配，而[ … ]不允许

## 3.3 测试字符串 使用-z 或 -n 测试，不使用 [[ "${var}" = "" ]]

## 3.4 所有的错误信息导向stderr

```
if [[ ${result} -ne 0 ]];then
    echo "wrong command" >&2
fi
```

## 3.5 使用 && 或 || 简化判断语句，语义更加清晰

## 3.6 不使用 $? -eq 0 检查命令退出状态，直接使用if cmd; 来判断。

## 3.7 使用$(cmd)来执行命令，不使用

## 3.8 包含变量，命令结果替换，空格或shell特殊字符的字符串始终加引号
```
# "quote command substitutions"
flag="$(some_command and its args "$@" 'quoted separately')"

# "quote variables"
echo "${flag}"
```

## 3.9 脚本中路径变量，最后面不要使用/结尾

## 3.10 需要判断函数返回值

# 4.检查工具

* **shfmt 用于风格检查**

shfmt -d myshell.sh
这个规范里的格式符合shfmt的默认风格，shfmt也可以用开关设定不一样的风格。

snap install shfmt

* **shellcheck 用于语法检查**

shellcheck myshell.sh

apt install shellcheck
# 参考
https://google.github.io/styleguide/shellguide.html


