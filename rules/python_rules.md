# Python代码规范指南
|   **版本**   |   **修改人** |   **评审人**    |  **修订时间**  |  **修订内容**  |
| :------: | :------: | :---------: | :--------: | :--------: |
|   V1.00  |  屈树谦  | - | 2021-10-11 | 评审版发布 | 

目录
=================
- [python代码规范指南](#python代码规范指南)
- [目录](#目录)
- [正文](#正文)
- [0 阅前说明](#0-阅前说明)
- [1 Code Lay-out](#1-Code Lay-out)
  - [1.1 规范](#11-规范)
  - [1.2 检查工具](#12-检查工具)
  - [1.3 Auto-format 工具](#13-Auto-format 工具)
  - [1.4 执行说明](#14-执行说明)
- [2 String Quotes](#2-String Quotes)
  - [2.1 规范](#21-规范)
  - [2.2 检查工具](#22-检查工具)
  - [2.3 Auto-format 工具](#23-Auto-format 工具)
  - [2.4 执行说明](#24-执行说明)
- [3 Whitespace in Expressions and Statements](#3-Whitespace in Expressions and Statements)
  - [3.1 规范](#31-规范)
  - [3.2 检查工具](#32-检查工具)
  - [3.3 Auto-format 工具](#33-Auto-format 工具)
  - [3.4 执行说明](#34-执行说明)
- [4 When to Use Trailing Commas](#4-When to Use Trailing Commas)
  - [4.1 规范](#41-规范)
  - [4.2 检查工具](#42-检查工具)
  - [4.3 Auto-format 工具](#43-Auto-format 工具)
  - [4.4 执行说明](#44-执行说明)
- [5 Comments](#5-Comments)
  - [5.1 规范](#51-规范)
  - [5.2 检查工具](#52-检查工具)
  - [5.3 Auto-format 工具](#53-Auto-format 工具)
  - [5.4 执行说明](#54-执行说明)
- [6 Naming Conventions](#6-Naming Conventions)
  - [6.1 规范](#61-规范)
  - [6.2 检查工具](#62-检查工具)
  - [6.3 Auto-format 工具](#63-Auto-format 工具)
  - [6.4 执行说明](#64-执行说明)
- [7 Programming Recommendations](#7-Programming Recommendations)
  - [7.1 规范](#71-规范)
  - [7.2 检查工具](#72-检查工具)
  - [7.3 Auto-format 工具](#73-Auto-format 工具)
  - [7.4 执行说明](#74-执行说明)
- [8 工具说明](#8-工具说明)
  - [8.1 检查工具](#82-检查工具)
  - [8.2 Auto-format 工具](#83-Auto-format 工具)


正文
=================

# 0 阅前说明
- python版本为3.6.x
- 一级条目参考 [pep8](https://www.python.org/dev/peps/pep-0008)。
- 每个条目上都从规范，检查工具，auto-format 工具和执行说明等四个方面进行阐述。
- 每个规范都由多个子条目组成，一般第一条为基础规范，后面为对基础规范的补充说明。

# 1 Code Lay-out
## 1.1 规范
### 1.1.1 遵从PEP8的规定，详情参见: [pep8 code-lay-out](https://www.python.org/dev/peps/pep-0008/#code-lay-out)
### 1.1.2 imports路径：除了当前目录可以采用相对路径（.），其他均使用绝对路径

【说明】

绝对路径相对于相对路径的可读性更好。事实上一般规范都会建议使用绝对路径。

### 1.2 检查工具

flake8

### 1.3 Auto-format 工具

- black
- isort

### 1.4 执行说明

与自动化工具冲突时可适当ignore冲突的错误码，如W503（Line break occurred before a binary operator）。

# 2 String Quotes
## 2.1 规范
### 2.1.1 优先使用双引号（" 和 """）
### 2.2 检查工具
### 2.3 Auto-format 工具

black

### 2.4 执行说明

自动化工具（black）会自动转换单引号为双引号，如果觉得实在不妥，例如单引号/双引号有不同含义，可以skip掉（--skip-string-normalization）。

# 3 Whitespace in Expressions and Statements
## 3.1 规范
### 3.1.1 遵从PEP8的规定，详情参见：[pep8 whitespace-in-expressions-and-statements](https://www.python.org/dev/peps/pep-0008/#whitespace-in-expressions-and-statements)
### 3.2 检查工具

flake8

### 3.3 Auto-format 工具

black

### 3.4 执行说明

与自动化工具冲突时可适当ignore冲突的错误码，如E203（whitespace before ':'）。

# 4 When to Use Trailing Commas
## 4.1 规范
### 4.1.1 遵从black的规定，详情参见：[black training-commas](https://black.readthedocs.io/en/stable/the_black_code_style/current_style.html#trailing-commas)
### 4.2 检查工具
### 4.3 Auto-format 工具

black

### 4.4 执行说明

# 5 Comments
## 5.1 规范
### 5.1.1 遵从PEP8的规定，详情参见：[pep8 comments](https://www.python.org/dev/peps/pep-0008/#comments)
### 5.1.2 docstring（格式补充）

- sections
 - 使用缩进而非换行来区分不同的section。
 - section heads请使用Args:、Returns: (or Yields: for generators)、Raises:、Attributes:(for class public attributes)和Examples:。
- type annotation
 - 如果args中已经添加了type annotation，则无需在docstring中说明；否则需要在描述args进行说明。
- 示例参见：[google style docstrings](https://sphinxcontrib-napoleon.readthedocs.io/en/latest/example_google.html)

### 5.1.3 docstring（可读性说明）

- 希望能够达到只看docstring就知道怎样使用的效果，不要草草了事;
- 伴随着代码变更及时更新，如某个逻辑变更或者新增参数;
- 必要的时候添加example进行说明。

### 5.2 检查工具

- flake8
- pydocstyle

### 5.3 Auto-format 工具

black

### 5.4 执行说明

文档详细程度很多时候与项目强相关，请根据具体情况执行。

# 6 Naming Conventions
## 6.1 规范
### 6.1.1 遵从PEP8的规定，详情参见：[pep8 naming-convertions](https://www.python.org/dev/peps/pep-0008/#naming-conventions)
### 6.1.2 尽量不要在接口上使用非业界通用或者共识的缩写。

### 6.2 检查工具

pep8-naming

### 6.3 Auto-format 工具
### 6.4 执行说明

命名规范很多时候与常用语，语义或者上下文相关，对于检查工具的使用只是推荐并不强制。

# 7 Programming Recommendations
## 7.1 规范
### 7.1.1 遵从PEP8的规定，详情参见：[pep8 programming-recommendations](https://www.python.org/dev/peps/pep-0008/#programming-recommendations)
### 7.1.2 针对容易出现的bug/design问题补充flake8-bugbear规定，参见[flake8-bugbear](https://github.com/PyCQA/flake8-bugbear)
### 7.1.3 list/set/dict的使用遵从flake-comprehensions的规定，参见[flake8-comprehensions](https://github.com/adamchainz/flake8-comprehensions)
### 7.1.4 Function Annotations：Public的method/function必须添加type hints
### 7.1.4 Threading：不要依赖build-in types的原子性

【说明】

一般情况下，python的build-in类型操作原子性与实现有关，不能保证一定是原子的。详情说明参见[theading](https://google.github.io/styleguide/pyguide.html#218-threading)

### 7.1.5 \*args, \*\*kwargs：谨慎使用

【说明】

\*args, \*\*kwargs功能比较强大，随便使用可能导致接口的可维护性差，用户使用时可读性也不好。建议使用具体含义的args。


### 7.2 检查工具

- flake8
- flake8-bugbear
- flake-comprehensions
- mypy

### 7.3 Auto-format 工具
### 7.4 执行说明

优秀的Python编写实践是偏软性的，尤其是检查工具不能涉及的范围，很多规范也没有相应的检查工具能够检查出来。只能在代码review阶段进行检查。


# 8 工具说明
## 8.1 检查工具

主要采用flake8系列，包括flake8, flake8-bugbear和flake8-comprehensions，以及mypy，均为社区常用的检查工具，成熟度好。

## 8.2 Auto-format 工具

采用black和isort，black社区活跃度高，运行速度快且限制更加严格，一致性好。迁移的示例说明见[replace yapf with black](https://github.com/PyTorchLightning/pytorch-lightning/pull/7783)。

