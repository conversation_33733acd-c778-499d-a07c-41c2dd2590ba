
# 地平线C语言规范-基本规则

|版本|修改人|评审人|修订时间|修订内容|
|-|-|-|-|-|
|1.0.0|凌晓峰|软件工程规范TMG-C组|2021/9/23|1.0终版|
|2.0.0|凌晓峰|软件工程规范TMG-C组|2022/12/21|2.0终版|
|2.1.0|凌晓峰|软件工程规范TMG-C组|2023/11/13|2.1修订版|

# 1 风格

## 1.1 行长

### 1.1.1 代码行长不超过120，推荐不超过80, 包括空格，tab按8个空格计算

## 1.2 缩进对齐

### 1.2.1 新代码统一使用tab作缩进，除了历史遗留代码或第三方代码

### 1.2.2 结构中成员定义类型和变量名之间用tab区隔对齐

```
/*
 * The poor guys doing the actual heavy lifting.  All on-duty workers are
 * either serving the manager role, on idle list or on busy hash.  For
 * details on the locking annotation (L, I, X...), refer to workqueue.c.
 *
 * Only to be used in workqueue and async.
 */
struct worker {
        /* on idle list while idle, on busy hash table while busy */
        union {
                struct list_head        entry;  /* L: while idle */
                struct hlist_node       hentry; /* L: while busy */
        };

        struct work_struct      *current_work;  /* L: work being processed */
        work_func_t             current_func;   /* L: current_work's fn */
        struct pool_workqueue   *current_pwq;   /* L: current_work's pwq */
        struct list_head        scheduled;      /* L: scheduled works */

        /* 64 bytes boundary on 64bit, 32 on 32bit */

        struct task_struct      *task;          /* I: worker task */
        struct worker_pool      *pool;          /* A: the associated pool */
                                                /* L: for rescuers */
        struct list_head        node;           /* A: anchored at pool->workers */
                                                /* A: runs through worker->node */
```
注：clang-format不支持结构中这种对齐，如果要用clang-format处理，需用/* clang-format off \*/ /\* clang-format on */包起来

### 1.2.3 if, while, for, do, switch 左括号{在同一行， 右括号}和首字母对齐。

### 1.2.4 switch, case, default, 右括号} 按首字母对齐

```
        switch (key->both.offset & (FUT_OFF_INODE|FUT_OFF_MMSHARED)) {
        case FUT_OFF_INODE:
                ihold(key->shared.inode); /* implies smp_mb(); (B) */
                break;
        case FUT_OFF_MMSHARED:
                futex_get_mm(key); /* implies smp_mb(); (B) */
                break;
        default:
                /*
                 * Private futexes do not hold reference on an inode or
                 * mm, therefore the only purpose of calling get_futex_key_refs
                 * is because we need the barrier for the lockless waiter check.
                 */
                smp_mb(); /* explicit smp_mb(); (B) */
        }
```

### 1.2.5 预处理宏指令全部从行头开始，无缩进

## 1.3 分行对齐

对于行长度太长需要换行的对齐

### 1.3.1 换行先用tab对齐上一行前面的缩进，再用空格对齐上一行相关位置

### 1.3.2 函数调用按左括号右一格对齐

```
 kthread_create_on_node(worker_thread, worker, pool->node,
                        "kworker/%s", id_buf);
```

### 1.3.3 赋值换行按缩进一tab对齐

```
worker->task = 
           kthread_create_on_node(worker_thread, worker, pool->node,
                                  "kworker/%s", id_buf); 
```

### 1.3.4 条件语句换行按左括号右一对齐

```
if (worker->current_work == work &&
    worker->current_func == work->func)
    return worker;
```

### 1.3.5 函数定义左括号右一对齐

```
struct workqueue_struct *alloc_workqueue(const char *fmt,
                                         unsigned int flags,
                                         int max_active, ...)
{
```

###   引号换行按引号右一格对齐

## 1.4 空格

### 1.4.1 运算符前后加空格，关键字和括号间加空格

### 1.4.2 小括号左（后面，小括号右)前面不加空格

```
if (id == 0) {
  ...
}
switch (mode) {
  ...
}
for (i = 0; i < 10; i++) {
 ...
}
```

### 1.4.3 函数之间加一个空行

### 1.4.4 头文件引用和全局变量之间加换行

## 1.5 括号

### 1.5.1 if/else如果都是单语句，可以都不加{}， 如果只有一个单行语句，都要加{}

### 1.5.2 if/else代码段，如果if里面已经return了，后面就不需要写else了；

### 1.5.3 左大括号{跟在行尾, 函数定义的除外, 关键字， 见样例

```
        for (i = 0; i < VIO_MP_MAX_FRAMES; i++) {
                frame_start = (struct mp_vio_frame *)frame_array_addr[i];
                if (!frame_start)
                        continue;
                first_index = frame_start->first_indx;

                vfree(frame_start);
        }
```

## 2 类型

## 2.1 定义

### 2.1.1 数据类型不要使用原始定义，使用uint32_t, uint16_t等明确长度的定义
### 2.1.2 标识符的命名要清晰、明了，有明确含义，同时使用完整的单词或大家基本可以理解的缩写，避免使人产生误解。

## 2.2 宏

### 2.2.1 magic number必须用宏标识, 除了0和1
### 2.2.2 用宏定义表达式时，要使用完备的括号, 以免扩展后改变了流程
### 2.2.3 宏定义中尽量不使用return、goto、continue、break等改变程序流程的语句。

## 2.3 头文件

### 2.3.1 除inline函数外，头文件中适合放置接口的声明，不适合放置实现。

### 2.3.2 .c/.h文件禁止包含用不到的头文件，头文件通过#ifdef防止重复包含

### 2.3.3 只能通过包含头文件的方式使用其他.c提供的接口，禁止在.c中通过extern的方式使用外部函数接口、变量，禁止未声明函数直接调用函数

### 2.3.4 禁止在头文件中定义变量, 只能声明全局变量

### 2.3.5 头文件应向稳定的方向包含,应当让不稳定的模块依赖稳定的模块
   自研代码中包含顺序为C标准库、C++标准库、其它库的头文件、本工程的头文件，本目录头文件

### 2.3.6 头文件中链接库的头文件用\<\>，其它用\"\"包含

## 2.4 常量

### 2.4.1  对于数值、字符串、宏、枚举等常量的定义，建议采用全大写字母，单词之间加下划线的方式命名。

## 2.5 变量

### 2.5.1 全局变量尽量不要用，尤其可重入函数应避免使用共享变量，如果定义了，要有较详细的注释，包括对其功能、取值范围以及存取时注意事项等的说明。

### 2.5.2 全局变量命名应增加特定前缀，如"g\_"
### 2.5.3 禁止宽类型向窄类型的隐形转换，如int转换成short, 需要加显式的强制转换，并加注释说明丢失高位不影响

## 3 函数

## 3.1 命名

### 3.1.1 如果函数是特定作用于一个模块、域、对象、编程形式的，可以以其作为前缀命名

### 3.1.2 同一类的函数名风格保持一致，比如都是模块+动词+形容词(可选)+名词

### 3.1.3 只在本文件内调用的函数应该加static修饰

### 3.1.4 除了只在本文件内调用的static函数，函数名必须标识当前模块名信息，包括通过回调注册到文件外部的static函数

  目的除了避免链接的冲突，对于回调函数也是便于调试，在回调函数中有可能有重名的回调函数。

### 3.1.5 用正确的反义词组命名具有互斥意义的变量或相反动作的函数

### 3.1.6 尽量避免名字中出现无意义数字编号，除非逻辑上的确需要编号。

## 3.2 局部变量定义

### 3.2.1 函数内部的局部变量尽量放在函数前面声明，如果要用，只能放到中间带有{}的block内前面声明

### 3.2.2 指针变量、表示资源描述符的变量，BOOL变量声明必须赋予初值
	例如fd推荐初始化为-1、避免局部变量未初始化，导致访问随机错误值，其他局部变量如果能够确认后续被赋值后再使用，可以不用在声明时初始化 

### 3.2.3 局部变量不能与全局变量同名。

### 3.2.4 不使用大的局部变量数组/buffer,以免栈溢出，建议用户态进程函数局部变量不超过8192字节

### 3.2.5 禁止使用单字节命名变量，但允许定义i、j、k、m、n、v作为局部循环变量，p、q作为局部指针变量。

## 3.3 入参

### 3.3.1 对外函数必须作入参校验；（以模块为单位）

### 3.3.2 不通过参数传结构体
	如果结构大小不超过8字节，在函数内只读，可以直接传递，需加const并需加注释

### 3.3.3 参数个数不超过8个， 传入参数在内部不被修改，用const声明

### 3.3.4 第一次从外部传入的结构中获取指针时，需要检查，再传给本模块的其它函数，函数里面不用检查

## 3.4 函数体

### 3.4.1 函数有效行数原则上不超过70行，（如果超出70行，需要检查嵌套深度和代码最大路径）

### 3.4.2 函数的圈复杂度建议不超过15

### 3.4.3 简短函数建议用inline声明

### 3.4.4 尽量避免使用while(1)代码段，如果一定要加，需要注释和解释循环退出条件；while里面的函数需要有阻塞函数

### 3.4.5 不要使用递归函数；如果一定要使用，需要注释和解释递归退出条件；

## 3.5 异常处理

### 3.5.1 异常分支必须处理，现阶段无法明确的异常处理，必须加panic或ASSERT；

### 3.5.2 除了错误处理，不允许使用goto语句；

### 3.5.3 函数需要仔细检查是否需要有返回值，对肯定不会有异常执行路径的函数可以没有返回值，如果无返回值的函数，需要注释

### 3.5.4 如果函数返回出错，应当相当于这个函数没有跑，所有资源释放或回退；

### 3.5.5 异常返回时要有ASSERT或异常打印告知

### 3.5.6 返回正常时，正常返回统一出口，例外情况是在函数开头判断已经打开，已经初始化等条件，可返回正常。

## 3.6 返回值

### 3.6.1 如果函数的名字是一个动作或者强制性的命令，那么这个函数应该返回0表示成功，负数表示错误代码整数。如果是一个判断，那么函数应该返回一个\"成功\" 布尔值，1-成功，0-失败

### 3.6.2 函数返回值，如果是status类型，尽量用枚举或宏定义替代，0和1可以例外

### 3.6.3 调用函数时，需要对函数返回值要做校验后，再继续运行,对于不关心返回值的场景，需要加void标识
	防止对返回值的错误处理，没有正确的识别返回值的语义，错误的判断返回值意义，进行了错误的处理，可能导致业务流错误蔓延，或者进入错误的业务流分支；防止对业务有影响的返回值没有判断，造成访问空指针，或者继续运行失败的业务流，导致错误蔓延，或者资源未回退，导致泄露。

### 3.6.4 函数返回值为指针类型，需要作有效性判断，判断方法需要确认，不一定能用NULL判断，可能需要用IS_ERR方式；

###   不返回结构体，不返回局部变量结构指针

## 4 语言

## 4.1 分配释放

### 4.1.1 内存申请前，必须对申请内存大小进行合法性校验

### 4.1.2 内存分配后必须判断是否成功

### 4.1.3 禁止使用realloc()函数

### 4.1.4 禁止使用alloca()函数申请栈上内存

### 4.1.5 如果一个函数的返回内容是内部申请的资源，必须要有对应另外的函数释放该资源，需要检查是否成对调用

### 4.1.6 一个结构或其它资源的释放时需要判断引用计数, 没有要加注释说明。

### 4.1.7  free后，指针变量如果为全局变量或入参结构中的指针变量，相应释放的内存变量要设成NULL，防止野指针被意外访问，导致程序崩溃

## 4.2 原子性检查

### 4.2.1 链表操作要加锁保护，如果不加，需要注释说明没有竞争条件

### 4.2.2 可并发函数中对非局变量的非原子访问（++, --, +=, -=等），应通过互斥手段（锁或原子增减）对其加以保护,不加锁需要注释说明没有竞争条件

### 4.2.3 对于全局的标志变量的先判断后改变，需要加锁，不加锁需要注释说明没有竞争条件

### 4.2.4 修改全局的标志变量的某些位时，需要加锁保证原子性,不加锁要加注释说明没有竞争条件。

### 4.2.5 如果某个文件中的函数都是没有并发场景的，可以在文件开头加注释，后面就不用每个地方加了。

### 4.2.6 结构初始化完成之间，不能加入链表,使其它线程能访问到。


## 4.3 注释

### 4.3.1 作为库函数向外提供的接口需要在头文件申明处提供标准函数说明（功能，参数，返回值），可以通过doxgen等生成API手册,
    见下面模板

```
/**
 * @brief 简单描述
 * 简单描述....
 *
 * 详细描述（如果有的话）简单描述和详细描述间一定要有一行空行
 * @param[in] fileName 文件名
 * @param[in] fileMode 文件模式，可以由以下几个模块组合而成：
 * -r读取
 * -w 可
 * -a 添加
 * -t 文本模式(不能与b联用)
 * -b 二进制模式(不能与t联用)
 * @return 返回文件编号
 */

int
OpenFile(const char* fileName, const char* fileMode);
```

### 4.3.2 对于非static函数，需要提供注释介绍函数功能和重要的参数与返回值，
    对于static函数,
    除了很简单的函数，尽量提供注释介绍功能，参数和返回值按需注释。
### 4.3.3 结构定义各成员，宏定义，全局变量需要注释说明用处。如果行末写不下，写在行前,
    用C标准注释。

```
struct worker {
        /* on idle list while idle, on busy hash table while busy */
        union {
                struct list_head        entry;  /* L: while idle */
                struct hlist_node       hentry; /* L: while busy */
        };
```

### 4.3.4 函数体内复杂的逻辑加注释说明做了什么，但不是怎么做，如果一个函数体内要加注释的地方太多，考虑拆分函数，在函数头加注释。
   注释加在代码块前面。

### 4.3.5 对于和规范或常规做法不一致的地方，加注释说明

### 4.3.6 对于已知有欠缺,限制或待改进的代码，加注释说明

### 4.3.7 在.c的文件头加上版权说明，对外接口的.h头加上版权说明：

Copyright \[year file created\] - \[last year file modified\], Horizon
Robotics

## 4.4 第三方来源代码

### 4.4.1 包括开源代码和供应商代码，代码风格上按第三方原有风格，不冲突的项，新加代码按地平线要求。

