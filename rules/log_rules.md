# **地平线日志规范**

# 公共规范
## 关于级别

* fatal 致命错误，导致系统崩溃或应用崩溃
* error 发生错误的消息，部分功能将会异常，最终产品发布也会打开
* warning 非预期的路径，功能可能受影响，也可能不受影响，最终产品发布也会打开
* info 重要节点的关键提示信息，也不能过多，系统测试，集成测试时打开，最终产品视具体产品特性而定，目标是尽量能打开，所以在添加时也要慎重。
* debug(trace/verbose) 调试信息，个人调试，单元测试时打开,最终产品可以不生成代码，也可以动态能打开，根据性能的要求而定。
* 对于下面各领域log的API中，info和debug可能并不区分，而是warning以下再分很多级别，这时info就是指warning以下最高一级，表示最重要的正常信息。

## 默认级别
有不同的默认级别设置:

* 是否编译生成打印的语句
* 是否输出到日志的内存缓冲区中
* 是否输出到文件中
* 是否在标准输出设备上输出

## 基本原则

1. 添加相关打印信息时，一次性把需要的重要变量都展示出来，避免后期重新再加，也作为review的一个检查点。
2. 对于特定子系统，如一个库，一个应用，通过统一的接口，统一前缀信息， 比如模块名，文件名，行号，函数名，cpu号，进程号，时戳。也要注意前缀信息不要过长。
3. 对于特定子系统，尽量使用能够动态根据模块，文件，函数行号开关的日志接口。对于可能输出到串口的日志调试，要考虑对整个系统性能和时序的影响。
4. 评估性能的时候，需要考虑日志的影响，不使用串口输出。进入集成测试阶段，一般不再利用串口输出日志，以免受时序影响，和最终产品运行时序不一致。
5. 在系统稳定性相关测试需要在串口输出特定日志的，要设定串口输出级别，一般只输出error, warn级别。
6. 如果某个子系统支持动态配置，模块开发者需要思考准备好特定问题需要的log配置项，问题解决时可以快速提供。
7. 循环内不输出日志，特殊情况需评审，加上注释说明。
8. 临时日志不合入主线。
9. 日志输出信息需要考虑是否有敏感内容，需要防破解的代码是否增加了破解概率，需要防攻击的代码是否泄露了变量地址等。

## 日志存放位置
#### 在嵌入式端的默认的log存放位置：
#### 内核日志存放位置
/userdata/log/kernel/
每个文件的转存大小ROTATESIZE=2048K，也就是单个kernel log文件最大2M，超过2M后将转存为带时间戳的文件名，如1970-01-01-08-00-01.kmsg。并会转存到/log/kernel/xxxx文件夹下，xxxx文件夹的名字为时间戳。如/log/kernel/1970-01-01-08-01-58文件夹
#### 用户态日志存放位置
/userdata/log/usr/
每个文件的转存大小ROTATESIZE=2048K，也就是单个用户层 log文件最大2M，超过2M后将转存为带时间戳的文件名，如1970-01-01-08-00-01.umsg。并会转存到/log/usr/xxxx文件夹下，xxxx文件夹的名字为时间戳。如/log/usr/1970-01-01-08-01-58文件夹
#### 应用崩溃dump文件存放位置
/userdata/log/coredump， 文件格式为core-执行程序名-pid-运行时的时间戳

# 特定规范
## 1.linux内核
### 接口使用

* 在驱动中推荐使用如下接口dev_err, dev_warn, dev_info, dev_dbg
* 没有设备结构的场合使用如下接口pr_error, pr_warn, pr_info, pr_debug
* 对于重要信息必须打印，可直接使用printk，无法用loglevel关闭
* 对于一旦出错可能重复非常多的打印，用如下接口printk_once 只打印一次或printk_ratelimited 限制每秒打印次数

默认每5秒最多打印10次
可以通过
/proc/sys/kernel/printk_ratelimit
/proc/sys/kernel/printk_ratelimit_burst
来设置
调用这两个需要自已在前在放上级别和pr_fmt：
printk_ratelimited(KERN_ERR pr_fmt("end_request: %s error, dev %s, ") , xxxx, xxxx)

### 关于格式设置
在文件开头用 pr_fmt用来定义输出格式， 可加统一前缀：
define pr_fmt(fmt) "ipu: " fmt
调试可加入函数名：
define pr_fmt(fmt) "hobot-xxx: %s: " fmt, __func__

### 动态开关
pr_debug可以由宏控制是否编译进去
kernel CONFIG_DYNAMIC_DEBUG, 可以使能动态开关
kernel/Documentation/admin-guide/dynamic-debug-howto.rst
e.g.
echo "file bif_base.c +p" >/sys/kernel/debug/dynamic_debug/control
echo "func bifbase_init +p" >/sys/kernel/debug/dynamic_debug/control
启动时打开，在uboot中设置kernel 命令行
dyndbg="file drivers/mmc/host/* +p"
目前默认打开 CONFIG_DYNAMIC_DEBUG， 某个文件需要及致性能优化性时，可以关掉，不编译进去。

### 死机日志
/userdata/log/pstore

## 2.底层库
### 接口

代码路径位于：hbre/liblog/include/logging.h
/* debug level */
pr_debug(fmt, ...);
/* info level */
pr_info(fmt, ...);
/* warn level */
pr_warn(fmt, ...);
/* error level */
pr_error(fmt. ...);

### 输出格式设置

各个模块在输出log时，可能需要打印自己的模块名字，此时可以通过定义SUBSYS_NAME宏，来传入名字即可。比如，下文中示例部分的log输出，[camera]中，camera即模块的名字定义，可以在Makefile中通过-DSUBSYS_NAME=camera来传入。

### 输出方式选择

* Console方式

这种方式是最常用的，通过串口进行输出，使用这种方式也是最简单的，直接在代码中包含头文件hbre/liblog/include/logging.h就可以了。

* ALOG方式

使用ALOG方式，利用安卓的日志系统，Log不会输出到终端来，使用这种方式，有三个步骤：
1）包含头文件hbre/liblog/include/logging.h；
2）打开ALOG_SUPPORT宏；
3）链接libalog.so库；

后两步可以在Makefile中指定，给一个示例：

```
LOG_SUPPORT = -DALOG_SUPPORT
 LOG_LIB = -L liblog_path -llog
/* liblog_path为libalog.so库文件的存放路径 */

CFLAGS += $(LOG_SUPPORT)
LDFLAGS += $(LOG_LIB)
```

### Log Level选择
/* output log by console */
 #define CONSOLE_DEBUG_LEVEL 14
#define CONSOLE_INFO_LEVEL 13
#define CONSOLE_WARNING_LEVEL 12
#define CONSOLE_ERROR_LEVEL 11

#define ALOG_DEBUG_LEVEL 4
#define ALOG_INFO_LEVEL 3
#define ALOG_WARNING_LEVEL 2
#define ALOG_ERROR_LEVEL 1

修改环境变量，来设置Log Level值，其中各个值如上图所示，
#loglevel_value为设定值
 export
LOGLEVEL=loglevel_value

当Log Level设定不在1 ~ 4, 11 ~ 14之间时，会默认选择成11的值。
Log Level 设为1-4 时，并且HAL库ALOG_SUPPORT打开时，会通过libalog输出，可通过logcat工具获取。
Log Level 设为11-14 时，通过console 用默认printf输出。

### Logcat使用
当ALOG_SUPPORT打开时，接口转为安卓的log系统，可使用logcat工具来获取log

[adb] logcat [<option>] ... [<filter-spec>] ...
[options]命令包括如下选项:
-s 设置过滤器，例如指定 '*:s'
-f <filename> 输出到文件，默认情况是标准输出。
-r [<kbytes>] Rotate log every kbytes. (16 if unspecified). Requires -f
-n <count> Sets max number of rotated logs to <count>, default 4
-v <format> 设置log的打印格式, <format> 是下面的一种:
brief process tag thread raw time threadtime long
-c 清除所有log并退出
-d 得到所有log并退出 (不阻塞)
-g 得到环形缓冲区的大小并退出
-b <buffer> 请求不同的环形缓冲区 ('main', 'system', 'radio', 'events',默认为"-b main -b system")
-B 输出log到二进制中。
过滤器的格式是一个这样的串：
<tag>[:priority]
其中 <tag> 表示log的component， tag (或者使用 * 表示所有) ， priority 从低到高如下所示:
V Verbose
D Debug
I Info
W Warn
E Error
F Fatal
S Silent

## 3.安卓应用
### 接口使用
android.util.Log
Log.v(),
Log.d(),
Log.i(),
Log.w(),
Log.e()

例：
```
private static final String TAG = "MyActivity";
Log.v(TAG, "index=" + i);
```
 


## 4.嵌入式应用

* 发布版本，禁止开启Debug及以下级别的日志；【强制】
* FATAL 级别的日志应该保留足够的上下文函数调用关系信息；【推荐】
* 统一使用平台提供的公司级的 日志服务/库 提供的接口 完成日志输出；【推荐】
* 考虑日志流量，例如，当需要将日志存储在spi-flash这种慢速硬件上时，或者在慢速网络设备上时，需要考虑限制每秒日志输出的大小。【推荐】
* 使用统一的日志输出路径，建议在/userdata/log/app下【推荐】
* 日志文件名的格式相对统一
后缀名.log 【强制】
{Timstamp}_{进程名}_{进程ID}_{Number}.log 【推荐】
Timestamp 的格式：YYYYMMDD-HHMMSS

Number：是指的次数，一旦时间同步服务没有开启时，只依托Timestamp不便于定位

* 日志输出的格式化；【强制】

%L%m%d %H:%M:%S.%e %s:%#]<%n>(%t) %v

| 符号 | 含义 |
| --- | --- |
|  |  |
|%L|日志等级：F：Fatal / E: Error / W:Warning / I：Info / D: Debug / T: Trace|
|%t|线程ID|
|%m|月份: 08|
|%d|日期：01-31|
|%H|小时：00-23|
|%M|分钟：00-59|
|%S|秒：00-59|
|%e|毫秒：000-999|
|%s|文件名|
|%#|行号|
|%n|自定义的tag / module|
|%v|日志正文|

### 接口使用
glog
https://github.com/google/glog#readme

## 5.PC端应用
同嵌入式应用

## 6.Web前端
主要分埋点和监控
### 埋点：
{
uuid: 'xxxxx',
url: 'https://xxx',
eventType：'pv',// 分类：pv/页面停留/曝光/交互事件/逻辑事件
eventValue: 'xxxx',
eventTime: '2018-09-10 10:20:30', // 上传用户本地时间字符串
extra: 'xxx' //额外信息，可以是对象进行json字符串化的结果
}

## 7.云端/后端

### 日志级别：
#### info日志
入日志系统，用来追查问题（fn表示当次请求的返回结果，非负数表示成功，fn>0表示返回多条结果，fn=0表示正常处理，fn<0 表示处理失败）。

格式的范例如下：
{ "timestamp": "2018-09-10 10:20:30", "threadid":1220,"Modulename":"mainmodule1" ，"trackid":"0xa12318990101", “fn”: 10, "TM":510,"Parse":10, "Module1":150,"Module2":180,"Module3":160,"Rtime":10, "tm":140,"parset":10, "module1":"30", "module2":40,"module3":50, "rtime":10}

#### Warning日志：
warning日志，本地记录，记录异常问题、流程无法继续，问题跟进的重要依据， 会添加报警短信、邮件、微信通知。格式的范例如下：
{time: "2018-09-10 10:20:30", threadid:1220,"picid":0xa12318990101, "message"：“this is error, you should notice that”}

### 日志信息必选项
日志包含唯一traceId用于定位多个服务调用

### 输出
日志输出到标准输出，结合日志采集agent 采集到日志服务

### 推荐logging选型
golang：logrus

# 待后续解决问题

* 关于应用和库输出log的统一
* HAL层log动态配置开关问题
* 是否使用spdlog作为公司的标准log库 

