# 系统软件日志规范补充

## 规范更新日志

| 更新日期 | 更新人 | 更新说明 |
| --- | --- | --- |
| 2023.2.2 | 王思远，王云乾，凌晓峰，余明 | 初版 |

## 目的

统一日志和注释规范，并将注释信息能够解析到表格，表达出log代表的状态信息，方便开发同学分析问题，同时测试人员也能根据日志和提取的注释信息来更直观的确认测试结果

## 日志规范

此日志规范是在地平线日志规范的基础上做了些补充，主要介绍日志的打印相关注释的格式规范

### 日志格式

主要针对warn和error级别的日志，格式如下：
subject：predicate + object, extra

举例：vio_sif cpu_id thread_id file line：transfer failed，channel=1

#### 2.1.1 细则

1. **subject（主语）**：子系统、模块名字，关键路径标志（cpu id、线程id、文件名、行号）
2. **predicate + object（谓语+宾语）**：做了什么，产生了什么结果，例如可以定义为rx，tx，init，函数执行流程出现的异常状态...
3. **extra**：额外的定制log，各个子模块可以独立去封装定义，例如下面
    1. **count**：计数：针对不同需求的计数，如 txcnt：1 rxcnt：2
    2. **channel**：通道： 当前所处的数据通道
4. **格式中符号代表意义**
    1. ： 主语描述完毕后使用：与后面的输出语句进行分割
    2. ，作为不同属性打印状态的分割
    3. 当遇到表征同一属性需要增加多个log的时候，可用空格隔开记录

#### 2.1.2 表达用语标准

1. 口语化表达，玩具化表达，不应出现在release版本中
2. error, fail等单词，不应出现在正常日志语句中
3. 不应使用感叹号，会导致客户恐慌
4. 不应中英文标点混用，可能会导致显示乱码

### 2.2 注释格式

1. 针对error，warn级别的错误情况，在要打印日志的前一行进行注释，格式如下
    1. `/*! <explain> 异常现象：可能的原因补充 */`
        1. 对要打印的日志进行解释说明，表达打印的日志代表什么信息，出现了什么现象，如果明确产生原因，加入出现这个现象的原因的描述
2. **具体形式**
    1. 最终各个模块要调用统一定义好的接口（带有关键信息）去打印log
        1. **内核层**
            1. 基于dev_xxx，pr_xxx的命名，对其fmt进行整改，增加关键路径的相关信息
            2. 按照debug和release版本来去区分不同的关键路径信息封装
                1. **debug**：cpu id、线程id、文件名、行数名、行号都存在
                2. **release**：cpu id、线程id
                3. 对应release和debug版本的区分通config配置变量传入 
                    1. **J5**：区分debug和release是在build中根据传入的编译参数判断对.config进行sed -i xxx追加
                        1. J6的debug和release是否还是使用这种方式

```C
#if defined(CONFIG_DEBUG_LOG_FMT)
#define pr_critical_path_fmt(fmt) KBUILD_MODNAME ": [C:%d P:%d %s %s %d] " fmt, get_cpu(), current->pid, __FILE__, __FUNCTION__, __LINE__
#define dev_critical_path_fmt(fmt) "[C:%d P:%d %s %s %d] " fmt, get_cpu(), current->pid, __FILE__, __FUNCTION__, __LINE__
#endif
#if defined(CONFIG_RELEASE_LOG_FMT)
#define pr_critical_path_fmt(fmt) KBUILD_MODNAME ": [C:%d P:%d] " fmt, get_cpu(), current->pid
#define dev_critical_path_fmt(fmt) "[C:%d P:%d] " fmt, get_cpu(), current->pid
#endif
```

        2. **应用层**
            1. 按照原来的方式正常使用ALOG的相关接口，针对关键信息我们在ALOG内部函数中做了封装，对于使用log接口的开发者是无感的，还是延续以前的使用方式
                1. 相应的关键信息我们按照debug和release版本来去区分输出不同的信息
                    1. **debug**：cpu id、线程id、文件名、行数名、行号都存在
                    2. **release**：cpuid、线程id
    2. **实际效果**
        1. **内核层**
            1. **注意**：当前大家在写内核驱动代码的时候
                1. 优先关注黄色的部分即可，给每条warn和err级别的log增加注释
                2. 关于fmt的宏定义暂定如下，还需要跟build的开发同事确认，后续确定好形式再和大家同步

```C
#if defined(CONFIG_DEBUG_LOG_FMT)
#define pr_critical_path_fmt(fmt) KBUILD_MODNAME ": [C:%d P:%d %s %s %d] " fmt, get_cpu(), current->pid, __FILE__, __FUNCTION__, __LINE__
#define dev_critical_path_fmt(fmt) "[C:%d P:%d %s %s %d] " fmt, get_cpu(), current->pid, __FILE__, __FUNCTION__, __LINE__
#endif
#if defined(CONFIG_RELEASE_LOG_FMT)
#define pr_critical_path_fmt(fmt) KBUILD_MODNAME ": [C:%d P:%d] " fmt, get_cpu(), current->pid
#define dev_critical_path_fmt(fmt) "[C:%d P:%d] " fmt, get_cpu(), current->pid
#endif

int a_func（int a）{
    ......
    /*! <explain> ABRT_7B_ADDR_NOACK：slave address not acknowledged (7bit mode)*/
    dev_err(dev, "transfer failed, abort_source=%d\n", ABRT_7B_ADDR_NOACK);
    ......
}
```

        2. **应用层**
        3. 
            保持原来的log接口使用方式即可
            **注意**：但要给每条warn、err级别的日志加上注释如上黄色标注的一样

### 2.3 错误日志添加规则

1. 如果在一个函数中有多个相同的返回值的异常路径，每个地方都需要加log输出区别
2. 如果在一个循环中异常返回，需要加log输出循环相关信息
3. 驱动中返回到用户态的最上层函数，由中断调用的第一层函数，内核线程调用的第一层函数，这三类函数中的错误路径需要加log，用户态库的返回应用的最上层函数错误路径需要加log
4. 当上面的c条件不足于判断函数最底层异常返回路径的，需要在下层函数各个地方加log
5. 引起走到错误路径的变量需要打印出来，其它有助于判断的错误路径的变量也要打印出来
6. IP寄存器读写失败的情况，在fusa做检查时候打印出来详细的寄存器地址信息，方便低概率问题排查

#### 范例

1. **在一个函数中有多个相同的返回值的异常路径，每个地方都需要加log输出区别**，如下面的-EINVAL、-ENOMEM相关的异常返回路径

2. 如果在一个循环中异常返回，需要加log输出循环相关信息
3. 驱动中返回到用户态的最上层函数，由中断处理第一层函数，内核线程调用的第一层函数，这三类函数中的错误路径需要加log，用户态库的返回应用的最上层函数错误路径需要加log.
4. 当上面的c条件不足于判断函数最底层异常返回路径的，需要在下层函数各个地方加log.
     例：A->B, B调了C和D, A是最上层函数，有打印，但B中C， D的返回值相同的，B中就需要加打印才能知道是C返回失败还D失败。

### 2.4 warn补充

* 代表warn信息的日志如果不需要处理，不要加入到代码中.
* warn 信息比较敏感，如果是不需要处理的warn信息（也就是说非问题类的warn 信息），我们是不希望看到的，需要明确这一类的打印，在编写log的时候，不需要处理的warn信息不要加入到代码中.
   举例：
        如果retry正常运行场景中如果经常碰到, 比如别人在用，要等一下，不需要加warn，如果正常场景不应该有的retry,可能硬件已经有异常，需要加入warn做预警。

### 2.5 打印频率

* 循环中不能打印正常日志，异常情况打印，需要跳出循环或者限制次数或频率.
  