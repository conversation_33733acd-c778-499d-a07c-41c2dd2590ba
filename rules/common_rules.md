# C/C++ 编码规则和最佳实践

## 1. 命名规范

- **变量命名**: 使用有意义的名称，避免单字母命名（除非是明确的局部临时变量，如循环索引）
- **函数命名**: 使用动词或动词短语，表明函数的作用
- **常量命名**: 全部大写，单词间用下划线分隔
- **类型命名**: 使用首字母大写的驼峰式命名法
- **宏命名**: 全部大写，单词间用下划线分隔

## 2. 格式化规则

- **缩进**: 使用4个空格或1个制表符（项目内保持一致）
- **行长度**: 每行不超过80/100个字符
- **括号风格**: 左花括号与函数/控制语句在同一行，右花括号独占一行
- **空格**: 操作符两边添加空格，函数名与左括号之间不加空格
- **代码块**: 即使是单行语句，也应使用花括号

## 3. 注释规则

- **文件头注释**: 包含文件名、作者、创建日期、版权信息等
- **函数注释**: 描述函数功能、参数、返回值、副作用等
- **复杂逻辑注释**: 对于复杂的逻辑，需要添加注释解释原理
- **TODO注释**: 使用统一格式标记待完成的工作
- **避免过时注释**: 修改代码时同步更新注释

## 4. 安全性规则

- **边界检查**: 总是进行数组边界检查
- **输入验证**: 验证所有外部输入
- **资源管理**: 使用RAII或类似技术确保资源正确释放
- **避免危险函数**: 不使用 gets()、strcpy()等不安全函数
- **空指针检查**: 在解引用前检查指针是否为NULL

## 5. 性能考虑

- **避免深拷贝**: 对大型对象使用引用或指针传递
- **适当内联**: 短小且频繁调用的函数考虑内联
- **减少动态内存分配**: 尽量使用栈内存或对象池
- **减少系统调用**: 批处理I/O操作
- **异常使用**: 只用于真正的异常情况，不用于正常流程控制

## 6. 错误处理

- **一致性**: 使用一致的错误处理策略（返回码或异常）
- **错误检查**: 检查所有可能的错误条件
- **资源清理**: 出错时确保资源被正确释放
- **错误信息**: 提供有用的错误信息
- **错误传播**: 在适当的层次处理错误

## 7. 可维护性

- **函数长度**: 每个函数应尽量短小，通常不超过50行
- **函数职责**: 每个函数只做一件事
- **复杂度控制**: 控制循环嵌套深度和条件复杂度
- **避免硬编码**: 使用常量或配置替代硬编码值
- **模块化**: 相关功能组织到同一模块

## 8. C++特有规则

- **资源管理**: 使用智能指针管理资源
- **RAII原则**: 利用构造函数/析构函数管理资源
- **避免多重继承**: 谨慎使用多重继承
- **虚析构函数**: 基类应有虚析构函数
- **避免异常滥用**: 异常只用于真正的异常情况
- **使用STL**: 优先使用标准库而非自定义实现
- **const正确性**: 正确使用const修饰符

## 9. 嵌入式系统特有规则

- **避免动态内存**: 尽量避免使用动态内存分配
- **中断处理**: 中断处理函数尽量简短
- **位操作**: 正确使用位操作处理寄存器
- **避免递归**: 避免或严格限制递归深度
- **任务优先级**: 明确定义任务优先级

## 10. 代码审查要点

- **功能正确性**: 代码是否正确实现了预期功能
- **健壮性**: 是否处理了所有边界条件和错误情况
- **可读性**: 代码是否容易理解
- **可测试性**: 代码是否易于测试
- **安全性**: 是否存在安全隐患
- **性能**: 是否有明显的性能问题
- **可维护性**: 代码结构是否清晰，易于维护

