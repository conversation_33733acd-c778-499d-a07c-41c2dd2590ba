!_TAG_EXTRA_DESCRIPTION	anonymous	/Include tags for non-named objects like lambda/
!_TAG_EXTRA_DESCRIPTION	fileScope	/Include tags of file scope/
!_TAG_EXTRA_DESCRIPTION	pseudo	/Include pseudo tags/
!_TAG_EXTRA_DESCRIPTION	subparser	/Include tags generated by subparsers/
!_TAG_FIELD_DESCRIPTION	epoch	/the last modified time of the input file (only for F\/file kind tag)/
!_TAG_FIELD_DESCRIPTION	file	/File-restricted scoping/
!_TAG_FIELD_DESCRIPTION	input	/input file/
!_TAG_FIELD_DESCRIPTION	name	/tag name/
!_TAG_FIELD_DESCRIPTION	pattern	/pattern/
!_TAG_FIELD_DESCRIPTION	typeref	/Type and name of a variable or typedef/
!_TAG_FILE_FORMAT	2	/extended format; --format=1 will not append ;" to lines/
!_TAG_FILE_SORTED	1	/0=unsorted, 1=sorted, 2=foldcase/
!_TAG_KIND_DESCRIPTION!TypeScript	C,constant	/constants/
!_TAG_KIND_DESCRIPTION!TypeScript	G,generator	/generators/
!_TAG_KIND_DESCRIPTION!TypeScript	a,alias	/aliases/
!_TAG_KIND_DESCRIPTION!TypeScript	c,class	/classes/
!_TAG_KIND_DESCRIPTION!TypeScript	e,enumerator	/enumerators (values inside an enumeration)/
!_TAG_KIND_DESCRIPTION!TypeScript	f,function	/functions/
!_TAG_KIND_DESCRIPTION!TypeScript	g,enum	/enums/
!_TAG_KIND_DESCRIPTION!TypeScript	i,interface	/interfaces/
!_TAG_KIND_DESCRIPTION!TypeScript	m,method	/methods/
!_TAG_KIND_DESCRIPTION!TypeScript	n,namespace	/namespaces/
!_TAG_KIND_DESCRIPTION!TypeScript	p,property	/properties/
!_TAG_KIND_DESCRIPTION!TypeScript	v,variable	/variables/
!_TAG_OUTPUT_EXCMD	number	/number, pattern, mixed, or combineV2/
!_TAG_OUTPUT_FILESEP	slash	/slash or backslash/
!_TAG_OUTPUT_MODE	u-ctags	/u-ctags or e-ctags/
!_TAG_OUTPUT_VERSION	0.0	/current.age/
!_TAG_PARSER_VERSION!TypeScript	0.0	/current.age/
!_TAG_PATTERN_LENGTH_LIMIT	96	/0 for no limit/
!_TAG_PROC_CWD	/home/<USER>/xiaofeng.ling/ext/sourceseek/	//
!_TAG_PROGRAM_AUTHOR	Universal Ctags Team	//
!_TAG_PROGRAM_NAME	Universal Ctags	/Derived from Exuberant Ctags/
!_TAG_PROGRAM_URL	https://ctags.io/	/official site/
!_TAG_PROGRAM_VERSION	6.1.0	/464ab03d1/
BLOCK	src/CommentToggle.ts	10;"	e	enum:CommentFormat
BOTH	src/BuildDatabase.ts	13;"	e	enum:DatabaseType
BROWSER_HISTORY_FILE	src/globalSettings.ts	7;"	C
BROWSER_HISTORY_LIMIT	src/globalSettings.ts	8;"	C
BrowserHistory	src/BrowserHistoryStore.ts	44;"	c
BrowserHistoryDragAndDropController	src/BrowserHistoryView.ts	422;"	c
BrowserHistoryItem	src/BrowserHistoryView.ts	15;"	c
BrowserHistoryViewProvider	src/BrowserHistoryView.ts	103;"	c
CCallHierarchyItem	src/CallHierarchyProvider.ts	138;"	c
CCallHierarchyProvider	src/CallHierarchyProvider.ts	158;"	c
CSCOPE	src/BuildDatabase.ts	11;"	e	enum:DatabaseType
CSCOPE_PATH	src/CallHierarchyProvider.ts	18;"	v
CTAGS	src/BuildDatabase.ts	12;"	e	enum:DatabaseType
CTAGS_PATH	src/CallHierarchyProvider.ts	19;"	v
CallNode	src/CallTree.ts	5;"	c
CallTree	src/CallTree.ts	32;"	c
ClickJumpLocation	src/CallHierarchyProvider.ts	81;"	g
CommentFormat	src/CommentToggle.ts	38;"	C	function:toggleComment
CommentFormat	src/CommentToggle.ts	8;"	g
CscopeFilesCollector	src/CollectCscopeFiles.ts	11;"	c
DEBOUNCE_DELAY	src/SymbolPreviewView.ts	21;"	C
DatabaseType	src/BuildDatabase.ts	10;"	g
Definition	src/ReadTags.ts	22;"	i
ERROR	src/CallHierarchyProvider.ts	89;"	e	enum:LogLevel
EXTENSION_ID	src/globalSettings.ts	5;"	C
EXTENSION_NAME	src/globalSettings.ts	4;"	C
ExecFunction	src/UtilFuns.ts	212;"	a
ExecOptions	src/ReadTags.ts	17;"	i
ExecResult	src/UtilFuns.ts	207;"	i
FunctionPointerMap	src/FunctionPointerMap.ts	12;"	c
HistoryEntry	src/BrowserHistoryStore.ts	35;"	i
INFO	src/CallHierarchyProvider.ts	87;"	e	enum:LogLevel
LogLevel	src/CallHierarchyProvider.ts	86;"	g
MacroDefinitionManager	src/MacroDefinition.ts	10;"	c
MultilinefunctionPattern	src/UtilFuns.ts	339;"	C
MyCallHierarchyItem	src/BrowserHistoryStore.ts	5;"	c
Number	src/CallHierarchyProvider.ts	735;"	C	function:getSymbolKind
READTAGS_PATH	src/CallHierarchyProvider.ts	20;"	v
ReadtagsProvider	src/ReadTags.ts	32;"	c
ReadtagsProvider	src/extension.ts	8;"	C
RecordField	src/UtilFuns.ts	70;"	C
RefProvider	src/RefProvider.ts	11;"	c
SINGLE_LINE	src/CommentToggle.ts	9;"	e	enum:CommentFormat
SYMBOL_KINDS	src/UtilFuns.ts	88;"	C
SYMBOL_KINDS2	src/UtilFuns.ts	33;"	C
SymbolCall	src/CallHierarchyProvider.ts	83;"	e	enum:ClickJumpLocation
SymbolDefinition	src/CallHierarchyProvider.ts	82;"	e	enum:ClickJumpLocation
SymbolInfo	src/CallHierarchyProvider.ts	93;"	c
SymbolInfoResult	src/CallHierarchyProvider.ts	152;"	i
SymbolPreviewProvider	src/SymbolPreviewView.ts	306;"	c
TASK_NAME	src/globalSettings.ts	6;"	C
WARN	src/CallHierarchyProvider.ts	88;"	e	enum:LogLevel
_	src/BrowserHistoryView.ts	257;"	C
_	src/ExportCallHierarchy.ts	218;"	C	function:formatCallHierarchyContent
_	src/ExportCallHierarchy.ts	275;"	C	function:formatReferencesContent
_extractFunctionCode	src/SymbolPreviewView.ts	879;"	f
_newestFirst	src/BrowserHistoryStore.ts	54;"	p	class:BrowserHistory
_onDidChangeDocumentSymbols	src/ReadTags.ts	35;"	p	class:ReadtagsProvider
_os	src/UtilFuns.ts	239;"	C
_spawn	src/UtilFuns.ts	238;"	C
absoluteDbPath	src/BuildDatabase.ts	193;"	C	function:generate_cscopefiles
absoluteObjRoot	src/CollectCscopeFiles.ts	555;"	C	function:collectCscopeFiles
absoluteSrcRoot	src/CollectCscopeFiles.ts	525;"	C	function:collectCscopeFiles
absoluteToRelative	src/UtilFuns.ts	187;"	f
abspath	src/UtilFuns.ts	529;"	C	function:findStructVariableAtLine
activate	src/BrowserHistoryStore.ts	675;"	f
activate	src/extension.ts	178;"	f
activeEditor	src/CallHierarchyProvider.ts	673;"	C	function:findReferences
activeEditor	src/ExportCallHierarchy.ts	47;"	C	function:exportCallHierarchy
add	src/CallTree.ts	89;"	m	class:CallTree
addChild	src/CallTree.ts	15;"	m	class:CallNode
addMacro	src/MacroDefinition.ts	224;"	m	class:MacroDefinitionManager
adjustedTargetIndex	src/BrowserHistoryStore.ts	387;"	v
adjustedTargetIndex	src/BrowserHistoryStore.ts	395;"	C
afterContent	src/CommentToggle.ts	127;"	C	function:toggleBlockComment
afterContent	src/CommentToggle.ts	93;"	C	function:toggleSingleLineComment
analysis	src/ReviewCode.ts	460;"	C	function:reviewCurrentFunction
analysis	src/ReviewCode.ts	95;"	C	function:reviewCurrentFile
answer	src/BrowserHistoryView.ts	547;"	C	function:registerBrowserHistoryView
answer	src/FunctionPointerMap.ts	736;"	C	function:registerFunctionPointerMap
answer	src/FunctionPointerMap.ts	770;"	C	function:registerFunctionPointerMap
answer	src/ReviewCode.ts	201;"	C	function:reviewCodeChanges
args	src/SymbolPreviewView.ts	983;"	C	function:getSymbolDefinitionCode
autoBuildCscopeDatabase	src/CallHierarchyProvider.ts	459;"	C	function:checkDatabase
autoBuildCtagsDatabase	src/CallHierarchyProvider.ts	478;"	C	function:checkDatabase
autoSaveInterval	src/BrowserHistoryStore.ts	53;"	p	class:BrowserHistory
autoSaveInterval	src/FunctionPointerMap.ts	22;"	p	class:FunctionPointerMap
beforeContent	src/CommentToggle.ts	126;"	C	function:toggleBlockComment
beforeContent	src/CommentToggle.ts	92;"	C	function:toggleSingleLineComment
braceDepth	src/UtilFuns.ts	342;"	v
bracketCount	src/SymbolPreviewView.ts	889;"	v
browserHistory	src/BrowserHistoryStore.ts	680;"	C	function:activate
buildDatabase	src/BuildDatabase.ts	50;"	f
callHierarchyItem	src/EditWindow.ts	679;"	C	function:registerEditWindowCommand
callHierarchyItem	src/EditWindow.ts	746;"	C	function:registerEditWindowCommand
callHierarchyItems	src/BrowserHistoryStore.ts	183;"	C
callHierarchyItems	src/BrowserHistoryStore.ts	545;"	C
callHierarchyItems	src/ExportCallHierarchy.ts	57;"	C	function:exportCallHierarchy
callHierarchyProvider	src/BrowserHistoryView.ts	9;"	C
callHierarchyProvider	src/extension.ts	14;"	C
caller	src/CallTree.ts	106;"	C	method:CallTree.add
caller	src/FunctionPointerMap.ts	378;"	C	method:FunctionPointerMap.loadFromFile
caller	src/FunctionPointerMap.ts	467;"	C	function:registerFunctionPointerMap
caller	src/FunctionPointerMap.ts	640;"	C	function:registerFunctionPointerMap
caller	src/FunctionPointerMap.ts	659;"	C	function:registerFunctionPointerMap
callerIndex	src/BrowserHistoryStore.ts	231;"	v
callerKey	src/FunctionPointerMap.ts	124;"	C	method:FunctionPointerMap.link
callerKey	src/FunctionPointerMap.ts	155;"	C	method:FunctionPointerMap.getDefinitions
callerNode	src/CallTree.ts	115;"	C	method:CallTree.add
callerToDefMap	src/FunctionPointerMap.ts	14;"	p	class:FunctionPointerMap
caller_symbol	src/ReadTags.ts	146;"	C	method:ReadtagsProvider.provideDefinition
caller_symbol_info	src/ReadTags.ts	149;"	C	method:ReadtagsProvider.provideDefinition
callers	src/FunctionPointerMap.ts	176;"	C	method:FunctionPointerMap.getCallersBySymbol
callers	src/FunctionPointerMap.ts	17;"	p	class:FunctionPointerMap
callers	src/FunctionPointerMap.ts	579;"	C	function:registerFunctionPointerMap
callname	src/FunctionPointerMap.ts	562;"	C	function:registerFunctionPointerMap
calltree	src/CallHierarchyProvider.ts	160;"	p	class:CCallHierarchyProvider
canShowFileNames	src/CallHierarchyProvider.ts	390;"	C	method:CCallHierarchyProvider.getSymbolInfo
canShowMessages	src/CallHierarchyProvider.ts	758;"	C	function:showMessageWindow
char	src/SymbolPreviewView.ts	931;"	C
checkDatabase	src/CallHierarchyProvider.ts	449;"	f
child	src/CallTree.ts	151;"	C	method:CallTree.traverseTree
child	src/CallTree.ts	78;"	C	method:CallTree.findNodeInSubtree
child	src/UtilFuns.ts	251;"	C	function:run_command
childItems	src/BrowserHistoryView.ts	171;"	C
children	src/BrowserHistoryView.ts	178;"	C
children	src/CallTree.ts	7;"	p	class:CallNode
clear	src/CallTree.ts	175;"	m	class:CallTree
clear	src/FunctionPointerMap.ts	230;"	m	class:FunctionPointerMap
clear	src/MacroDefinition.ts	166;"	m	class:MacroDefinitionManager
clickJumpLocation	src/CallHierarchyProvider.ts	391;"	C	method:CCallHierarchyProvider.getSymbolInfo
closeBraces	src/UtilFuns.ts	370;"	C
closingBracePattern	src/UtilFuns.ts	340;"	C
cmd	src/ReadTags.ts	163;"	C	method:ReadtagsProvider.provideWorkspaceSymbols
cmd	src/ReadTags.ts	76;"	C	method:ReadtagsProvider.getDefinition
cmd_result	src/globalSettings.ts	35;"	i
cmdline	src/ReadTags.ts	166;"	C	method:ReadtagsProvider.provideWorkspaceSymbols
cmdline	src/ReadTags.ts	201;"	C	method:ReadtagsProvider.provideDocumentSymbols
cmdline	src/ReadTags.ts	79;"	C	method:ReadtagsProvider.getDefinition
code	src/SymbolPreviewView.ts	955;"	C
code	src/UtilFuns.ts	245;"	C	function:run_command
code	src/globalSettings.ts	39;"	p	interface:cmd_result
codeLines	src/SymbolPreviewView.ts	954;"	C
codeText	src/SymbolPreviewView.ts	976;"	C	function:getSymbolDefinitionCode
colNum	src/ExportCallHierarchy.ts	218;"	C	function:formatCallHierarchyContent
colNum	src/ExportCallHierarchy.ts	275;"	C	function:formatReferencesContent
collapsibleState	src/BrowserHistoryView.ts	218;"	C
collectCscopeFiles	src/CollectCscopeFiles.ts	125;"	m	class:CscopeFilesCollector
collectCscopeFiles	src/CollectCscopeFiles.ts	497;"	f
collectSrc	src/CollectCscopeFiles.ts	46;"	m	class:CscopeFilesCollector
command	src/BuildDatabase.ts	129;"	C	function:buildDatabase
command	src/BuildDatabase.ts	79;"	C	function:buildDatabase
command	src/CallHierarchyProvider.ts	404;"	C	method:CCallHierarchyProvider.getSymbolInfo
command	src/CallHierarchyProvider.ts	504;"	C	function:findFiles
command	src/CallHierarchyProvider.ts	528;"	C	function:findIncluders
command	src/CallHierarchyProvider.ts	580;"	C	function:findCallees
command	src/CallHierarchyProvider.ts	599;"	C	function:findDefinition
command	src/CallHierarchyProvider.ts	624;"	C	function:findFunctionReferences
command	src/CallHierarchyProvider.ts	663;"	C	function:findReferences
command	src/CallHierarchyProvider.ts	714;"	C	function:getSymbolKind
command	src/ReadTags.ts	164;"	C	method:ReadtagsProvider.provideWorkspaceSymbols
command	src/ReadTags.ts	195;"	C	method:ReadtagsProvider.provideDocumentSymbols
command	src/ReadTags.ts	77;"	C	method:ReadtagsProvider.getDefinition
command	src/ReviewCode.ts	226;"	C	function:reviewDiffs
command	src/UtilFuns.ts	412;"	C	function:getFunctionNameFromFileAndLineUsingTags
command	src/extension.ts	96;"	C	function:initializeReadtags
commandGuard	src/UtilFuns.ts	22;"	f
commandGuard	src/extension.ts	10;"	C
commands	src/extension.ts	58;"	C	function:initializeSubscriptions
commentFormat	src/CommentToggle.ts	38;"	C	function:toggleComment
commentFormatSetting	src/CommentToggle.ts	37;"	C	function:toggleComment
commentIndex	src/CommentToggle.ts	77;"	C	function:toggleSingleLineComment
completion	src/ReviewCode.ts	16;"	C	function:requestAI
condition	src/EditWindow.ts	229;"	C
condition	src/EditWindow.ts	353;"	C
conditionResult	src/EditWindow.ts	232;"	v
conditionResult	src/EditWindow.ts	373;"	v
config	src/BrowserHistoryStore.ts	677;"	C	function:activate
config	src/BuildDatabase.ts	17;"	C	function:getCommandPath
config	src/BuildDatabase.ts	206;"	C	function:generate_cscopefiles
config	src/BuildDatabase.ts	268;"	C	function:showBuildDialog
config	src/BuildDatabase.ts	37;"	C
config	src/CallHierarchyProvider.ts	389;"	C	method:CCallHierarchyProvider.getSymbolInfo
config	src/CallHierarchyProvider.ts	458;"	C	function:checkDatabase
config	src/CallHierarchyProvider.ts	477;"	C	function:checkDatabase
config	src/CallHierarchyProvider.ts	757;"	C	function:showMessageWindow
config	src/CollectCscopeFiles.ts	210;"	C	function:showConfigDialog
config	src/CollectCscopeFiles.ts	443;"	C	function:showConfigDialog
config	src/CollectCscopeFiles.ts	470;"	C	function:showConfigDialog
config	src/CommentToggle.ts	36;"	C	function:toggleComment
config	src/MacroDefinition.ts	187;"	C	method:MacroDefinitionManager.saveMacroFilePathToWorkspace
config	src/MacroDefinition.ts	202;"	C	method:MacroDefinitionManager.loadMacroFilePathFromWorkspace
config	src/ReviewCode.ts	357;"	C	function:getCustomerRuleFile
config	src/SymbolPreviewView.ts	130;"	C	function:togglePreviewMode
config	src/SymbolPreviewView.ts	50;"	C	function:loadSettings
config	src/SymbolPreviewView.ts	68;"	C	function:saveSettings
config_field_str	src/globalSettings.ts	53;"	C
config_variable_str	src/globalSettings.ts	46;"	C
const	src/UtilFuns.ts	35;"	C
constant	src/UtilFuns.ts	36;"	C
constractor	src/UtilFuns.ts	37;"	C
constructor	src/CallHierarchyProvider.ts	163;"	m	class:CCallHierarchyProvider
constructor	src/CallHierarchyProvider.ts	99;"	m	class:SymbolInfo
constructor	src/CallTree.ts	10;"	m	class:CallNode
constructor	src/CallTree.ts	36;"	m	class:CallTree
constructor	src/CollectCscopeFiles.ts	18;"	m	class:CscopeFilesCollector
constructor	src/FunctionPointerMap.ts	26;"	m	class:FunctionPointerMap
constructor	src/ReadTags.ts	38;"	m	class:ReadtagsProvider
content	src/CollectCscopeFiles.ts	48;"	C	method:CscopeFilesCollector.collectSrc
content	src/ExportCallHierarchy.ts	21;"	C	function:exportCallHierarchy
content	src/MacroDefinition.ts	22;"	C	method:MacroDefinitionManager.loadFromFile
content	src/MacroDefinition.ts	275;"	C	function:showCustomMacroDialog
context	src/CallHierarchyProvider.ts	161;"	p	class:CCallHierarchyProvider
context	src/FunctionPointerMap.ts	24;"	p	class:FunctionPointerMap
context	src/ReadTags.ts	34;"	p	class:ReadtagsProvider
contextLines	src/SymbolPreviewView.ts	884;"	C
convertToFuncInfo	src/CallHierarchyProvider.ts	108;"	m	class:SymbolInfo
convertToFuncRefInfo	src/CallHierarchyProvider.ts	113;"	m	class:SymbolInfo
convertToSymbolInfo	src/CallHierarchyProvider.ts	119;"	m	class:SymbolInfo
copyCount	src/BrowserHistoryStore.ts	643;"	C
count	src/MacroDefinition.ts	34;"	C	method:MacroDefinitionManager.loadFromFile
createIfdefDecorations	src/EditWindow.ts	44;"	f
createSymbolInfo	src/FunctionPointerMap.ts	346;"	C	method:FunctionPointerMap.loadFromFile
cs_caller	src/CallHierarchyProvider.ts	564;"	C	function:findCallers
cscopeFilesExists	src/BuildDatabase.ts	69;"	C	function:buildDatabase
cscopeFilesPath	src/BuildDatabase.ts	203;"	C	function:generate_cscopefiles
cscopeFilesPath	src/BuildDatabase.ts	68;"	C	function:buildDatabase
cscopeFilesUri	src/CollectCscopeFiles.ts	403;"	C	function:showConfigDialog
cscopeFindAllRefCommand	src/globalSettings.ts	18;"	C
cscopeFindCalleeCommand	src/globalSettings.ts	20;"	C
cscopeFindCallerCommand	src/globalSettings.ts	21;"	C
cscopeFindDefineCommand	src/globalSettings.ts	19;"	C
cscopeFindTextCommand	src/globalSettings.ts	22;"	C
cscopeGenerateCommand	src/globalSettings.ts	17;"	C
cscopePath	src/extension.ts	150;"	C	function:initializeCscope
cscope_path	src/CallHierarchyProvider.ts	553;"	C	function:findCallers
cscopesDbPath	src/BuildDatabase.ts	270;"	C	function:showBuildDialog
cscopesDbPath	src/BuildDatabase.ts	57;"	C	function:buildDatabase
cscopesDbPath	src/CallHierarchyProvider.ts	332;"	C	method:CCallHierarchyProvider.provideCallHierarchyOutgoingCalls
cscopesDbPath	src/CallHierarchyProvider.ts	392;"	C	method:CCallHierarchyProvider.getSymbolInfo
cscopesDbPath	src/CallHierarchyProvider.ts	452;"	C	function:checkDatabase
cscopesDbPath	src/CallHierarchyProvider.ts	503;"	C	function:findFiles
cscopesDbPath	src/CallHierarchyProvider.ts	527;"	C	function:findIncluders
cscopesDbPath	src/CallHierarchyProvider.ts	552;"	C	function:findCallers
cscopesDbPath	src/CallHierarchyProvider.ts	579;"	C	function:findCallees
cscopesDbPath	src/CallHierarchyProvider.ts	598;"	C	function:findDefinition
cscopesDbPath	src/CallHierarchyProvider.ts	623;"	C	function:findFunctionReferences
cscopesDbPath	src/CallHierarchyProvider.ts	662;"	C	function:findReferences
ctag_message_show	src/CallHierarchyProvider.ts	16;"	v
ctagsDbPath	src/BuildDatabase.ts	57;"	C	function:buildDatabase
ctagsDbPath	src/CallHierarchyProvider.ts	452;"	C	function:checkDatabase
ctagsDbPath	src/CallHierarchyProvider.ts	713;"	C	function:getSymbolKind
ctagsDbPath	src/UtilFuns.ts	411;"	C	function:getFunctionNameFromFileAndLineUsingTags
ctagsGenerateCommand	src/globalSettings.ts	15;"	C
ctagsGenerateCommandWin	src/globalSettings.ts	16;"	C
ctagsGoToSymbolInEditorCommand	src/globalSettings.ts	13;"	C
ctagsGoToSymbolInEditorCommandWin	src/globalSettings.ts	14;"	C
currentDescription	src/BrowserHistoryView.ts	364;"	C
currentFilePath	src/CallHierarchyProvider.ts	675;"	C	function:findReferences
currentFilePath	src/ReviewCode.ts	141;"	C	function:reviewCodeChanges
currentFilePath	src/ReviewCode.ts	39;"	C	function:reviewCurrentFile
currentFilePath	src/ReviewCode.ts	432;"	C	function:reviewCurrentFunction
currentFunction	src/UtilFuns.ts	333;"	v
currentFunctionName	src/UtilFuns.ts	344;"	v
currentLine	src/ReviewCode.ts	77;"	C	function:reviewCurrentFile
currentLineNumber	src/ReviewCode.ts	412;"	C	function:reviewCurrentFunction
currentLineNumber	src/UtilFuns.ts	349;"	C
currentState	src/EditWindow.ts	350;"	C
currentState	src/EditWindow.ts	431;"	C
currentTime	src/SymbolPreviewView.ts	739;"	C	function:getSymbolDefinitions
currentUri	src/SymbolPreviewView.ts	740;"	C	function:getSymbolDefinitions
currentViewColumn	src/SymbolPreviewView.ts	1142;"	C	function:registerSymbolPreviewView
current_os	src/UtilFuns.ts	281;"	f
cursorPosition	src/ReviewCode.ts	411;"	C	function:reviewCurrentFunction
cursorPosition	src/SymbolPreviewView.ts	1026;"	C	function:handleHoverAndClick
cursorPosition	src/SymbolPreviewView.ts	1152;"	C	function:registerSymbolPreviewView
customMacrosFilePath	src/BuildDatabase.ts	149;"	C	function:buildDatabase
customMacrosFilePath	src/BuildDatabase.ts	99;"	C	function:buildDatabase
customMacrosFilePath	src/MacroDefinition.ts	269;"	C	function:showCustomMacroDialog
customMacrosFilePath	src/MacroDefinition.ts	613;"	C	function:loadCustomMacros
customMacrosFilePath	src/ReadTags.ts	188;"	C	method:ReadtagsProvider.provideDocumentSymbols
customRuleFile	src/ReviewCode.ts	281;"	C	function:reviewCurrentFileWithCustomRule
cwd	src/CallHierarchyProvider.ts	159;"	p	class:CCallHierarchyProvider
cwd	src/ReadTags.ts	165;"	C	method:ReadtagsProvider.provideWorkspaceSymbols
cwd	src/ReadTags.ts	18;"	p	interface:ExecOptions
cwd	src/ReadTags.ts	200;"	C	method:ReadtagsProvider.provideDocumentSymbols
cwd	src/ReadTags.ts	78;"	C	method:ReadtagsProvider.getDefinition
databasePath	src/BuildDatabase.ts	271;"	C	function:showBuildDialog
databasePath	src/BuildDatabase.ts	38;"	C
databasePath	src/BuildDatabase.ts	58;"	C	function:buildDatabase
databasePathUri	src/BuildDatabase.ts	490;"	C	function:showBuildDialog
database_path	src/extension.ts	159;"	C	function:initializeCscope
deactivate	src/extension.ts	285;"	f
decorationsArray	src/EditWindow.ts	75;"	C	function:updateIfdefDecorations
def	src/FunctionPointerMap.ts	648;"	C	function:registerFunctionPointerMap
def	src/FunctionPointerMap.ts	663;"	C	function:registerFunctionPointerMap
defKey	src/FunctionPointerMap.ts	132;"	C	method:FunctionPointerMap.link
defKey	src/FunctionPointerMap.ts	220;"	C	method:FunctionPointerMap.delDefinition
defaultCscopeFiles	src/CollectCscopeFiles.ts	204;"	C	function:showConfigDialog
defaultKernelOutput	src/CollectCscopeFiles.ts	201;"	C	function:showConfigDialog
defaultMacroFile	src/CollectCscopeFiles.ts	207;"	C	function:showConfigDialog
defaultPath	src/BrowserHistoryView.ts	642;"	C	function:registerBrowserHistoryView
defaultPath	src/FunctionPointerMap.ts	683;"	C	function:registerFunctionPointerMap
defaultPath	src/MacroDefinition.ts	654;"	C	function:registerMacroDefinitionCommand
default_config_values	src/globalSettings.ts	64;"	C
define	src/UtilFuns.ts	38;"	C
definition	src/CallHierarchyProvider.ts	735;"	C	function:getSymbolKind
definition	src/EditWindow.ts	654;"	C	function:registerEditWindowCommand
definition	src/FunctionPointerMap.ts	392;"	C	method:FunctionPointerMap.loadFromFile
definition	src/FunctionPointerMap.ts	568;"	C	function:registerFunctionPointerMap
definition	src/SymbolPreviewView.ts	384;"	C
definition	src/SymbolPreviewView.ts	788;"	C	function:openSymbolDefinitionInEditor
definition	src/SymbolPreviewView.ts	967;"	C	function:getSymbolDefinitionCode
definitionDocument	src/SymbolPreviewView.ts	389;"	C
definitionDocument	src/SymbolPreviewView.ts	793;"	C	function:openSymbolDefinitionInEditor
definitionDocument	src/SymbolPreviewView.ts	972;"	C	function:getSymbolDefinitionCode
definitionRange	src/EditWindow.ts	656;"	C	function:registerEditWindowCommand
definitionRange	src/SymbolPreviewView.ts	386;"	C
definitionRange	src/SymbolPreviewView.ts	790;"	C	function:openSymbolDefinitionInEditor
definitionRange	src/SymbolPreviewView.ts	969;"	C	function:getSymbolDefinitionCode
definitionToSymbolInformation	src/UtilFuns.ts	139;"	f
definitionUri	src/EditWindow.ts	655;"	C	function:registerEditWindowCommand
definitionUri	src/SymbolPreviewView.ts	385;"	C
definitionUri	src/SymbolPreviewView.ts	789;"	C	function:openSymbolDefinitionInEditor
definitionUri	src/SymbolPreviewView.ts	968;"	C	function:getSymbolDefinitionCode
definitions	src/FunctionPointerMap.ts	164;"	C	method:FunctionPointerMap.getDefinitionsBySymbol
definitions	src/FunctionPointerMap.ts	18;"	p	class:FunctionPointerMap
definitions	src/FunctionPointerMap.ts	383;"	C	method:FunctionPointerMap.loadFromFile
definitions	src/FunctionPointerMap.ts	641;"	C	function:registerFunctionPointerMap
definitions	src/ReadTags.ts	168;"	C	method:ReadtagsProvider.provideWorkspaceSymbols
definitions	src/ReadTags.ts	203;"	C	method:ReadtagsProvider.provideDocumentSymbols
definitions	src/ReadTags.ts	84;"	C	method:ReadtagsProvider.getDefinition
definitions	src/SymbolPreviewView.ts	380;"	C
definitions	src/SymbolPreviewView.ts	784;"	C	function:openSymbolDefinitionInEditor
defs	src/UtilFuns.ts	140;"	C	function:definitionToSymbolInformation
delCaller	src/FunctionPointerMap.ts	206;"	m	class:FunctionPointerMap
delDefinition	src/FunctionPointerMap.ts	219;"	m	class:FunctionPointerMap
delMapByCaller	src/FunctionPointerMap.ts	191;"	m	class:FunctionPointerMap
delayMs	src/UtilFuns.ts	10;"	f
description	src/BrowserHistoryStore.ts	160;"	C
description	src/BrowserHistoryStore.ts	40;"	p	interface:HistoryEntry
description	src/BrowserHistoryStore.ts	572;"	C
description	src/CallHierarchyProvider.ts	153;"	p	interface:SymbolInfoResult
description	src/CallHierarchyProvider.ts	97;"	p	class:SymbolInfo
determineScope	src/UtilFuns.ts	14;"	f
dirPath	src/CollectCscopeFiles.ts	130;"	C	method:CscopeFilesCollector.collectCscopeFiles
disposable	src/BrowserHistoryView.ts	790;"	C	function:registerBrowserHistoryView
disposable	src/CommentToggle.ts	141;"	C	function:registerCommentToggleCommand
disposable	src/ExportCallHierarchy.ts	289;"	C	function:registerExportCallHierarchyCommand
disposableOpenRule	src/ReviewCode.ts	497;"	C	function:registerReviewCodeCommand
disposableReviewChanges	src/ReviewCode.ts	490;"	C	function:registerReviewCodeCommand
disposableReviewCurrentFile	src/ReviewCode.ts	492;"	C	function:registerReviewCodeCommand
disposableReviewCurrentFileWithCustomRule	src/ReviewCode.ts	493;"	C	function:registerReviewCodeCommand
disposableReviewCurrentFunction	src/ReviewCode.ts	494;"	C	function:registerReviewCodeCommand
disposableReviewLastCommit	src/ReviewCode.ts	491;"	C	function:registerReviewCodeCommand
disposableReviewLastGitCommit	src/ReviewCode.ts	496;"	C	function:registerReviewCodeCommand
disposableReviewWholeGitDiff	src/ReviewCode.ts	495;"	C	function:registerReviewCodeCommand
dispose	src/FunctionPointerMap.ts	433;"	m	class:FunctionPointerMap
dist	src/UtilFuns.ts	613;"	C	function:getFullPath
doCLI	src/UtilFuns.ts	116;"	f
docLines	src/CallHierarchyProvider.ts	734;"	C	function:getSymbolKind
document	src/CallHierarchyProvider.ts	340;"	C	method:CCallHierarchyProvider.provideCallHierarchyOutgoingCalls
document	src/CommentToggle.ts	23;"	C	function:toggleComment
document	src/EditWindow.ts	638;"	C	function:registerEditWindowCommand
document	src/EditWindow.ts	731;"	C	function:registerEditWindowCommand
document	src/EditWindow.ts	809;"	C	function:registerEditWindowCommand
document	src/ExportCallHierarchy.ts	54;"	C	function:exportCallHierarchy
document	src/MacroDefinition.ts	553;"	C	function:showCustomMacroDialog
document	src/SymbolPreviewView.ts	1015;"	C	function:handleHoverAndClick
document	src/SymbolPreviewView.ts	1214;"	C	function:registerSymbolPreviewView
document	src/SymbolPreviewView.ts	339;"	C
document	src/UtilFuns.ts	530;"	C	function:findStructVariableAtLine
documentSelector	src/extension.ts	89;"	C	function:initializeReadtags
docuri	src/BrowserHistoryView.ts	388;"	v
edit	src/EditWindow.ts	30;"	C	function:refreshDocumentSymbols
editor	src/BrowserHistoryView.ts	403;"	C
editor	src/CommentToggle.ts	17;"	C	function:toggleComment
editor	src/EditWindow.ts	27;"	C	function:refreshDocumentSymbols
editor	src/EditWindow.ts	526;"	C	function:registerEditWindowCommand
editor	src/EditWindow.ts	599;"	C	function:registerEditWindowCommand
editor	src/EditWindow.ts	631;"	C	function:registerEditWindowCommand
editor	src/EditWindow.ts	707;"	C	function:registerEditWindowCommand
editor	src/ExportCallHierarchy.ts	20;"	C	function:exportCallHierarchy
editor	src/FunctionPointerMap.ts	443;"	C	function:registerFunctionPointerMap
editor	src/FunctionPointerMap.ts	546;"	C	function:registerFunctionPointerMap
editor	src/ReviewCode.ts	125;"	C	function:reviewCodeChanges
editor	src/ReviewCode.ts	32;"	C	function:reviewCurrentFile
editor	src/ReviewCode.ts	404;"	C	function:reviewCurrentFunction
editor	src/SymbolPreviewView.ts	340;"	C
editor	src/extension.ts	312;"	C	function:refreshOutlineByForceSave
enableCallHierarchy	src/extension.ts	50;"	C	function:initializeSubscriptions
enableDefinitionSearch	src/extension.ts	115;"	C	function:initializeReadtags
enableDocumentSymbol	src/extension.ts	127;"	C	function:initializeReadtags
enableSourceSeek	src/extension.ts	180;"	C	function:activate
enableWorkspaceSymbol	src/extension.ts	121;"	C	function:initializeReadtags
endLine	src/EditWindow.ts	386;"	v
endLine	src/SymbolPreviewView.ts	886;"	v
endLine	src/UtilFuns.ts	333;"	v
endPos	src/UtilFuns.ts	501;"	C	function:getFunctionCode
endPos	src/UtilFuns.ts	511;"	C	function:getFunctionCode
end_pos	src/RefProvider.ts	38;"	v
entrie	src/BrowserHistoryView.ts	268;"	C
entries	src/CollectCscopeFiles.ts	158;"	C	method:CscopeFilesCollector.walkDirectory
entries	src/FunctionPointerMap.ts	502;"	C	function:registerFunctionPointerMap
entriesToMove	src/BrowserHistoryStore.ts	376;"	C
entry	src/BrowserHistoryStore.ts	155;"	C
entry	src/BrowserHistoryStore.ts	199;"	v
entry	src/BrowserHistoryStore.ts	286;"	C
entry	src/BrowserHistoryStore.ts	300;"	C
entry	src/BrowserHistoryStore.ts	310;"	C
entry	src/BrowserHistoryStore.ts	324;"	v
entry	src/BrowserHistoryStore.ts	337;"	C
entry	src/BrowserHistoryStore.ts	360;"	C
entry	src/BrowserHistoryStore.ts	485;"	C
entry	src/BrowserHistoryStore.ts	567;"	C
entry	src/BrowserHistoryView.ts	167;"	C
entry	src/BrowserHistoryView.ts	274;"	C
entry	src/BrowserHistoryView.ts	285;"	C
entry	src/BrowserHistoryView.ts	357;"	C
entry	src/BrowserHistoryView.ts	728;"	C	function:registerBrowserHistoryView
entry	src/CollectCscopeFiles.ts	160;"	C	method:CscopeFilesCollector.walkDirectory
entry	src/FunctionPointerMap.ts	129;"	C	method:FunctionPointerMap.link
entry	src/FunctionPointerMap.ts	156;"	C	method:FunctionPointerMap.getDefinitions
entry	src/FunctionPointerMap.ts	377;"	C	method:FunctionPointerMap.loadFromFile
entry	src/FunctionPointerMap.ts	483;"	C	function:registerFunctionPointerMap
entryData	src/BrowserHistoryStore.ts	544;"	C
entryData	src/BrowserHistoryView.ts	727;"	C	function:registerBrowserHistoryView
enumConstant	src/UtilFuns.ts	40;"	C
enumerator	src/UtilFuns.ts	41;"	C
escapedPattern	src/BuildDatabase.ts	215;"	C	function:generate_cscopefiles
event	src/UtilFuns.ts	42;"	C
excludedPaths	src/BuildDatabase.ts	207;"	C	function:generate_cscopefiles
exec	src/CollectCscopeFiles.ts	567;"	C	function:collectCscopeFiles
exec	src/ReadTags.ts	33;"	p	class:ReadtagsProvider
exec	src/SymbolPreviewView.ts	10;"	C
exec	src/extension.ts	5;"	C
executablePath	src/BuildDatabase.ts	25;"	C	function:getCommandPath
executeCommand	src/ReviewCode.ts	255;"	f
existed	src/MacroDefinition.ts	235;"	C	method:MacroDefinitionManager.removeMacro
existingChild	src/CallTree.ts	108;"	C	method:CallTree.add
existingEntry	src/BrowserHistoryStore.ts	142;"	C
exportCallHierarchy	src/ExportCallHierarchy.ts	6;"	f
exportContent	src/BrowserHistoryView.ts	293;"	v
exportContent	src/ExportCallHierarchy.ts	30;"	C	function:exportCallHierarchy
exportContent	src/ExportCallHierarchy.ts	39;"	C	function:exportCallHierarchy
extension	src/CscopeFind.ts	7;"	C	function:registerCscopeMoreCommand
extension	src/MacroDefinition.ts	633;"	C	function:registerMacroDefinitionCommand
extensionPath	src/ReviewCode.ts	291;"	C	function:selectRuleFileFromExtension
extensionPath	src/ReviewCode.ts	66;"	C	function:reviewCurrentFile
externvar	src/UtilFuns.ts	43;"	C
field	src/UtilFuns.ts	44;"	C
file	src/CallHierarchyProvider.ts	426;"	C	method:CCallHierarchyProvider.getWordRange
file	src/UtilFuns.ts	148;"	C	function:definitionToSymbolInformation
fileContent	src/BrowserHistoryStore.ts	534;"	C
fileContent	src/BrowserHistoryView.ts	715;"	C	function:registerBrowserHistoryView
fileContent	src/BuildDatabase.ts	242;"	C	function:generate_cscopefiles
fileContent	src/CollectCscopeFiles.ts	33;"	C	method:CscopeFilesCollector.saveListToFile
fileContent	src/FunctionPointerMap.ts	339;"	C	method:FunctionPointerMap.loadFromFile
fileContent	src/MacroDefinition.ts	21;"	C	method:MacroDefinitionManager.loadFromFile
fileContent	src/ReviewCode.ts	41;"	C	function:reviewCurrentFile
fileContent	src/SymbolPreviewView.ts	392;"	C
fileCount	src/BuildDatabase.ts	243;"	C	function:generate_cscopefiles
fileDirectory	src/ReviewCode.ts	142;"	C	function:reviewCodeChanges
fileList	src/CollectCscopeFiles.ts	15;"	p	class:CscopeFilesCollector
fileMap	src/CollectCscopeFiles.ts	14;"	p	class:CscopeFilesCollector
fileMatch	src/ExportCallHierarchy.ts	216;"	C	function:formatCallHierarchyContent
fileMatch	src/ExportCallHierarchy.ts	273;"	C	function:formatReferencesContent
fileName	src/BrowserHistoryView.ts	29;"	C
fileName	src/RefProvider.ts	32;"	v
fileName	src/ReviewCode.ts	143;"	C	function:reviewCodeChanges
fileName	src/ReviewCode.ts	40;"	C	function:reviewCurrentFile
fileName	src/ReviewCode.ts	433;"	C	function:reviewCurrentFunction
fileName	src/SymbolPreviewView.ts	395;"	C
fileName	src/SymbolPreviewView.ts	417;"	C
fileName	src/SymbolPreviewView.ts	435;"	C
filePath	src/BrowserHistoryView.ts	33;"	v
filePath	src/CallHierarchyProvider.ts	154;"	p	interface:SymbolInfoResult
filePath	src/CallHierarchyProvider.ts	339;"	C	method:CCallHierarchyProvider.provideCallHierarchyOutgoingCalls
filePath	src/CallHierarchyProvider.ts	95;"	p	class:SymbolInfo
filePath	src/EditWindow.ts	801;"	C	function:registerEditWindowCommand
filePath	src/ExportCallHierarchy.ts	218;"	C	function:formatCallHierarchyContent
filePath	src/ExportCallHierarchy.ts	275;"	C	function:formatReferencesContent
filePath	src/MacroDefinition.ts	203;"	C	method:MacroDefinitionManager.loadMacroFilePathFromWorkspace
fileRelativePath	src/UtilFuns.ts	415;"	C	function:getFunctionNameFromFileAndLineUsingTags
fileUri	src/BrowserHistoryView.ts	646;"	C	function:registerBrowserHistoryView
fileUri	src/BrowserHistoryView.ts	712;"	C	function:registerBrowserHistoryView
fileUri	src/FunctionPointerMap.ts	687;"	C	function:registerFunctionPointerMap
fileUri	src/FunctionPointerMap.ts	729;"	C	function:registerFunctionPointerMap
fileUri	src/ReviewCode.ts	362;"	C	function:getCustomerRuleFile
fileUris	src/BrowserHistoryView.ts	698;"	C	function:registerBrowserHistoryView
fileUris	src/FunctionPointerMap.ts	715;"	C	function:registerFunctionPointerMap
fileUris	src/MacroDefinition.ts	659;"	C	function:registerMacroDefinitionCommand
filecontext	src/ReviewCode.ts	193;"	C	function:reviewCodeChanges
filepath	src/SymbolPreviewView.ts	993;"	C	function:getSymbolDefinitionCode
files	src/ReviewCode.ts	298;"	C	function:selectRuleFileFromExtension
filterSymbolInfos	src/UtilFuns.ts	589;"	f
filteredEntries	src/BrowserHistoryView.ts	207;"	C
filteredItems	src/BrowserHistoryView.ts	174;"	C
filteredLines	src/CallHierarchyProvider.ts	628;"	C	function:findFunctionReferences
filteredSymbolInfos	src/EditWindow.ts	646;"	C	function:registerEditWindowCommand
filteredSymbolInfos	src/ReadTags.ts	140;"	C	method:ReadtagsProvider.provideDefinition
filteredSymbolInfos	src/SymbolPreviewView.ts	1175;"	C	function:registerSymbolPreviewView
filteredSymbolInfos	src/SymbolPreviewView.ts	1219;"	C	function:registerSymbolPreviewView
filteredSymbolInfos	src/SymbolPreviewView.ts	379;"	C
finalMap	src/CollectCscopeFiles.ts	16;"	p	class:CscopeFilesCollector
findCallees	src/CallHierarchyProvider.ts	578;"	f
findCallers	src/CallHierarchyProvider.ts	542;"	f
findDefinition	src/CallHierarchyProvider.ts	596;"	f
findField	src/UtilFuns.ts	200;"	f
findFiles	src/CallHierarchyProvider.ts	502;"	f
findFunctionNameByLine	src/UtilFuns.ts	310;"	f
findFunctionReferences	src/CallHierarchyProvider.ts	619;"	f
findIncluders	src/CallHierarchyProvider.ts	526;"	f
findNodeBySymbol	src/CallTree.ts	56;"	m	class:CallTree
findNodeInSubtree	src/CallTree.ts	73;"	m	class:CallTree
findParentSymbol	src/CallTree.ts	181;"	m	class:CallTree
findReferences	src/CallHierarchyProvider.ts	658;"	f
findRootSymbol	src/CallTree.ts	191;"	m	class:CallTree
findStructVariableAtLine	src/UtilFuns.ts	527;"	f
firstNonWhitespaceIndex	src/CommentToggle.ts	115;"	C	function:toggleBlockComment
firstNonWhitespaceIndex	src/CommentToggle.ts	124;"	C	function:toggleBlockComment
firstNonWhitespaceIndex	src/CommentToggle.ts	90;"	C	function:toggleSingleLineComment
fname	src/CollectCscopeFiles.ts	76;"	C	method:CscopeFilesCollector.makeCscopeFiles
formatCallHierarchyContent	src/ExportCallHierarchy.ts	152;"	f
formatReferencesContent	src/ExportCallHierarchy.ts	235;"	f
found	src/CallTree.ts	79;"	C	method:CallTree.findNodeInSubtree
foundStart	src/SymbolPreviewView.ts	890;"	v
fp_callers	src/CallHierarchyProvider.ts	543;"	C	function:findCallers
fp_definitions	src/ReadTags.ts	64;"	C	method:ReadtagsProvider.getDefinition
fp_definitions	src/ReadTags.ts	70;"	C	method:ReadtagsProvider.getDefinition
fpath	src/CallHierarchyProvider.ts	408;"	C	method:CCallHierarchyProvider.getSymbolInfo
fpath	src/CallHierarchyProvider.ts	512;"	C	function:findFiles
fs	src/EditWindow.ts	5;"	C
fs	src/extension.ts	15;"	C
fullPath	src/CollectCscopeFiles.ts	161;"	C	method:CscopeFilesCollector.walkDirectory
func	src/UtilFuns.ts	396;"	C
func	src/UtilFuns.ts	446;"	C	function:getFunctionNameFromFileAndLineUsingTags
func	src/UtilFuns.ts	45;"	C
functionCode	src/SymbolPreviewView.ts	401;"	C
functionCodeAndLine	src/ReviewCode.ts	415;"	C	function:reviewCurrentFunction
functionDeclarationRegex	src/CallHierarchyProvider.ts	692;"	C	function:findReferences
functionDefinitions	src/FunctionPointerMap.ts	480;"	C	function:registerFunctionPointerMap
functionEndLine	src/SymbolPreviewView.ts	892;"	v
functionMatch	src/UtilFuns.ts	353;"	C
functionMatch	src/UtilFuns.ts	359;"	C
functionPattern	src/UtilFuns.ts	338;"	C
functionStack	src/UtilFuns.ts	334;"	C
functionStartLine	src/SymbolPreviewView.ts	891;"	v
functionStartLine	src/UtilFuns.ts	343;"	v
functionVar	src/UtilFuns.ts	47;"	C
function_state	src/UtilFuns.ts	345;"	v
functions	src/UtilFuns.ts	426;"	C	function:getFunctionNameFromFileAndLineUsingTags
g	src/EditWindow.ts	166;"	C	function:updateIfdefDecorations
g_browserHistory	src/extension.ts	32;"	v
g_browserHistoryViewProvider	src/BrowserHistoryView.ts	13;"	v
g_buildInProgress	src/BuildDatabase.ts	8;"	v
g_callhierarchyAddHistory	src/SymbolPreviewView.ts	31;"	v
g_checkSymbolKind	src/SymbolPreviewView.ts	34;"	v
g_context	src/extension.ts	30;"	v
g_context	src/globalSettings.ts	26;"	v
g_cscopeupdate	src/globalSettings.ts	25;"	v
g_findAssignment	src/CallHierarchyProvider.ts	22;"	v
g_findGrep	src/CallHierarchyProvider.ts	23;"	v
g_findInclude	src/CallHierarchyProvider.ts	24;"	v
g_fnstructOnly	src/SymbolPreviewView.ts	33;"	v
g_functionOnlyFilter	src/EditWindow.ts	16;"	v
g_functionPointerMap	src/extension.ts	33;"	v
g_hoverAddHistory	src/SymbolPreviewView.ts	28;"	v
g_hoverCallTrigger	src/SymbolPreviewView.ts	30;"	v
g_hoverCallerClickTrigger	src/SymbolPreviewView.ts	27;"	v
g_hoverCallerEnabled	src/SymbolPreviewView.ts	26;"	v
g_hoverPosition	src/SymbolPreviewView.ts	1052;"	v
g_hoverPreviewEnabled	src/SymbolPreviewView.ts	25;"	v
g_hoverThreads	src/SymbolPreviewView.ts	1050;"	v
g_isHovering	src/SymbolPreviewView.ts	1051;"	v
g_macroDefinitionManager	src/MacroDefinition.ts	245;"	v
g_middleClick	src/EditWindow.ts	19;"	v
g_nonstatic	src/EditWindow.ts	17;"	v
g_openInNewWindow	src/SymbolPreviewView.ts	24;"	v
g_openRightWindow	src/SymbolPreviewView.ts	29;"	v
g_readtagThreads	src/ReadTags.ts	27;"	v
g_showCommand	src/UtilFuns.ts	107;"	v
g_showNonActiveCodeAsGrey	src/EditWindow.ts	18;"	v
generate_cscopefiles	src/BuildDatabase.ts	185;"	f
getAllMacros	src/MacroDefinition.ts	174;"	m	class:MacroDefinitionManager
getAllSymbols	src/CallTree.ts	138;"	m	class:CallTree
getCSCOPE_PATH	src/CallHierarchyProvider.ts	57;"	f
getCTAGS_PATH	src/CallHierarchyProvider.ts	61;"	f
getCallersBySymbol	src/FunctionPointerMap.ts	175;"	m	class:FunctionPointerMap
getCommandPath	src/BuildDatabase.ts	16;"	f
getCommandPath	src/ReadTags.ts	47;"	m	class:ReadtagsProvider
getConfiguration	src/UtilFuns.ts	18;"	f
getConfiguration	src/extension.ts	10;"	C
getCscopeCommand	src/CallHierarchyProvider.ts	133;"	f
getCustomerRuleFile	src/ReviewCode.ts	355;"	f
getDatabasePath	src/BuildDatabase.ts	36;"	f
getDefinition	src/ReadTags.ts	51;"	m	class:ReadtagsProvider
getDefinitions	src/FunctionPointerMap.ts	154;"	m	class:FunctionPointerMap
getDefinitionsBySymbol	src/FunctionPointerMap.ts	163;"	m	class:FunctionPointerMap
getFileName	src/CallHierarchyProvider.ts	125;"	m	class:SymbolInfo
getFullPath	src/UtilFuns.ts	606;"	f
getFunctionCode	src/UtilFuns.ts	467;"	f
getFunctionCodeByLine	src/UtilFuns.ts	315;"	f
getFunctionNameAndStartLine	src/UtilFuns.ts	320;"	f
getFunctionNameFromFileAndLineUsingTags	src/UtilFuns.ts	406;"	f
getLastLoadedFile	src/MacroDefinition.ts	159;"	m	class:MacroDefinitionManager
getMacroCount	src/MacroDefinition.ts	152;"	m	class:MacroDefinitionManager
getMacroNumericValue	src/MacroDefinition.ts	119;"	m	class:MacroDefinitionManager
getMacroValue	src/MacroDefinition.ts	91;"	m	class:MacroDefinitionManager
getNodesForSymbol	src/CallTree.ts	128;"	m	class:CallTree
getPathToSymbol	src/CallTree.ts	157;"	m	class:CallTree
getREADTAGS_PATH	src/CallHierarchyProvider.ts	65;"	f
getRelativePath	src/UtilFuns.ts	631;"	f
getSymbolDefinitionCode	src/SymbolPreviewView.ts	963;"	f
getSymbolDefinitions	src/SymbolPreviewView.ts	729;"	f
getSymbolInfo	src/CallHierarchyProvider.ts	384;"	m	class:CCallHierarchyProvider
getSymbolKey	src/CallTree.ts	51;"	m	class:CallTree
getSymbolKey	src/FunctionPointerMap.ts	74;"	m	class:FunctionPointerMap
getSymbolKind	src/CallHierarchyProvider.ts	709;"	f
getTree	src/CallTree.ts	133;"	m	class:CallTree
getWordRange	src/CallHierarchyProvider.ts	422;"	m	class:CCallHierarchyProvider
getWorkspaceRootPath	src/BuildDatabase.ts	32;"	f
gitShowCommand	src/ReviewCode.ts	234;"	C	function:reviewLastCommit
gitShowCommand	src/ReviewCode.ts	239;"	C	function:reviewLastGitCommit
gitShowCommand	src/ReviewCode.ts	244;"	C	function:reviewWholeGitDiff
globalVar	src/UtilFuns.ts	48;"	C
handleHoverAndClick	src/SymbolPreviewView.ts	1013;"	f
hasSymbol	src/CallTree.ts	26;"	m	class:CallNode
head	src/BrowserHistoryStore.ts	47;"	p	class:BrowserHistory
header	src/UtilFuns.ts	49;"	C
highlightLine	src/SymbolPreviewView.ts	958;"	C
history	src/BrowserHistoryStore.ts	45;"	p	class:BrowserHistory
historyEntries	src/BrowserHistoryView.ts	200;"	C
historyEntries	src/BrowserHistoryView.ts	634;"	C	function:registerBrowserHistoryView
historyEntries	src/FunctionPointerMap.ts	478;"	C	function:registerFunctionPointerMap
hoverRange	src/SymbolPreviewView.ts	1027;"	C	function:handleHoverAndClick
hoverRange	src/SymbolPreviewView.ts	1153;"	C	function:registerSymbolPreviewView
i	src/BrowserHistoryStore.ts	141;"	v
i	src/BrowserHistoryStore.ts	285;"	v
i	src/BrowserHistoryStore.ts	299;"	v
i	src/BrowserHistoryStore.ts	309;"	v
i	src/BrowserHistoryStore.ts	425;"	v
i	src/BrowserHistoryStore.ts	470;"	v
i	src/BrowserHistoryStore.ts	474;"	v
i	src/BrowserHistoryStore.ts	484;"	v
i	src/BrowserHistoryStore.ts	644;"	v
i	src/EditWindow.ts	212;"	v
i	src/EditWindow.ts	213;"	v
i	src/SymbolPreviewView.ts	895;"	v
i	src/SymbolPreviewView.ts	896;"	C
i	src/SymbolPreviewView.ts	927;"	v
i	src/SymbolPreviewView.ts	928;"	C
i	src/UtilFuns.ts	347;"	v
i	src/UtilFuns.ts	348;"	C
idx	src/BrowserHistoryStore.ts	290;"	C
idx	src/BrowserHistoryStore.ts	461;"	C
ifclass	src/UtilFuns.ts	50;"	C
ifdefDecorationType	src/EditWindow.ts	46;"	C	function:createIfdefDecorations
ifdefDecorationType	src/EditWindow.ts	508;"	C	function:registerEditWindowCommand
ifdefStack	src/EditWindow.ts	208;"	C
ifdefStack	src/EditWindow.ts	241;"	C
ifdefStack	src/EditWindow.ts	293;"	C
ifdefStack	src/EditWindow.ts	328;"	C
incomingCalls	src/ExportCallHierarchy.ts	65;"	C	function:exportCallHierarchy
indentation	src/CommentToggle.ts	117;"	C	function:toggleBlockComment
index	src/BrowserHistoryStore.ts	200;"	v
index	src/BrowserHistoryStore.ts	377;"	C
index	src/BrowserHistoryView.ts	349;"	v
indices	src/BrowserHistoryView.ts	441;"	C
initializeCscope	src/extension.ts	137;"	f
initializeReadtags	src/extension.ts	87;"	f
initializeSubscriptions	src/extension.ts	48;"	f
instance	src/UtilFuns.ts	51;"	C
interface	src/UtilFuns.ts	52;"	C
isCallHierarchy	src/ExportCallHierarchy.ts	24;"	C	function:exportCallHierarchy
isCallStack	src/BrowserHistoryStore.ts	158;"	C
isCallStack	src/BrowserHistoryStore.ts	38;"	p	interface:HistoryEntry
isCallStack	src/BrowserHistoryStore.ts	570;"	C
isCalling	src/BrowserHistoryStore.ts	159;"	C
isCalling	src/BrowserHistoryStore.ts	39;"	p	interface:HistoryEntry
isCalling	src/BrowserHistoryStore.ts	571;"	C
isComplexExpression	src/EditWindow.ts	225;"	C
isDirty	src/BrowserHistoryStore.ts	52;"	p	class:BrowserHistory
isDirty	src/FunctionPointerMap.ts	21;"	p	class:FunctionPointerMap
isParent	src/BrowserHistoryStore.ts	337;"	C
is_show_command	src/UtilFuns.ts	109;"	C	function:show_command
istips	src/SymbolPreviewView.ts	1181;"	C	function:registerSymbolPreviewView
item	src/BrowserHistoryView.ts	257;"	C
item	src/CollectCscopeFiles.ts	56;"	C	method:CscopeFilesCollector.collectSrc
item	src/FunctionPointerMap.ts	484;"	C	function:registerFunctionPointerMap
items	src/BrowserHistoryStore.ts	362;"	C
items	src/BrowserHistoryStore.ts	37;"	p	interface:HistoryEntry
items	src/BrowserHistoryStore.ts	397;"	C
items	src/BrowserHistoryStore.ts	483;"	C
items	src/BrowserHistoryStore.ts	569;"	C
items	src/BrowserHistoryView.ts	730;"	C	function:registerBrowserHistoryView
items	src/CollectCscopeFiles.ts	54;"	C	method:CscopeFilesCollector.collectSrc
items	src/SymbolPreviewView.ts	219;"	C	function:showModeSelectionMenu
itemsToAdd	src/BrowserHistoryStore.ts	216;"	C
joinedPath	src/BrowserHistoryView.ts	808;"	C	function:registerBrowserHistoryView
jsonData	src/BrowserHistoryView.ts	682;"	C	function:registerBrowserHistoryView
jsonData	src/FunctionPointerMap.ts	322;"	C	method:FunctionPointerMap.saveToFile
kernelOutputUri	src/CollectCscopeFiles.ts	386;"	C	function:showConfigDialog
key	src/FunctionPointerMap.ts	166;"	C	method:FunctionPointerMap.getDefinitionsBySymbol
key	src/FunctionPointerMap.ts	178;"	C	method:FunctionPointerMap.getCallersBySymbol
key	src/FunctionPointerMap.ts	192;"	C	method:FunctionPointerMap.delMapByCaller
key	src/FunctionPointerMap.ts	207;"	C	method:FunctionPointerMap.delCaller
key	src/FunctionPointerMap.ts	639;"	C	function:registerFunctionPointerMap
key	src/UtilFuns.ts	53;"	C
kind	src/UtilFuns.ts	152;"	C	function:definitionToSymbolInformation
language	src/SymbolPreviewView.ts	980;"	C	function:getSymbolDefinitionCode
languageId	src/SymbolPreviewView.ts	398;"	C
lastLoadedFile	src/MacroDefinition.ts	12;"	p	class:MacroDefinitionManager
lastOpenedDefinition	src/SymbolPreviewView.ts	13;"	v
lastSymbolEditor	src/SymbolPreviewView.ts	36;"	v
lastSymbolViewColumn	src/SymbolPreviewView.ts	37;"	v
last_name	src/BrowserHistoryStore.ts	51;"	p	class:BrowserHistory
leadingSpaces	src/ExportCallHierarchy.ts	194;"	C	function:formatCallHierarchyContent
library	src/UtilFuns.ts	54;"	C
limit	src/BrowserHistoryStore.ts	46;"	p	class:BrowserHistory
line	src/CallHierarchyProvider.ts	343;"	C	method:CCallHierarchyProvider.provideCallHierarchyOutgoingCalls
line	src/CollectCscopeFiles.ts	51;"	C	method:CscopeFilesCollector.collectSrc
line	src/CommentToggle.ts	44;"	C	function:toggleComment
line	src/EditWindow.ts	213;"	v
line	src/ExportCallHierarchy.ts	175;"	C	function:formatCallHierarchyContent
line	src/ExportCallHierarchy.ts	255;"	C	function:formatReferencesContent
line	src/MacroDefinition.ts	278;"	C	function:showCustomMacroDialog
line	src/MacroDefinition.ts	59;"	C	method:MacroDefinitionManager.parseMacroDefinitions
line	src/SymbolPreviewView.ts	896;"	C
line	src/SymbolPreviewView.ts	928;"	C
line	src/UtilFuns.ts	151;"	C	function:definitionToSymbolInformation
line	src/UtilFuns.ts	348;"	C
line	src/UtilFuns.ts	482;"	C	function:getFunctionCode
line	src/UtilFuns.ts	538;"	C	function:findStructVariableAtLine
lineEnd	src/SymbolPreviewView.ts	344;"	C
lineLink	src/ReviewCode.ts	448;"	C	function:reviewCurrentFunction
lineLink	src/ReviewCode.ts	79;"	C	function:reviewCurrentFile
lineNum	src/ExportCallHierarchy.ts	218;"	C	function:formatCallHierarchyContent
lineNum	src/ExportCallHierarchy.ts	275;"	C	function:formatReferencesContent
lineNum	src/RefProvider.ts	36;"	C
linePosition	src/CallHierarchyProvider.ts	96;"	p	class:SymbolInfo
lineRange	src/CommentToggle.ts	107;"	C	function:toggleBlockComment
lineRange	src/CommentToggle.ts	72;"	C	function:toggleSingleLineComment
lineStr	src/UtilFuns.ts	150;"	C	function:definitionToSymbolInformation
lineText	src/CommentToggle.ts	45;"	C	function:toggleComment
lines	src/CollectCscopeFiles.ts	49;"	C	method:CscopeFilesCollector.collectSrc
lines	src/EditWindow.ts	211;"	C
lines	src/ExportCallHierarchy.ts	154;"	C	function:formatCallHierarchyContent
lines	src/ExportCallHierarchy.ts	238;"	C	function:formatReferencesContent
lines	src/MacroDefinition.ts	276;"	C	function:showCustomMacroDialog
lines	src/MacroDefinition.ts	55;"	C	method:MacroDefinitionManager.parseMacroDefinitions
lines	src/SymbolPreviewView.ts	881;"	C
lines	src/UtilFuns.ts	327;"	C
lines	src/UtilFuns.ts	425;"	C	function:getFunctionNameFromFileAndLineUsingTags
link	src/FunctionPointerMap.ts	123;"	m	class:FunctionPointerMap
link	src/ReviewCode.ts	75;"	C	function:reviewCurrentFile
linksymbol	src/BrowserHistoryView.ts	294;"	v
linux_find_cmd	src/globalSettings.ts	23;"	C
list	src/BrowserHistoryStore.ts	468;"	C
list	src/RefProvider.ts	30;"	v
listCallers	src/FunctionPointerMap.ts	94;"	m	class:FunctionPointerMap
listDefs	src/FunctionPointerMap.ts	114;"	m	class:FunctionPointerMap
load	src/FunctionPointerMap.ts	421;"	m	class:FunctionPointerMap
loadCustomMacros	src/MacroDefinition.ts	607;"	f
loadFromFile	src/FunctionPointerMap.ts	336;"	m	class:FunctionPointerMap
loadFromFile	src/MacroDefinition.ts	18;"	m	class:MacroDefinitionManager
loadMacroFilePathFromWorkspace	src/MacroDefinition.ts	196;"	m	class:MacroDefinitionManager
loadSettings	src/SymbolPreviewView.ts	49;"	f
loadedData	src/BrowserHistoryView.ts	716;"	C	function:registerBrowserHistoryView
loc	src/RefProvider.ts	39;"	v
local	src/UtilFuns.ts	55;"	C
macro	src/UtilFuns.ts	86;"	C
macroCount	src/MacroDefinition.ts	37;"	C	method:MacroDefinitionManager.loadFromFile
macroFileUri	src/BuildDatabase.ts	466;"	C	function:showBuildDialog
macroFileUri	src/CollectCscopeFiles.ts	420;"	C	function:showConfigDialog
macroName	src/EditWindow.ts	102;"	C	function:updateIfdefDecorations
macroName	src/EditWindow.ts	110;"	C	function:updateIfdefDecorations
macroName	src/EditWindow.ts	170;"	C	function:updateIfdefDecorations
macroName	src/EditWindow.ts	285;"	C
macroName	src/EditWindow.ts	323;"	C
macroName	src/MacroDefinition.ts	286;"	C	function:showCustomMacroDialog
macroName	src/MacroDefinition.ts	72;"	C	method:MacroDefinitionManager.parseMacroDefinitions
macroPattern	src/EditWindow.ts	166;"	C	function:updateIfdefDecorations
macroRegex	src/EditWindow.ts	176;"	C	function:updateIfdefDecorations
macroTable	src/MacroDefinition.ts	11;"	p	class:MacroDefinitionManager
macroValue	src/EditWindow.ts	103;"	C	function:updateIfdefDecorations
macroValue	src/EditWindow.ts	111;"	C	function:updateIfdefDecorations
macroValue	src/EditWindow.ts	130;"	C	function:updateIfdefDecorations
macroValue	src/EditWindow.ts	137;"	C	function:updateIfdefDecorations
macroValue	src/EditWindow.ts	143;"	C	function:updateIfdefDecorations
macroValue	src/EditWindow.ts	173;"	C	function:updateIfdefDecorations
macroValue	src/EditWindow.ts	288;"	v
macroValue	src/EditWindow.ts	325;"	C
macroValue	src/MacroDefinition.ts	288;"	C	function:showCustomMacroDialog
macroValue	src/MacroDefinition.ts	74;"	C	method:MacroDefinitionManager.parseMacroDefinitions
makeCscopeFiles	src/CollectCscopeFiles.ts	74;"	m	class:CscopeFilesCollector
mappings	src/FunctionPointerMap.ts	632;"	C	function:registerFunctionPointerMap
mappings	src/FunctionPointerMap.ts	674;"	C	function:registerFunctionPointerMap
mappings	src/FunctionPointerMap.ts	732;"	C	function:registerFunctionPointerMap
mappings	src/FunctionPointerMap.ts	761;"	C	function:registerFunctionPointerMap
markCaller	src/FunctionPointerMap.ts	82;"	m	class:FunctionPointerMap
markDef	src/FunctionPointerMap.ts	102;"	m	class:FunctionPointerMap
match	src/EditWindow.ts	100;"	C	function:updateIfdefDecorations
match	src/EditWindow.ts	108;"	C	function:updateIfdefDecorations
match	src/EditWindow.ts	282;"	v
match	src/EditWindow.ts	318;"	v
match	src/UtilFuns.ts	553;"	C	function:findStructVariableAtLine
maxBuffer	src/ReadTags.ts	19;"	p	interface:ExecOptions
maxHistorySize	src/BrowserHistoryStore.ts	678;"	C	function:activate
maxHistorySize	src/extension.ts	193;"	C	function:activate
mdString	src/SymbolPreviewView.ts	1188;"	C	function:registerSymbolPreviewView
mdString	src/SymbolPreviewView.ts	990;"	C	function:getSymbolDefinitionCode
member	src/UtilFuns.ts	56;"	C
method	src/UtilFuns.ts	57;"	C
missingFile	src/CollectCscopeFiles.ts	143;"	C	method:CscopeFilesCollector.collectCscopeFiles
mode	src/BrowserHistoryView.ts	505;"	C	function:registerBrowserHistoryView
mode	src/BrowserHistoryView.ts	521;"	C	function:registerBrowserHistoryView
mode	src/SymbolPreviewView.ts	120;"	C	function:toggleSymbolOpenMode
module	src/UtilFuns.ts	58;"	C
myItem	src/BrowserHistoryStore.ts	321;"	C
myItem	src/BrowserHistoryStore.ts	340;"	C
myItem	src/BrowserHistoryView.ts	61;"	C
myRootItem	src/BrowserHistoryStore.ts	327;"	C
my_callHierarchyProvider	src/extension.ts	57;"	C	function:initializeSubscriptions
name	src/CallHierarchyProvider.ts	94;"	p	class:SymbolInfo
name	src/CollectCscopeFiles.ts	92;"	C	method:CscopeFilesCollector.makeCscopeFiles
namespace	src/UtilFuns.ts	59;"	C
net	src/UtilFuns.ts	60;"	C
nettype	src/UtilFuns.ts	61;"	C
newConfig	src/BrowserHistoryStore.ts	694;"	C	function:activate
newDescription	src/BrowserHistoryView.ts	365;"	C
newHistory	src/BrowserHistoryStore.ts	640;"	C
newMaxHistorySize	src/BrowserHistoryStore.ts	695;"	C	function:activate
newText	src/CommentToggle.ts	128;"	C	function:toggleBlockComment
newText	src/CommentToggle.ts	94;"	C	function:toggleSingleLineComment
nextLine	src/UtilFuns.ts	561;"	C	function:findStructVariableAtLine
node	src/CallTree.ts	158;"	C	method:CallTree.getPathToSymbol
node	src/CallTree.ts	182;"	C	method:CallTree.findParentSymbol
node	src/CallTree.ts	192;"	C	method:CallTree.findRootSymbol
node	src/CallTree.ts	60;"	C	method:CallTree.findNodeBySymbol
nodeMap	src/CallTree.ts	34;"	p	class:CallTree
nodes	src/CallTree.ts	58;"	C	method:CallTree.findNodeBySymbol
normalizedPath	src/CollectCscopeFiles.ts	77;"	C	method:CscopeFilesCollector.makeCscopeFiles
objPath	src/CollectCscopeFiles.ts	94;"	C	method:CscopeFilesCollector.makeCscopeFiles
objRoot	src/CollectCscopeFiles.ts	13;"	p	class:CscopeFilesCollector
oldIndex	src/BrowserHistoryStore.ts	645;"	C
onDidChangeDocumentSymbols	src/ReadTags.ts	36;"	p	class:ReadtagsProvider
openBraces	src/UtilFuns.ts	369;"	C
openRuleFile	src/ReviewCode.ts	334;"	f
openSymbolDefinitionInEditor	src/SymbolPreviewView.ts	777;"	f
openai	src/ReviewCode.ts	11;"	C	function:requestAI
openstr	src/SymbolPreviewView.ts	994;"	C	function:getSymbolDefinitionCode
originalEditor	src/SymbolPreviewView.ts	1023;"	C	function:handleHoverAndClick
originalEditor	src/SymbolPreviewView.ts	1033;"	C	function:handleHoverAndClick
originalEditor	src/SymbolPreviewView.ts	1158;"	C	function:registerSymbolPreviewView
os_constants	src/globalSettings.ts	28;"	C
os_platform	src/UtilFuns.ts	282;"	C	function:current_os
outgoingCalls	src/ExportCallHierarchy.ts	71;"	C	function:exportCallHierarchy
output	src/UtilFuns.ts	225;"	C	function:wrapExec
outputChannel	src/FunctionPointerMap.ts	623;"	C	function:registerFunctionPointerMap
outputChannel	src/UtilFuns.ts	205;"	C
outputFile	src/CollectCscopeFiles.ts	115;"	C	method:CscopeFilesCollector.makeCscopeFiles
package	src/UtilFuns.ts	62;"	C
panel	src/BuildDatabase.ts	257;"	C	function:showBuildDialog
panel	src/CollectCscopeFiles.ts	185;"	C	function:showConfigDialog
panel	src/MacroDefinition.ts	253;"	C	function:showCustomMacroDialog
parameter	src/UtilFuns.ts	63;"	C
parent	src/BrowserHistoryStore.ts	41;"	p	interface:HistoryEntry
parent	src/BrowserHistoryStore.ts	573;"	C
parent	src/CallTree.ts	8;"	p	class:CallNode
parentState	src/EditWindow.ts	241;"	C
parentState	src/EditWindow.ts	293;"	C
parentState	src/EditWindow.ts	328;"	C
parent_symbol	src/CallHierarchyProvider.ts	312;"	C	method:CCallHierarchyProvider.provideCallHierarchyIncomingCalls
parseInt	src/UtilFuns.ts	151;"	C	function:definitionToSymbolInformation
parseMacroDefinitions	src/MacroDefinition.ts	51;"	m	class:MacroDefinitionManager
parts	src/MacroDefinition.ts	282;"	C	function:showCustomMacroDialog
parts	src/MacroDefinition.ts	68;"	C	method:MacroDefinitionManager.parseMacroDefinitions
parts	src/UtilFuns.ts	428;"	C	function:getFunctionNameFromFileAndLineUsingTags
path	src/CallHierarchyProvider.ts	241;"	C	method:CCallHierarchyProvider.provideCallHierarchyIncomingCalls
path	src/CallHierarchyProvider.ts	606;"	C	function:findDefinition
path	src/CallTree.ts	163;"	C	method:CallTree.getPathToSymbol
path	src/RefProvider.ts	9;"	v
path	src/UtilFuns.ts	140;"	C	function:definitionToSymbolInformation
pattern	src/UtilFuns.ts	552;"	C	function:findStructVariableAtLine
port	src/UtilFuns.ts	64;"	C
position	src/EditWindow.ts	31;"	C	function:refreshDocumentSymbols
position	src/EditWindow.ts	613;"	C	function:registerEditWindowCommand
position	src/EditWindow.ts	637;"	C	function:registerEditWindowCommand
position	src/EditWindow.ts	730;"	C	function:registerEditWindowCommand
position	src/ExportCallHierarchy.ts	53;"	C	function:exportCallHierarchy
position	src/SymbolPreviewView.ts	1014;"	C	function:handleHoverAndClick
position	src/SymbolPreviewView.ts	1215;"	C	function:registerSymbolPreviewView
position	src/SymbolPreviewView.ts	1252;"	C	function:registerSymbolPreviewView
position	src/SymbolPreviewView.ts	343;"	C
prepareCallHierarchy	src/CallHierarchyProvider.ts	171;"	m	class:CCallHierarchyProvider
previewEnabled	src/SymbolPreviewView.ts	217;"	C	function:showModeSelectionMenu
procedure	src/UtilFuns.ts	66;"	C
processedLine	src/CollectCscopeFiles.ts	53;"	C	method:CscopeFilesCollector.collectSrc
program	src/UtilFuns.ts	65;"	C
progressInterval	src/BuildDatabase.ts	107;"	C	function:buildDatabase
progressInterval	src/BuildDatabase.ts	156;"	C	function:buildDatabase
promisify	src/SymbolPreviewView.ts	9;"	C
promisify	src/extension.ts	6;"	C
property	src/UtilFuns.ts	67;"	C
protected	src/UtilFuns.ts	68;"	C
provideCallHierarchyIncomingCalls	src/CallHierarchyProvider.ts	227;"	m	class:CCallHierarchyProvider
provideCallHierarchyOutgoingCalls	src/CallHierarchyProvider.ts	328;"	m	class:CCallHierarchyProvider
provideDefinition	src/ReadTags.ts	90;"	m	class:ReadtagsProvider
provideDocumentSymbols	src/ReadTags.ts	178;"	m	class:ReadtagsProvider
provideWorkspaceSymbols	src/ReadTags.ts	159;"	m	class:ReadtagsProvider
provider	src/extension.ts	114;"	C	function:initializeReadtags
quickPickItems	src/FunctionPointerMap.ts	511;"	C	function:registerFunctionPointerMap
quickPickItems	src/FunctionPointerMap.ts	588;"	C	function:registerFunctionPointerMap
range	src/BrowserHistoryView.ts	386;"	C
range	src/ReadTags.ts	54;"	C	method:ReadtagsProvider.getDefinition
range	src/SymbolPreviewView.ts	1017;"	C	function:handleHoverAndClick
range	src/SymbolPreviewView.ts	1145;"	C	function:registerSymbolPreviewView
range	src/SymbolPreviewView.ts	369;"	C
range	src/SymbolPreviewView.ts	732;"	C	function:getSymbolDefinitions
range	src/UtilFuns.ts	502;"	C	function:getFunctionCode
range	src/UtilFuns.ts	512;"	C	function:getFunctionCode
readtagsGoToDefinitionCommand	src/globalSettings.ts	11;"	C
readtagsGoToDefinitionCommandWin	src/ReadTags.ts	76;"	C	method:ReadtagsProvider.getDefinition
readtagsGoToDefinitionCommandWin	src/globalSettings.ts	12;"	C
readtagsGoToSymbolInWorkspaceCommand	src/globalSettings.ts	9;"	C
readtagsGoToSymbolInWorkspaceCommandWin	src/ReadTags.ts	163;"	C	method:ReadtagsProvider.provideWorkspaceSymbols
readtagsGoToSymbolInWorkspaceCommandWin	src/globalSettings.ts	10;"	C
readtagsProvider	src/EditWindow.ts	642;"	C	function:registerEditWindowCommand
readtagsProvider	src/SymbolPreviewView.ts	1059;"	C	function:registerSymbolPreviewView
references	src/ExportCallHierarchy.ts	119;"	C	function:exportCallHierarchy
references	src/RefProvider.ts	26;"	v
referencesContent	src/ExportCallHierarchy.ts	38;"	C	function:exportCallHierarchy
referencesViewEditors	src/ExportCallHierarchy.ts	13;"	C	function:exportCallHierarchy
referencesViewType	src/ExportCallHierarchy.ts	10;"	C	function:exportCallHierarchy
refresh	src/ReadTags.ts	43;"	m	class:ReadtagsProvider
refreshDocumentSymbols	src/EditWindow.ts	26;"	f
refreshOutlineByForceSave	src/extension.ts	311;"	f
regex	src/CallHierarchyProvider.ts	736;"	C	function:getSymbolKind
register	src/UtilFuns.ts	69;"	C
registerBrowserHistoryView	src/BrowserHistoryView.ts	484;"	f
registerCollectCscopeFilesCommand	src/CollectCscopeFiles.ts	602;"	f
registerCommentToggleCommand	src/CommentToggle.ts	140;"	f
registerCscopeMoreCommand	src/CscopeFind.ts	5;"	f
registerEditWindowCommand	src/EditWindow.ts	506;"	f
registerExportCallHierarchyCommand	src/ExportCallHierarchy.ts	288;"	f
registerFunctionPointerMap	src/FunctionPointerMap.ts	438;"	f
registerMacroDefinitionCommand	src/MacroDefinition.ts	631;"	f
registerReviewCodeCommand	src/ReviewCode.ts	488;"	f
registerSymbolPreviewView	src/SymbolPreviewView.ts	1054;"	f
relPath	src/SymbolPreviewView.ts	977;"	C	function:getSymbolDefinitionCode
relativePath	src/BuildDatabase.ts	520;"	C	function:showBuildDialog
relativePath	src/BuildDatabase.ts	538;"	C	function:showBuildDialog
relativePath	src/CollectCscopeFiles.ts	82;"	C	method:CscopeFilesCollector.makeCscopeFiles
relativePath	src/FunctionPointerMap.ts	456;"	C	function:registerFunctionPointerMap
relativePath	src/FunctionPointerMap.ts	560;"	C	function:registerFunctionPointerMap
relativePath	src/ReadTags.ts	196;"	C	method:ReadtagsProvider.provideDocumentSymbols
relativeSymbolInfos	src/BrowserHistoryStore.ts	168;"	v
relativeToAbsolute	src/UtilFuns.ts	174;"	f
removeMacro	src/MacroDefinition.ts	234;"	m	class:MacroDefinitionManager
requestAI	src/ReviewCode.ts	10;"	f
resolve_path	src/UtilFuns.ts	297;"	f
result	src/BrowserHistoryStore.ts	202;"	C
result	src/ReviewCode.ts	187;"	C	function:reviewCodeChanges
result	src/UtilFuns.ts	243;"	C	function:run_command
result	src/UtilFuns.ts	311;"	C	function:findFunctionNameByLine
result	src/UtilFuns.ts	316;"	C
results	src/CallHierarchyProvider.ts	646;"	C	function:findFunctionReferences
results	src/ReadTags.ts	162;"	C	method:ReadtagsProvider.provideWorkspaceSymbols
reviewCodeChanges	src/ReviewCode.ts	122;"	f
reviewCurrentFile	src/ReviewCode.ts	29;"	f
reviewCurrentFileWithCustomRule	src/ReviewCode.ts	280;"	f
reviewCurrentFileWithRule	src/ReviewCode.ts	275;"	f
reviewCurrentFunction	src/ReviewCode.ts	401;"	f
reviewDiffs	src/ReviewCode.ts	225;"	f
reviewLastCommit	src/ReviewCode.ts	233;"	f
reviewLastGitCommit	src/ReviewCode.ts	238;"	f
reviewOutputChannel	src/ReviewCode.ts	24;"	C
reviewWholeGitDiff	src/ReviewCode.ts	243;"	f
rightEditor	src/EditWindow.ts	665;"	C	function:registerEditWindowCommand
rightEditor	src/SymbolPreviewView.ts	1261;"	C	function:registerSymbolPreviewView
root	src/CallTree.ts	33;"	p	class:CallTree
rootItem	src/BrowserHistoryView.ts	222;"	C
rootItems	src/BrowserHistoryView.ts	211;"	C
rootItems	src/BrowserHistoryView.ts	435;"	C
root_parent	src/CallHierarchyProvider.ts	311;"	C	method:CCallHierarchyProvider.provideCallHierarchyIncomingCalls
rpath	src/CallHierarchyProvider.ts	547;"	C	function:findCallers
rpath	src/FunctionPointerMap.ts	457;"	C	function:registerFunctionPointerMap
rpath	src/FunctionPointerMap.ts	561;"	C	function:registerFunctionPointerMap
ruleFile	src/ReviewCode.ts	131;"	C	function:reviewCodeChanges
ruleFile	src/ReviewCode.ts	276;"	C	function:reviewCurrentFileWithRule
ruleFile	src/ReviewCode.ts	422;"	C	function:reviewCurrentFunction
ruleFilePath	src/ReviewCode.ts	337;"	C	function:openRuleFile
ruleFileUri	src/ReviewCode.ts	343;"	C	function:openRuleFile
ruleFiles	src/ReviewCode.ts	301;"	C	function:selectRuleFileFromExtension
rule_folder	src/ReviewCode.ts	297;"	C	function:selectRuleFileFromExtension
rules	src/ReviewCode.ts	72;"	C	function:reviewCurrentFile
rulesFileContent	src/ReviewCode.ts	134;"	C	function:reviewCodeChanges
rulesFileContent	src/ReviewCode.ts	425;"	C	function:reviewCurrentFunction
rulesFileContent	src/ReviewCode.ts	71;"	C	function:reviewCurrentFile
run_command	src/UtilFuns.ts	241;"	f
safeExpr	src/EditWindow.ts	196;"	C	function:updateIfdefDecorations
save	src/FunctionPointerMap.ts	409;"	m	class:FunctionPointerMap
saveListToFile	src/CollectCscopeFiles.ts	31;"	m	class:CscopeFilesCollector
saveMacroFilePathToWorkspace	src/MacroDefinition.ts	181;"	m	class:MacroDefinitionManager
saveResult	src/ReviewCode.ts	376;"	C	function:getCustomerRuleFile
saveSettings	src/SymbolPreviewView.ts	67;"	f
saveSymbolPreviewSettings	src/SymbolPreviewView.ts	1286;"	f
saveToFile	src/FunctionPointerMap.ts	241;"	m	class:FunctionPointerMap
savedCscopeFiles	src/CollectCscopeFiles.ts	212;"	C	function:showConfigDialog
savedKernelOutput	src/CollectCscopeFiles.ts	211;"	C	function:showConfigDialog
savedMacroFile	src/CollectCscopeFiles.ts	213;"	C	function:showConfigDialog
savedMacroPath	src/BuildDatabase.ts	272;"	C	function:showBuildDialog
scope	src/ReadTags.ts	165;"	C	method:ReadtagsProvider.provideWorkspaceSymbols
scope	src/ReadTags.ts	179;"	C	method:ReadtagsProvider.provideDocumentSymbols
scope	src/ReadTags.ts	56;"	C	method:ReadtagsProvider.getDefinition
scope	src/ReadTags.ts	97;"	C	method:ReadtagsProvider.provideDefinition
scriptPath	src/CollectCscopeFiles.ts	570;"	C	function:collectCscopeFiles
selectRuleFileFromExtension	src/ReviewCode.ts	288;"	f
selected	src/FunctionPointerMap.ts	518;"	C	function:registerFunctionPointerMap
selected	src/FunctionPointerMap.ts	595;"	C	function:registerFunctionPointerMap
selected	src/ReviewCode.ts	312;"	C	function:selectRuleFileFromExtension
selected	src/SymbolPreviewView.ts	267;"	C
selectedText	src/EditWindow.ts	603;"	C	function:registerEditWindowCommand
selectedText	src/FunctionPointerMap.ts	455;"	C	function:registerFunctionPointerMap
selectedText	src/FunctionPointerMap.ts	558;"	C	function:registerFunctionPointerMap
selection	src/CommentToggle.ts	24;"	C	function:toggleComment
selection	src/EditWindow.ts	601;"	C	function:registerEditWindowCommand
selection	src/FunctionPointerMap.ts	448;"	C	function:registerFunctionPointerMap
selection	src/FunctionPointerMap.ts	551;"	C	function:registerFunctionPointerMap
selectionRange	src/SymbolPreviewView.ts	831;"	C	function:openSymbolDefinitionInEditor
serializableHistory	src/BrowserHistoryView.ts	661;"	C	function:registerBrowserHistoryView
serializedData	src/BrowserHistoryStore.ts	494;"	C
serializedData	src/BrowserHistoryStore.ts	535;"	C
serializedData	src/FunctionPointerMap.ts	244;"	C	method:FunctionPointerMap.saveToFile
serializedData	src/FunctionPointerMap.ts	340;"	C	method:FunctionPointerMap.loadFromFile
setCSCOPE_PATH	src/CallHierarchyProvider.ts	69;"	f
setCTAGS_PATH	src/CallHierarchyProvider.ts	73;"	f
setFindAssignment	src/CallHierarchyProvider.ts	26;"	f
setFindGrep	src/CallHierarchyProvider.ts	30;"	f
setFindInclude	src/CallHierarchyProvider.ts	34;"	f
setHoverCallTrigger	src/SymbolPreviewView.ts	42;"	f
setNonActiveCodeAsGrey	src/EditWindow.ts	21;"	f
setREADTAGS_PATH	src/CallHierarchyProvider.ts	77;"	f
set_context	src/globalSettings.ts	68;"	f
shouldGrey	src/EditWindow.ts	233;"	v
shouldGrey	src/EditWindow.ts	287;"	v
shouldGrey	src/EditWindow.ts	326;"	v
shouldGrey	src/EditWindow.ts	374;"	v
shouldGrey	src/EditWindow.ts	432;"	v
showBuildDialog	src/BuildDatabase.ts	254;"	f
showConfigDialog	src/CollectCscopeFiles.ts	182;"	f
showCustomMacroDialog	src/MacroDefinition.ts	250;"	f
showMessageWindow	src/CallHierarchyProvider.ts	756;"	f
showModeSelectionMenu	src/SymbolPreviewView.ts	216;"	f
show_command	src/UtilFuns.ts	108;"	f
side	src/SymbolPreviewView.ts	1134;"	C	function:registerSymbolPreviewView
signal	src/UtilFuns.ts	71;"	C
singletonMethod	src/UtilFuns.ts	72;"	C
size	src/BrowserHistoryStore.ts	36;"	p	interface:HistoryEntry
size	src/BrowserHistoryStore.ts	49;"	p	class:BrowserHistory
sortedIndices	src/BrowserHistoryStore.ts	373;"	C
sourceIndex	src/BrowserHistoryStore.ts	388;"	C
sourceIndices	src/BrowserHistoryView.ts	455;"	C
srcPath	src/CollectCscopeFiles.ts	93;"	C	method:CscopeFilesCollector.makeCscopeFiles
srcRoot	src/CollectCscopeFiles.ts	12;"	p	class:CscopeFilesCollector
startAutoSave	src/FunctionPointerMap.ts	34;"	m	class:FunctionPointerMap
startLine	src/EditWindow.ts	385;"	v
startLine	src/SymbolPreviewView.ts	885;"	v
startLine	src/SymbolPreviewView.ts	958;"	C
startLine	src/SymbolPreviewView.ts	975;"	C	function:getSymbolDefinitionCode
startLine	src/UtilFuns.ts	333;"	v
startLine	src/UtilFuns.ts	533;"	C	function:findStructVariableAtLine
startPos	src/UtilFuns.ts	500;"	C	function:getFunctionCode
startPos	src/UtilFuns.ts	510;"	C	function:getFunctionCode
start_pos	src/RefProvider.ts	37;"	v
state	src/EditWindow.ts	397;"	C
state	src/EditWindow.ts	436;"	C
state	src/EditWindow.ts	470;"	C
stateInfo	src/EditWindow.ts	493;"	C
status	src/extension.ts	29;"	v
statusResult	src/ReviewCode.ts	150;"	C	function:reviewCodeChanges
stderr	src/UtilFuns.ts	209;"	p	interface:ExecResult
stderr	src/globalSettings.ts	43;"	p	interface:cmd_result
stdout	src/UtilFuns.ts	208;"	p	interface:ExecResult
stdout	src/UtilFuns.ts	224;"	C	function:wrapExec
stdout	src/globalSettings.ts	41;"	p	interface:cmd_result
stopAutoSave	src/FunctionPointerMap.ts	55;"	m	class:FunctionPointerMap
storageUri	src/BrowserHistoryStore.ts	50;"	p	class:BrowserHistory
storageUri	src/FunctionPointerMap.ts	23;"	p	class:FunctionPointerMap
struct	src/UtilFuns.ts	73;"	C
structPatterns	src/UtilFuns.ts	541;"	C	function:findStructVariableAtLine
submethod	src/UtilFuns.ts	74;"	C
subprogram	src/UtilFuns.ts	75;"	C
subroutine	src/UtilFuns.ts	76;"	C
subroutineDeclaration	src/UtilFuns.ts	77;"	C
subtype	src/UtilFuns.ts	78;"	C
success	src/globalSettings.ts	37;"	p	interface:cmd_result
symbol	src/CallTree.ts	6;"	p	class:CallNode
symbol	src/ReadTags.ts	55;"	C	method:ReadtagsProvider.getDefinition
symbol	src/RefProvider.ts	21;"	C
symbol	src/SymbolPreviewView.ts	374;"	C
symbol	src/UtilFuns.ts	140;"	C	function:definitionToSymbolInformation
symbolInfo	src/CallHierarchyProvider.ts	511;"	C	function:findFiles
symbolInfo	src/CallHierarchyProvider.ts	605;"	C	function:findDefinition
symbolInfo	src/ReadTags.ts	63;"	C	method:ReadtagsProvider.getDefinition
symbolInfos	src/EditWindow.ts	645;"	C	function:registerEditWindowCommand
symbolInfos	src/ReadTags.ts	105;"	C	method:ReadtagsProvider.provideDefinition
symbolInfos	src/SymbolPreviewView.ts	1174;"	C	function:registerSymbolPreviewView
symbolInfos	src/SymbolPreviewView.ts	1218;"	C	function:registerSymbolPreviewView
symbolInfos	src/SymbolPreviewView.ts	378;"	C
symbolItem	src/SymbolPreviewView.ts	844;"	C	function:openSymbolDefinitionInEditor
symbolModeStatusBarItem	src/SymbolPreviewView.ts	40;"	v
symbolRange	src/CallHierarchyProvider.ts	155;"	p	interface:SymbolInfoResult
symbols	src/CallHierarchyProvider.ts	38;"	C
symbols	src/CallTree.ts	139;"	C	method:CallTree.getAllSymbols
tag	src/UtilFuns.ts	201;"	C	function:findField
tail	src/BrowserHistoryStore.ts	48;"	p	class:BrowserHistory
targetEntry	src/BrowserHistoryStore.ts	395;"	C
targetIndex	src/BrowserHistoryView.ts	461;"	v
task	src/UtilFuns.ts	79;"	C
task	src/extension.ts	98;"	C	function:initializeReadtags
tempText	src/EditWindow.ts	32;"	C	function:refreshDocumentSymbols
text	src/EditWindow.ts	74;"	C	function:updateIfdefDecorations
text	src/SymbolPreviewView.ts	880;"	C
text	src/UtilFuns.ts	321;"	C
titleLine	src/ExportCallHierarchy.ts	158;"	C	function:formatCallHierarchyContent
titleLine	src/ExportCallHierarchy.ts	242;"	C	function:formatReferencesContent
toString	src/CallHierarchyProvider.ts	106;"	p	class:SymbolInfo
toggleBlockComment	src/CommentToggle.ts	106;"	f
toggleCallHierarchyAddHistory	src/SymbolPreviewView.ts	181;"	f
toggleCheckSymbolKind	src/SymbolPreviewView.ts	190;"	f
toggleComment	src/CommentToggle.ts	16;"	f
toggleFnStructOnly	src/SymbolPreviewView.ts	172;"	f
toggleHoverAddHistory	src/SymbolPreviewView.ts	154;"	f
toggleHoverCallerClickTrigger	src/SymbolPreviewView.ts	208;"	f
toggleHoverCallerEnabled	src/SymbolPreviewView.ts	199;"	f
toggleHoverPreviewMode	src/SymbolPreviewView.ts	145;"	f
toggleOpenRightWindow	src/SymbolPreviewView.ts	163;"	f
togglePreviewMode	src/SymbolPreviewView.ts	128;"	f
toggleSingleLineComment	src/CommentToggle.ts	71;"	f
toggleSymbolOpenMode	src/SymbolPreviewView.ts	107;"	f
tooltipItems	src/SymbolPreviewView.ts	89;"	C	function:updateStatusBar
trait	src/UtilFuns.ts	80;"	C
transferItem	src/BrowserHistoryView.ts	450;"	C
traverseTree	src/CallTree.ts	149;"	m	class:CallTree
treeView	src/BrowserHistoryView.ts	489;"	C	function:registerBrowserHistoryView
trimmedText	src/CommentToggle.ts	46;"	C	function:toggleComment
type	src/UtilFuns.ts	81;"	C
typedef	src/UtilFuns.ts	82;"	C
union	src/UtilFuns.ts	83;"	C
updateIfdefDecorations	src/EditWindow.ts	55;"	f
updateStatus	src/extension.ts	35;"	f
updateStatusBar	src/SymbolPreviewView.ts	83;"	f
update_option	src/CallHierarchyProvider.ts	134;"	C	function:getCscopeCommand
updown	src/BrowserHistoryView.ts	55;"	C
uri	src/BrowserHistoryView.ts	385;"	C
uri	src/EditWindow.ts	744;"	C	function:registerEditWindowCommand
uri	src/ReadTags.ts	148;"	C	method:ReadtagsProvider.provideDefinition
uri	src/ReadTags.ts	61;"	C	method:ReadtagsProvider.getDefinition
uri	src/SymbolPreviewView.ts	1213;"	C	function:registerSymbolPreviewView
uri	src/SymbolPreviewView.ts	1251;"	C	function:registerSymbolPreviewView
useInternalExecutable	src/BuildDatabase.ts	18;"	C	function:getCommandPath
value	src/FunctionPointerMap.ts	166;"	C	method:FunctionPointerMap.getDefinitionsBySymbol
value	src/FunctionPointerMap.ts	178;"	C	method:FunctionPointerMap.getCallersBySymbol
value	src/FunctionPointerMap.ts	639;"	C	function:registerFunctionPointerMap
value	src/MacroDefinition.ts	120;"	C	method:MacroDefinitionManager.getMacroNumericValue
value	src/MacroDefinition.ts	98;"	C	method:MacroDefinitionManager.getMacroValue
var	src/UtilFuns.ts	84;"	C
variable	src/UtilFuns.ts	85;"	C
visibleEditors	src/SymbolPreviewView.ts	1121;"	C	function:registerSymbolPreviewView
walkDirectory	src/CollectCscopeFiles.ts	156;"	m	class:CscopeFilesCollector
windows_find_cmd	src/globalSettings.ts	24;"	C
word	src/EditWindow.ts	616;"	C	function:registerEditWindowCommand
word	src/ReadTags.ts	123;"	C	method:ReadtagsProvider.provideDefinition
word	src/SymbolPreviewView.ts	1147;"	C	function:registerSymbolPreviewView
word	src/SymbolPreviewView.ts	761;"	C	function:getSymbolDefinitions
wordRange	src/EditWindow.ts	614;"	C	function:registerEditWindowCommand
workspaceFolder	src/FunctionPointerMap.ts	411;"	C	method:FunctionPointerMap.save
workspaceFolder	src/FunctionPointerMap.ts	423;"	C	method:FunctionPointerMap.load
workspaceFolder	src/extension.ts	146;"	C	function:initializeCscope
workspacePath	src/BuildDatabase.ts	148;"	C	function:buildDatabase
workspacePath	src/BuildDatabase.ts	187;"	C	function:generate_cscopefiles
workspacePath	src/BuildDatabase.ts	269;"	C	function:showBuildDialog
workspacePath	src/BuildDatabase.ts	98;"	C	function:buildDatabase
workspacePath	src/CollectCscopeFiles.ts	196;"	C	function:showConfigDialog
workspacePath	src/CollectCscopeFiles.ts	524;"	C	function:collectCscopeFiles
workspacePath	src/CollectCscopeFiles.ts	554;"	C	function:collectCscopeFiles
workspacePath	src/MacroDefinition.ts	264;"	C	function:showCustomMacroDialog
workspacePath	src/MacroDefinition.ts	612;"	C	function:loadCustomMacros
workspacePath	src/ReadTags.ts	187;"	C	method:ReadtagsProvider.provideDocumentSymbols
workspaceRoot	src/BrowserHistoryView.ts	389;"	C
workspace_path	src/UtilFuns.ts	608;"	C	function:getFullPath
wrapExec	src/UtilFuns.ts	214;"	f
wrapExec	src/extension.ts	10;"	C
zeroBasedLine	src/UtilFuns.ts	469;"	C	function:getFunctionCode
