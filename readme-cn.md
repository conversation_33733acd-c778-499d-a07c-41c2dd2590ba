# SourceSeek 使用说明

SourceSeek 是一个用于浏览 C/C++ 源代码的工具，它使用 cscope/ctags/readtags 创建的数据库。
它利用 VSCode 内置的"转到定义"、"转到引用"、"查找所有引用"、"显示调用层次结构"等接口来导航代码。

它还提供了大纲视图来显示当前文件的代码结构。
它有浏览器历史窗口，显示当前项目的符号和调系浏览历史记录。
它提供了一个预览窗口，当鼠标悬停在函数名上时显示函数代码宏定义等

它可以替代对于大型项目（如 Linux 内核）速度较慢的 VSCode C/C++ 扩展。
它使用 cscope/ctags 创建的数据库，因此不需要像clangd扩展那样编译代码。

## 功能

### 构建数据库

当您打开目录/工作区时，cscope/ctags 数据库将在首次搜索时自动构建。您也可以通过命令行提前生成它：

1. cscope -Rcbqk 或先创建 cscope.files 文件，然后执行 cscope -cbqk
2. ctags --fields=+i -Rno tags

### 大纲窗口

显示文件中的函数结构，可以切换为仅显示函数

![大纲窗口](png/sourceseek-outline.PNG)

### 跳转到定义(F12)

在上下文菜单中选择转到定义或按 F12 或 Ctrl+单击符号
跳转到定义，如果有多个定义，会显示内联窗口

![SourceSeek 定义](png/sourceseek-definition.PNG)

### 转到引用(Shift + F12)

跳转到引用，如果有多个引用，会显示内联窗口
![转到引用](png/sourceseek-reference.png)

### 调用层次结构窗口(Shift+Alt+H)

显示当前函数的调用层次结构

![调用层次结构](png/sourceseek-hierarchy.PNG)

### 所有引用窗口(Shift+Alt+F12)

显示当前符号的所有引用

![所有引用](png/sourceseek-allref.png)

### 预览窗口

当鼠标悬停在符号上时，在右侧窗口显示定义
通过更改设置，也可以在鼠标指针旁边的弹出窗口中显示代码

![预览窗口](png/sourceseek-preview.png)

也可以设置成悬浮窗口预览，除了函数，结构，宏定义等默认通过悬浮窗口预览，函数在右边编辑窗口预览，可以在状态栏菜单中设置函数也用悬浮窗口预览。

![预览窗口](png/sourceseek-preview-tips.png)

### 全局搜索

在顶部栏中输入 # 后跟符号前缀，以在整个工作区中搜索符号

![全局搜索](png/sourceseek-search.png)

### 文本搜索

在编辑窗口中双击一个单词，然后右键单击弹出上下文菜单并选择搜索。

![文本搜索](png/sourceseek-search-word.png)
![文本搜索](png/sourceseek-search-window.png)

### 浏览历史记录窗口

历史窗口记录了定义跳转、调用层次结构和引用搜索的符号历史。它会自动保存在工作区目录中，并在重新打开时加载历史记录。
您还可以使用按钮将其保存到文件和从文件加载。
您可以筛选仅显示函数。
当您将鼠标悬停在历史记录中的符号上时，工具提示会显示符号的完整文件路径和行号，方便快速了解符号位置。

![历史窗口](png/sourceseek-history.png)

对于符号定义，如果一个符号有多个定义，它们将显示在子节点中。

![多个定义](png/sourceseek-multipledef.png)

对于符号调用链，CALLERS OF 窗口逐层显示调用链，它在历史记录中作为子节点记录。调用链可以复制到剪贴板。

![历史筛选](png/sourceseek-his-call.png)
![历史调用链](png/sourceseek-call-exam.png)

您可以将调用链复制到剪贴板并粘贴到其他地方。

![历史筛选](png/sourceseek-clipboard.png)
![历史筛选](png/sourceseek-paste.png)

当您转到一个函数，然后继续转到子函数，以此类推，历史记录将记录调用堆栈。

您可以右键单击历史记录中的任何符号，并选择"在右侧窗口中打开"，这将在右侧编辑器组中打开文件并定位到该符号的定义处。这对于在保持当前文件打开的同时查看相关代码非常有用。

### 手动将函数指针映射到定义

对于某些函数指针调用或多层调用，您可能希望跳过中间调用过程，直接调用关键函数。您可以手动建立函数调用链接。

在定义中单击函数名符号，弹出菜单（链接调用者/标记定义），单击它，弹出菜单，您可以选择调用者，或取消菜单仅标记定义，以便在链接定义中选择。

![历史筛选](png/sourceseek-fnmap-menu.png)

在定义中单击函数名符号，弹出菜单（链接定义/标记调用者），单击它，弹出菜单，您可以选择函数名，或取消菜单标记调用者，以便在链接调用者中选择。

![历史筛选](png/sourceseek-fnmap-menu2.png)

菜单还将显示历史记录中的函数名和对话框中标记的函数供选择。

![历史筛选](png/sourceseek-fnmap-popup.png)

您可以右键单击菜单显示已建立的调用关系

![历史筛选](png/sourceseek-fnmap-menuprint.png)

调用关系将显示在输出窗口中

![历史筛选](png/sourceseek-fnmap-menuprint-output.png)

### 代码变更审查

您可以在编辑器中右键单击，选择"Review Changes"（审查变更）来查看当前文件的 Git 差异。这将在输出窗口中显示 git diff 命令的结果，帮助您审查尚未提交的更改。

此外，您还可以选择"Review Last Commit"（审查最后提交）来查看最近一次提交的详细信息。这将在输出窗口中显示 git show 命令的结果，包括提交的元数据、提交消息和更改的内容。

这些功能对于在提交代码前进行自我审查和回顾先前的提交非常有用，可以帮助您发现潜在问题或不必要的更改。

### 状态栏快速设置

单击状态栏中的 SourceSeek 显示几个开关

![状态栏](png/sourceseek-statusbar.png)

用于开启/关闭一些设置的菜单

![快速设置](png/sourceseek-statusmenu.png)

1. 启用鼠标悬停预览
2. 使用同一个标签页打开预览文件或为新文件打开新标签页
3. 是否将悬停的符号添加到历史记录
4. 是否在右侧窗口或悬停提示中预览
5. 是否显示 cscope/ctags/readtags 命令行。

### 命令

扩展中预安装了 Windows 和 Linux 版本的 cscope/ctags/readtags 命令。
您可以配置使用系统提供的命令，需要在系统环境中设置搜索路径或在设置中指定命令的路径。

![命令设置](png/sourceseek-setting1.png)
![路径设置](png/sourceseek-setting2.png)

## 要求

它依赖于 cscope/ctags/readtags 工具。

## 扩展设置

此扩展提供以下设置：

* `sourceseek.enableSourceSeek`: 启用/禁用此扩展。
* `sourceseek.enableReferenceSearch`: 启用/禁用引用搜索。
* `sourceseek.enableDefinitionSearch`: 启用/禁用定义搜索。
* `sourceseek.enableCallHierarchy`: 启用/禁用调用层次结构。
* `sourceseek.enableWorkspaceSymbol`: 启用/禁用工作区符号搜索。
* `sourceseek.enableDocumentSymbol`: 启用/禁用文档符号搜索。
* `sourceseek.HoverPreviewEnabled`: 启用/禁用悬停预览。
* `sourceseek.openRightWindow`: 启用/禁用在右侧窗口中打开预览。
* `sourceseek.openInNewWindow`: 启用/禁用在新窗口中打开预览。
* `sourceseek.hoverAddHistory`: 启用/禁用将悬停符号添加到历史记录。
* `sourceseek.useInternalExecutable`: 启用/禁用使用内部可执行文件。
* `sourceseek.executablePath`: 设置 cscope/ctags/readtags 的路径。
* `sourceseek.showCommand`: 启用/禁用显示 cscope/ctags/readtags 命令行。
* `sourceseek.databasePath`: 设置 cscope/ctags/readtags 数据库的路径。
* `sourceseek.cscope_database`: 设置 cscope 数据库的名称。
* `sourceseek.ctags_database`: 设置 ctags 数据库的名称。
* `sourceseek.excludedPaths`: 设置 cscope/ctags/readtags 的排除路径。

## 已知问题

 带提示的鼠标悬停预览显示良好

## 发布说明

### 1.4.0
1.support custom macro add/delete dialog

### 1.3.0
1.support load a macro definition file and show non-active code as grey color.
2.support toggle enable/disable show non-active code as grey 

### 1.2.0
1. support generate linux kernel cscope.files depend on .cmd file
2. add -m <macro file> support for ctags and cscope,(only linux)
 for linux kernel, the file kernel/include/generated/autoconf.h include the macros, so
 when creating ctags and cscope database, the code that depend on macro definition will not be included.
 e.g.
 #define CONFIG_FEATURE 1
 #ifdef CONFIG_FEATURE

### 1.1.0
1. review功能
### 1.0.0
1. This is no big changes compare with 0.9.5, just a message. So we got a 1.0 version.


### 0.9.5

1. add link in hover window to open in right window
2. bookmark use relative path
### 0.9.4

1. show path in history item tips
2. use function+line for bookmark

### 0.9.3

1. Add F2 key for bookmark
2. mark caller support without in function

### 0.9.2

1. Add open in right window menu for history item
2. Add open in right window menu for edit window
3. Add file path in hover window


### 0.9.1

1. 对于转到定义，过滤函数、结构体、宏，并将所有内容添加到历史记录，函数除外
2. 修复悬停提示显示代码格式问题
3. 对于结构体和微观，在悬停提示中显示。

### 0.9.0

1. 添加调用堆栈历史功能
2. 如果已在历史记录中，则将符号移到最新位置
3. 识别多行函数定义
4. 支持历史缓冲区大小设置
5. 在调用层次结构的根项中显示调用/被调用数量
6. 从最后 30 行中识别结构体变量
7. 支持删除整个树
8. 将调用和被调用的内容导出到剪贴板
9. 支持历史符号的拖放
10. 修复历史符号的删除问题

### 0.8.0

1. 在调用层次结构中添加对结构体变量引用函数的支持
2. 如果没有调用函数，则返回被结构体变量引用的函数
3. 支持悬停在提示中显示函数代码。
4. 在工作区符号搜索中添加仅函数-结构体的选项
5. 引用不返回 EXPORT_SYMBOL 和声明
6. 修复加载/保存历史顺序问题
7. 修复使用同一标签页显示预览文件的问题

### 0.7.0

1. 添加对手动设置函数指针到定义映射的支持
2. 添加对符号历史的描述编辑支持
3. 添加对手动映射的调用层次结构和转到定义的支持
4. 将符号历史路径更改为相对于工作区
5. 修复无符号历史文件注册失败的问题
6. 修复固定顺序保存历史的问题
7. 修复删除一个历史项目的问题

### 0.6.1

1. 添加自动构建选项
2. 当找不到 ctags 数据库用于转到定义时尝试 cscope

### 0.6

1. 添加符号搜索上下文菜单
2. 添加一些按钮

### 0.5.2

1. 修复 Linux 调用层次结构问题

### 0.5.1

1. 修复构建问题

### 0.5.0

1. 添加执行路径的设置
2. 为功能添加启用/禁用选项
3. 为历史窗口添加保存/加载按钮
4. 为历史窗口添加清除按钮
5. 添加 cscope/ctags/readtags 命令行的设置。

### 0.4.0

1. 添加预览窗口的设置。

### 0.3.0

1. 添加函数定义的预览窗口。

### 0.2.0

1. 添加浏览器历史的历史窗口。

### 0.1.0

1. SourceSeek 的初始版本。
2. 支持 cscope 和 ctags/readtags 用于 C/C++ 代码搜索和导航。
3. 为代码结构添加大纲视图功能开关。

---

## 遵循扩展指南

确保您已阅读扩展指南并遵循创建扩展的最佳实践。

* [扩展指南](https://code.visualstudio.com/api/references/extension-guidelines)
